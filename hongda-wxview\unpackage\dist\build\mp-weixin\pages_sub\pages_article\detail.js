"use strict";const e=require("../../common/vendor.js"),t=require("../../api/content/article.js"),a=require("./api/content/comment.js"),n=require("./api/common/mpHtmlStyles.js"),o=require("../../utils/config.js"),r=require("../../utils/image.js"),s=require("../../utils/date.js"),l=require("../../utils/tools.js");if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-popup"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-icon/u-icon.js")+u+i+(()=>"../../uni_modules/uview-plus/components/u-popup/u-popup.js"))();const u=()=>"../../uni_modules/mp-html/components/mp-html/mp-html.js",i=()=>"../../components/common/CommentItem.js",c={__name:"detail",setup(u){const i=e.index.upx2px(20),c=e.ref(0),m=e.ref(0),g=e.ref(0),v=e.ref(!1),p=e.ref(!1),d=e.ref(e.index.getStorageSync("staticAssets")||{}),h=e.computed((()=>d.value.icon_article_comment_input||"")),y=e.computed((()=>{const e=d.value.bg_article_summary_card;return e?{backgroundImage:`url('${e}')`}:{}})),f=()=>{try{if(!e.index.getStorageSync("token")){const a=(()=>{try{const e=getCurrentPages(),t=e[e.length-1],a="/"+t.route,n=t.options||{},o=Object.keys(n).map((e=>`${encodeURIComponent(e)}=${encodeURIComponent(n[e])}`)).join("&");return o?`${a}?${o}`:a}catch(e){return"/pages_sub/pages_article/detail"}})();try{e.index.setStorageSync("loginBackPage",a)}catch(t){}return e.index.navigateTo({url:"/pages_sub/pages_other/login"}),!1}return!0}catch(t){return e.index.navigateTo({url:"/pages_sub/pages_other/login"}),!1}},x=()=>{f()&&(C.content="",v.value=!0)},b=()=>{v.value=!1},w=()=>{getCurrentPages().length>1?e.index.navigateBack({delta:1}):e.index.switchTab({url:"/pages/article/index"})};e.onBackPress((()=>!(getCurrentPages().length>1)&&(e.index.switchTab({url:"/pages/article/index"}),!0)));const _=e.ref(null),T=e.ref(null),S=e.ref([]),j=e.ref(0),C=e.reactive({content:"",isSubmitting:!1}),I=e.ref(!1),A=e.computed((()=>o.config.imageBaseUrl?o.config.imageBaseUrl.replace(/\/$/,""):"")),k=async()=>{if(T.value){I.value=!0;try{const e=await a.getCommentList({relatedId:T.value,relatedType:"article"});200===e.code&&e.data?(S.value=e.data.comments||[],j.value=e.data.total||0):(S.value=[],j.value=0)}catch(e){S.value=[],j.value=0}finally{I.value=!1}}},q=async()=>{if(!f())return;await(async({content:t,stateObject:n})=>{if(t.trim()&&!n.isSubmitting){n.isSubmitting=!0;try{const o=await a.addComment({relatedId:T.value,relatedType:"article",content:t.trim()});return 200===o.code?(e.index.showToast({title:"发布成功",icon:"success"}),await k(),!0):(e.index.showToast({title:o.msg||"评论失败",icon:"error"}),!1)}catch(o){return!1}finally{n.isSubmitting=!1}}})({content:C.content,stateObject:C})&&(C.content="",b())},B=async()=>{T.value?(await(async a=>{p.value=!1;try{const o=await t.getArticleDetail(a);if(200===o.code&&o.data){const e=o.data;e.content=l.formatRichText(e.content);let t=[];if(e.tags&&"string"==typeof e.tags)try{t=JSON.parse(e.tags)}catch(n){console.error("标签解析失败:",n)}else Array.isArray(e.tags)&&(t=e.tags);Array.isArray(t)||(t=[]),_.value={...e,parsedTags:t}}else e.index.showToast({title:o.msg||"获取文章失败",icon:"none"}),p.value=!0}catch(o){console.error("获取文章失败:",o),e.index.showToast({title:"网络请求失败",icon:"error"}),p.value=!0}})(T.value),_.value&&(await e.nextTick$1(),(()=>{try{const t=e.index.getMenuButtonBoundingClientRect();c.value=t.top,m.value=t.height,g.value=t.bottom+i}catch(t){const a=e.index.getSystemInfoSync();c.value=a.statusBarHeight||20,m.value=44,g.value=c.value+m.value+i}})(),await k())):p.value=!0};return e.onLoad((t=>{console.log("文章详情页 onLoad - 页面首次加载"),e.index.showShareMenu({menus:["shareAppMessage","shareTimeline"]}),T.value=t.id,B()})),e.onShareAppMessage((()=>({}))),(t,a)=>e.e({a:_.value},_.value?e.e({b:c.value+"px",c:e.p({name:"arrow-left",color:"#000000",size:"22"}),d:e.o(w),e:m.value+"px",f:e.unref(i)+"px",g:g.value+"px",h:e.unref(r.getFullImageUrl)(_.value.coverImageUrl),i:e.t(_.value.title),j:e.t(_.value.source),k:e.t(e.unref(s.formatDate)(_.value.publishTime,"YYYY-MM-DD")),l:e.t(_.value.viewCount),m:_.value.summary},_.value.summary?{n:e.t(_.value.summary),o:e.s(y.value)}:{},{p:e.p({content:_.value.content,domain:A.value,"tag-style":e.unref(n.tagStyle),"preview-img":!0,"lazy-load":!0}),q:_.value.parsedTags&&_.value.parsedTags.length>0},_.value.parsedTags&&_.value.parsedTags.length>0?{r:e.f(_.value.parsedTags,((t,a,n)=>({a:e.t(t.name||t),b:e.t(a<_.value.parsedTags.length-1?" / ":""),c:t.id||t.name})))}:{},{s:e.t(j.value),t:h.value,v:e.o(x),w:S.value.length>0},S.value.length>0?{x:e.f(S.value,((t,a,n)=>({a:t.id,b:"9537b0b7-2-"+n,c:e.p({comment:t})})))}:{},{y:g.value+"px",z:e.o(b),A:C.content,B:e.o((e=>C.content=e.detail.value)),C:e.t(C.content.length),D:e.t(C.isSubmitting?"发布中...":"发布"),E:C.content.trim()?1:"",F:!C.content.trim()||C.isSubmitting,G:e.o(q),H:e.o(b),I:e.p({show:v.value,mode:"bottom",round:"20","safe-area-inset-bottom":!0})}):{})}},m=e._export_sfc(c,[["__scopeId","data-v-9537b0b7"]]);c.__runtimeHooks=2,wx.createPage(m);
