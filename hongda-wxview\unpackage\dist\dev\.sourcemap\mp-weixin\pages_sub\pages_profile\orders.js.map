{"version": 3, "file": "orders.js", "sources": ["pages_sub/pages_profile/orders.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX3Byb2ZpbGVcb3JkZXJzLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"orders-page\">\r\n    <!-- 顶部导航栏 -->\r\n    <up-navbar \r\n      title=\"报名订单\" \r\n        :autoBack=\"true\" \r\n        :fixed=\"true\" \r\n        :safeAreaInsetTop=\"true\" \r\n        :placeholder=\"true\"\r\n        bgColor=\"#ffffff\" \r\n        leftIcon=\"arrow-left\" \r\n        leftIconColor=\"#333333\"\r\n        titleStyle=\"font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30'; font-weight: normal; font-size: 32rpx; color: #000000; line-height: 44rpx; text-align: center; font-style: normal; text-transform: none;\"\r\n    />\r\n    \r\n    <!-- 主内容区域 -->\r\n    <scroll-view \r\n      scroll-y \r\n      class=\"orders-list-scroll\" \r\n      @scrolltolower=\"onLoadMore\"\r\n      refresher-enabled \r\n      :refresher-triggered=\"isRefreshing\" \r\n      @refresherrefresh=\"onRefresh\"\r\n    >\r\n      <!-- 加载状态 -->\r\n      <view v-if=\"loading && registrationList.length === 0\" class=\"loading-state\">\r\n        <up-loading-page :loading=\"true\" loading-text=\"加载中...\" />\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view v-else-if=\"!loading && registrationList.length === 0\" class=\"empty-state\">\r\n        <up-empty \r\n          mode=\"list\" \r\n          :text=\"isLoggedIn ? '暂无报名记录' : '请先登录查看报名记录'\" \r\n          textColor=\"#909399\" \r\n          iconSize=\"120\"\r\n        >\r\n          <template #bottom v-if=\"!isLoggedIn\">\r\n            <up-button type=\"primary\" text=\"去登录\" @click=\"goToLogin\" />\r\n          </template>\r\n        </up-empty>\r\n      </view>\r\n      \r\n      <!-- 报名订单列表 -->\r\n      <view v-else class=\"orders-list\">\r\n        <view\r\n          v-for=\"item in registrationList\"\r\n          :key=\"item.id\"\r\n          class=\"order-card\"\r\n          @click=\"goToEventDetail(item.eventId)\"\r\n        >\r\n          <!-- 左侧：活动封面 -->\r\n          <view class=\"card-left\">\r\n            <image\r\n              :src=\"item.coverImageUrl\"\r\n              mode=\"aspectFill\"\r\n              class=\"event-image\"\r\n              :lazy-load=\"true\"\r\n              @error=\"onImageError\"\r\n              @load=\"onImageLoad\"\r\n            />\r\n          </view>\r\n\r\n          <!-- 右侧：活动信息 -->\r\n          <view class=\"card-right\">\r\n            <!-- 活动标题 -->\r\n            <text class=\"event-title\">{{ item.title || '活动标题' }}</text>\r\n\r\n            <!-- 报名时间 -->\r\n            <view class=\"event-info registration-time\">\r\n              <text class=\"time-label\">报名时间：</text>\r\n              <text class=\"time-value\">{{ formatRegistrationTime(item.registrationTime) }}</text>\r\n            </view>\r\n\r\n            <!-- 底部行：只显示状态标签 -->\r\n            <view class=\"card-bottom-row\">\r\n              <view>\r\n                <!-- 已报名状态使用背景图片 -->\r\n                <view\r\n                  v-if=\"item.status === 0\"\r\n                  class=\"status-with-bg\"\r\n                >\r\n                  <image\r\n                    class=\"status-bg-image\"\r\n                    :src=\"orderStatusBgUrl\"\r\n                    mode=\"aspectFit\"\r\n                  ></image>\r\n                  <text class=\"status-text\">已报名</text>\r\n                </view>\r\n                <!-- 已取消状态使用灰色背景 -->\r\n                <view\r\n                  v-else-if=\"item.status === 1\"\r\n                  class=\"status-cancelled\"\r\n                >\r\n                  <text class=\"status-text\">已取消</text>\r\n                </view>\r\n                <!-- 其他状态使用普通标签 -->\r\n                <view\r\n                  v-else\r\n                  :class=\"['registration-status-tag', getRegistrationStatusClass(item.status)]\"\r\n                >\r\n                  {{ formatRegistrationStatus(item.status) }}\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <!-- 独立的取消报名按钮 - 基于卡片容器绝对定位 -->\r\n          <view\r\n            v-if=\"item.status === 0\"\r\n            class=\"cancel-btn-absolute\"\r\n            @click.stop=\"showCancelConfirm(item)\"\r\n          >\r\n            取消报名\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载更多组件 -->\r\n      <view class=\"loadmore-wrapper\" v-if=\"registrationList.length > 0\">\r\n        <up-loadmore \r\n          :status=\"loadMoreStatus\" \r\n          :loading-text=\"'正在加载...'\" \r\n          :loadmore-text=\"'上拉加载更多'\"\r\n          :nomore-text=\"'没有更多了'\" \r\n        />\r\n      </view>\r\n    </scroll-view>\r\n    \r\n    <!-- 自定义底部导航栏 -->\r\n    <CustomTabBar :current=\"4\" />\r\n\r\n    <!-- 自定义取消报名确认弹窗 -->\r\n    <view v-if=\"showCancelModal\" class=\"cancel-modal-overlay\" @click=\"closeCancelModal\">\r\n      <view class=\"cancel-modal-content\" @click.stop>\r\n        <!-- 警告图标和标题 -->\r\n        <view class=\"modal-header\">\r\n          <image class=\"warning-icon\" :src=\"ordersWarningIconUrl\" mode=\"aspectFit\"></image>\r\n          <text class=\"modal-title\">操作提示</text>\r\n        </view>\r\n\r\n        <!-- 提示内容 -->\r\n        <view class=\"modal-body\">\r\n          <text class=\"modal-message\">是否取消报名？</text>\r\n        </view>\r\n\r\n        <!-- 按钮组 -->\r\n        <view class=\"modal-footer\">\r\n          <view class=\"modal-btn cancel-btn\" @click=\"closeCancelModal\">\r\n            暂不取消\r\n          </view>\r\n          <view class=\"modal-btn confirm-btn\" @click=\"confirmCancelRegistration\">\r\n            确认取消\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport {computed, ref} from 'vue';\r\nimport {onLoad, onPullDownRefresh, onShow, onShareAppMessage} from '@dcloudio/uni-app';\r\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue';\r\nimport {getMyRegistrationsApi, cancelRegistrationApi} from '@/api/data/registration.js';\r\nimport {getEventDetailApi} from '@/api/data/event.js';\r\nimport {getFullImageUrl} from '@/utils/image.js';\r\n\r\n// ==================== 响应式数据定义 ====================\r\nconst loading = ref(false);\r\nconst isRefreshing = ref(false);\r\nconst registrationList = ref([]);\r\nconst loginCheckTrigger = ref(0); \r\nconst showCancelModal = ref(false);\r\nconst currentCancelItem = ref(null);\r\n// 订单页静态资源（暗号映射）\r\nconst ordersWarningIconUrl = ref(''); // 暗号：orders_warning\r\nconst orderStatusBgUrl = ref(''); // 暗号：order_bg\r\n\r\n// 解析静态资源缓存：仅读取缓存暗号\r\nconst resolveAssetUrl = (assetKey) => {\r\n  const assets = uni.getStorageSync('staticAssets');\r\n  return (assets && assets[assetKey]) ? assets[assetKey] : '';\r\n};\r\n\r\n// 刷新本页静态资源（暗号：orders_warning、order_bg）\r\nconst refreshOrdersAssets = () => {\r\n  ordersWarningIconUrl.value = resolveAssetUrl('orders_warning');\r\n  orderStatusBgUrl.value = resolveAssetUrl('order_bg');\r\n};\r\n\r\n// ==================== 计算属性 ====================\r\nconst loadMoreStatus = computed(() => {\r\n  if (loading.value) return 'loading';\r\n  return 'nomore'; // 报名记录通常不需要分页\r\n});\r\n\r\n// 从本地存储获取用户token\r\nconst getUserToken = () => {\r\n  try {\r\n    return uni.getStorageSync('token') || null;\r\n  } catch (e) {\r\n    return null;\r\n  }\r\n};\r\n\r\n// 检查用户是否已登录\r\nconst isLoggedIn = computed(() => {\r\n  // 通过 loginCheckTrigger 强制重新计算\r\n  loginCheckTrigger.value;\r\n  return !!getUserToken();\r\n});\r\n\r\n// ==================== 核心方法 ====================\r\n/**\r\n * 检查登录状态并刷新数据\r\n */\r\nconst checkLoginAndRefresh = async () => {\r\n  console.log('=== 检查登录状态并刷新数据 ===');\r\n  \r\n  // 强制触发登录状态重新计算\r\n  loginCheckTrigger.value++;\r\n  \r\n  const currentToken = getUserToken();\r\n  console.log('当前登录状态:', !!currentToken);\r\n  \r\n  if (!currentToken) {\r\n    console.log('用户未登录，清空数据并跳转到登录页');\r\n    registrationList.value = [];\r\n    \r\n    uni.showModal({\r\n      title: '提示',\r\n      content: '请先登录后查看报名记录',\r\n      confirmText: '去登录',\r\n      cancelText: '取消',\r\n      success: (res) => {\r\n        if (res.confirm) {\r\n          // 记录登录前的页面，便于登录成功后正确返回\r\n          uni.setStorageSync('loginBackPage', '/pages/profile/orders');\r\n          uni.navigateTo({ url: '/pages/login/index' });\r\n        } else {\r\n          // 用户取消，尝试返回上一页，如果失败则跳转到首页\r\n          uni.navigateBack({\r\n            fail: () => {\r\n              uni.switchTab({ url: '/pages/index/index' });\r\n            }\r\n          });\r\n        }\r\n      }\r\n    });\r\n    return;\r\n  }\r\n  \r\n  // 用户已登录，刷新数据\r\n  await loadMyRegistrations();\r\n};\r\n\r\n/**\r\n * 获取我的报名订单 \r\n */\r\nconst loadMyRegistrations = async () => {\r\n  if (loading.value) return;\r\n  \r\n  // 首先检查登录状态\r\n  if (!isLoggedIn.value) {\r\n    console.log('用户未登录，跳过数据加载');\r\n    registrationList.value = [];\r\n    return;\r\n  }\r\n  \r\n  loading.value = true;\r\n  \r\n  try {\r\n    // 直接调用现有接口\r\n    const response = await getMyRegistrationsApi();\r\n    \r\n    if (response.code === 200) {\r\n      // 直接使用返回数据，按时间倒序\r\n      const rawData = response.data || [];\r\n      console.log('获取报名记录成功:', rawData.length, '条');\r\n      \r\n      // 如果有数据，尝试获取活动标题信息\r\n      if (rawData.length > 0) {\r\n        registrationList.value = await enrichRegistrationData(rawData);\r\n      } else {\r\n        registrationList.value = [];\r\n      }\r\n    } else {\r\n      console.log('暂无报名记录');\r\n      registrationList.value = [];\r\n    }\r\n    \r\n  } catch (error) {\r\n    console.error('获取报名订单失败:', error);\r\n    \r\n    // 如果是401错误（未登录），特殊处理\r\n    if (error.message && error.message.includes('未登录')) {\r\n      console.log('检测到登录状态异常，清除token并提示重新登录');\r\n      uni.removeStorageSync('token');\r\n      loginCheckTrigger.value++; // 触发登录状态重新计算\r\n      \r\n      uni.showModal({\r\n        title: '登录过期',\r\n        content: '登录状态已过期，请重新登录',\r\n        confirmText: '重新登录',\r\n        showCancel: false,\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 记录登录前的页面，便于登录成功后正确返回\r\n            uni.setStorageSync('loginBackPage', '/pages/profile/orders');\r\n            uni.navigateTo({ url: '/pages/login/index' });\r\n          }\r\n        }\r\n      });\r\n      return;\r\n    }\r\n    \r\n    handleError(error, '获取报名记录');\r\n  } finally {\r\n    loading.value = false;\r\n    isRefreshing.value = false;\r\n  }\r\n};\r\n\r\n/**\r\n * 简化数据增强：只获取必要的活动信息\r\n */\r\nconst enrichRegistrationData = async (registrations) => {\r\n  // 先按报名时间倒序排列\r\n  const sortedRegistrations = registrations.sort((a, b) => {\r\n    const db = parseSafeDate(b.registrationTime)?.getTime() ?? 0;\r\n    const da = parseSafeDate(a.registrationTime)?.getTime() ?? 0;\r\n    return db - da;\r\n  });\r\n  \r\n  // 异步获取每个活动的基本信息\r\n  const enrichedData = await Promise.allSettled(\r\n    sortedRegistrations.map(async (registration) => {\r\n      try {\r\n        // 调用活动详情接口获取标题和封面\r\n        const eventDetailResponse = await getEventDetailApi(registration.eventId);\r\n        \r\n        if (eventDetailResponse.code === 200 && eventDetailResponse.data) {\r\n          const eventInfo = eventDetailResponse.data;\r\n          return {\r\n            ...registration,\r\n            title: eventInfo.title || '活动标题',\r\n            coverImageUrl: getFullImageUrl(eventInfo.coverImageUrl),\r\n            location: eventInfo.location || '待定',\r\n            startTime: eventInfo.startTime\r\n          };\r\n        } else {\r\n          // 如果获取失败，使用默认值\r\n          return {\r\n            ...registration,\r\n            title: '活动标题',\r\n            coverImageUrl: '',\r\n            location: '待定'\r\n          };\r\n        }\r\n      } catch (error) {\r\n        console.warn('获取活动详情失败:', registration.eventId, error);\r\n        // 出错时使用默认值\r\n        return {\r\n          ...registration,\r\n          title: '活动标题',\r\n            coverImageUrl: '',\r\n          location: '待定'\r\n        };\r\n      }\r\n    })\r\n  );\r\n  \r\n  // 过滤成功的结果\r\n  return enrichedData\r\n    .filter(result => result.status === 'fulfilled')\r\n    .map(result => result.value);\r\n};\r\n\r\n\r\n\r\n/**\r\n * 跳转到活动详情页\r\n */\r\nconst goToEventDetail = (eventId) => {\r\n  uni.navigateTo({\r\n    url: `/pages_sub/pages_event/detail?id=${eventId}`\r\n  });\r\n};\r\n\r\n/**\r\n * 显示取消报名确认弹窗\r\n */\r\nconst showCancelConfirm = (item) => {\r\n  currentCancelItem.value = item;\r\n  showCancelModal.value = true;\r\n};\r\n\r\n/**\r\n * 关闭自定义取消弹窗\r\n */\r\nconst closeCancelModal = () => {\r\n  showCancelModal.value = false;\r\n  currentCancelItem.value = null;\r\n};\r\n\r\n/**\r\n * 确认取消报名\r\n */\r\nconst confirmCancelRegistration = () => {\r\n  if (currentCancelItem.value) {\r\n    cancelRegistration(currentCancelItem.value);\r\n    closeCancelModal();\r\n  }\r\n};\r\n\r\n/**\r\n * 取消报名\r\n */\r\nconst cancelRegistration = async (item) => {\r\n  try {\r\n    // 显示加载提示\r\n    uni.showLoading({\r\n      title: '取消中...',\r\n      mask: true\r\n    });\r\n\r\n    // 调用取消报名API\r\n    const response = await cancelRegistrationApi({\r\n      eventId: item.eventId\r\n    });\r\n\r\n    uni.hideLoading();\r\n\r\n    if (response.code === 200) {\r\n      // 取消成功，更新本地数据\r\n      const index = registrationList.value.findIndex(reg => \r\n        reg.eventId === item.eventId && reg.userId === item.userId\r\n      );\r\n      \r\n      if (index !== -1) {\r\n        // 更新状态为已取消\r\n        registrationList.value[index].status = 1;\r\n      }\r\n\r\n      uni.showToast({\r\n        title: '取消报名成功',\r\n        icon: 'success',\r\n        duration: 2000\r\n      });\r\n    } else {\r\n      throw new Error(response.msg || '取消报名失败');\r\n    }\r\n  } catch (error) {\r\n    uni.hideLoading();\r\n    console.error('取消报名失败:', error);\r\n    \r\n    uni.showToast({\r\n      title: error.message || '取消报名失败，请稍后重试',\r\n      icon: 'none',\r\n      duration: 3000\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * 下拉刷新\r\n */\r\nconst onRefresh = () => {\r\n  isRefreshing.value = true;\r\n  checkLoginAndRefresh();\r\n};\r\n\r\n/**\r\n * 跳转到登录页\r\n */\r\nconst goToLogin = () => {\r\n  uni.navigateTo({ url: '/pages_sub/pages_other/login' });\r\n};\r\n\r\n/**\r\n * 图片加载成功事件\r\n */\r\nconst onImageLoad = (e) => {\r\n  console.log('图片加载成功');\r\n};\r\n\r\n/**\r\n * 图片加载失败事件\r\n */\r\nconst onImageError = (e) => {\r\n  console.error('图片加载失败:', e);\r\n};\r\n\r\n/**\r\n * 上拉加载更多（暂时不需要）\r\n */\r\nconst onLoadMore = () => {\r\n  // 报名记录通常不需要分页\r\n};\r\n\r\n/**\r\n * 兼容 iOS 的安全日期解析：\r\n * - 优先将 \"yyyy-MM-dd HH:mm:ss\" 转为 \"yyyy-MM-ddTHH:mm:ss\"\r\n * - 若仍失败，回退为 \"yyyy/MM/dd HH:mm:ss\" 形式\r\n * - 同时兼容时间戳与 Date 输入\r\n */\r\nconst parseSafeDate = (input) => {\r\n  if (!input) return null;\r\n  if (input instanceof Date) {\r\n    return isNaN(input.getTime()) ? null : input;\r\n  }\r\n  if (typeof input === 'number') {\r\n    const d = new Date(input);\r\n    return isNaN(d.getTime()) ? null : d;\r\n  }\r\n  if (typeof input === 'string') {\r\n    let s = input.trim();\r\n    // 将 \"yyyy-MM-dd HH:mm:ss\" 转为 \"yyyy-MM-ddTHH:mm:ss\"\r\n    if (/^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$/.test(s)) {\r\n      s = s.replace(' ', 'T');\r\n    }\r\n    let d = new Date(s);\r\n    if (isNaN(d.getTime())) {\r\n      // 回退：替换日期分隔符为斜杠，适配 iOS\r\n      const m = s.match(/^(\\d{4})-(\\d{1,2})-(\\d{1,2})(?:[ T](\\d{1,2}):(\\d{2})(?::(\\d{2}))?)?/);\r\n      if (m) {\r\n        const y = m[1];\r\n        const mo = m[2];\r\n        const day = m[3];\r\n        const rest = m[4] ? ` ${m[4]}:${m[5]}:${m[6] || '00'}` : '';\r\n        d = new Date(`${y}/${mo}/${day}${rest}`);\r\n      }\r\n    }\r\n    return isNaN(d.getTime()) ? null : d;\r\n  }\r\n  return null;\r\n};\r\n\r\n/**\r\n * 格式化报名时间\r\n */\r\nconst formatRegistrationTime = (time) => {\r\n  if (!time) return '';\r\n  const date = parseSafeDate(time);\r\n  if (!date) return '';\r\n  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\r\n};\r\n\r\n/**\r\n * 格式化报名状态\r\n */\r\nconst formatRegistrationStatus = (status) => {\r\n  return status === 0 ? '已报名' : '已取消';\r\n};\r\n\r\n/**\r\n * 获取报名状态样式类\r\n */\r\nconst getRegistrationStatusClass = (status) => {\r\n  return status === 0 ? 'registered' : 'cancelled';\r\n};\r\n\r\n/**\r\n * 统一错误处理\r\n */\r\nconst handleError = (error, context = '') => {\r\n  console.error(`${context} 错误:`, error);\r\n  \r\n  let message = '操作失败，请稍后重试';\r\n  \r\n  if (error.code) {\r\n    switch (error.code) {\r\n      case 401:\r\n        message = '登录已过期，请重新登录';\r\n        uni.reLaunch({ url: '/pages/login/index' });\r\n        return;\r\n      case 403:\r\n        message = '权限不足';\r\n        break;\r\n      case 404:\r\n        message = '请求的资源不存在';\r\n        break;\r\n      case 500:\r\n        message = '服务器错误，请稍后重试';\r\n        break;\r\n      default:\r\n        message = error.message || message;\r\n    }\r\n  }\r\n  \r\n  uni.showToast({\r\n    title: message,\r\n    icon: 'none',\r\n    duration: 3000\r\n  });\r\n};\r\n\r\n// ==================== 生命周期 ====================\r\nonLoad(() => {\r\n  console.log('报名订单页 onLoad - 页面首次加载');\r\n  uni.showShareMenu({\r\n      menus: ['shareAppMessage', 'shareTimeline']\r\n    });\r\n\r\n  refreshOrdersAssets();\r\n  checkLoginAndRefresh();\r\n});\r\n\r\nonShow(async () => {\r\n  // console.log('=== 我的报名订单页面 onShow 触发 ===');\r\n  \r\n  // 检查是否从登录页返回\r\n  try {\r\n    const loginBackPage = uni.getStorageSync('loginBackPage');\r\n    if (loginBackPage === '/pages/profile/orders') {\r\n      // console.log('检测到从登录页返回，清除标记并刷新数据');\r\n      uni.removeStorageSync('loginBackPage');\r\n      \r\n      // 强制触发登录状态重新计算\r\n      loginCheckTrigger.value++;\r\n      \r\n      // 直接加载数据，不需要再次检查登录状态\r\n      await loadMyRegistrations();\r\n      return;\r\n    }\r\n  } catch (e) {\r\n    console.warn('检查登录返回标记失败:', e);\r\n  }\r\n  \r\n  // 正常的登录状态检查\r\n  checkLoginAndRefresh();\r\n  refreshOrdersAssets();\r\n});\r\n\r\nonPullDownRefresh(async () => {\r\n  await onRefresh();\r\n  setTimeout(() => {\r\n    uni.stopPullDownRefresh();\r\n  }, 1000);\r\n});\r\n\r\nonShareAppMessage(() => {\r\n  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.orders-page {\r\n  width: 750rpx;\r\n    /* 如果希望高度是固定的，请使用 height。如果希望高度能自适应内容，请使用 min-height */\r\n    height: 1624rpx; \r\n    background: #FFFFFF;\r\n    border-radius: 0rpx; /* border-radius: 0rpx 0rpx 0rpx 0rpx; 的简写 */\r\n    \r\n    /* 以下为原有的flex布局，建议保留以维持页面结构 */\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.orders-list-scroll {\r\n  flex: 1;\r\n   box-sizing: border-box;\r\n   padding: 0; /* 移除所有内边距，特别是左右的 */\r\n   padding-bottom: 180rpx; // 仅保留底部的，为导航栏留出空间\r\n}\r\n\r\n.loading-state {\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.empty-state {\r\n  padding: 100rpx 0;\r\n  text-align: center;\r\n}\r\n\r\n.orders-list {\r\n  .order-card {\r\n    position: relative; \r\n    display: flex;\r\n    padding: 24rpx; \r\n    margin: 0 0 2rpx 0;\r\n    background-color: #ffffff;\r\n    border-radius: 0; \r\n    box-shadow: none;\r\n    border-bottom: 1rpx solid #F0F0F0; \r\n    & + .order-card {\r\n      margin-top: 24rpx;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n.card-left {\r\n position: relative;\r\n   width: 336rpx;\r\n   height: 192rpx;\r\n   flex-shrink: 0;\r\n \r\n   .event-image {\r\n     width: 100%;\r\n     height: 100%;\r\n     display: block;\r\n     border-radius: 19rpx; \r\n   }\r\n\r\n}\r\n\r\n.card-right {\r\n  flex: 1;\r\n  padding: 20rpx 24rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  margin-top: -15rpx;\r\n  \r\n  .event-title {\r\n    width: 346rpx;\r\n      height: 80rpx;\r\n      font-family: 'Alibaba PuHuiTi', sans-serif; \r\n      font-weight: 400;\r\n      font-size: 28rpx;\r\n      color: #23232A;\r\n      text-align: justified;\r\n      font-style: normal;\r\n      text-transform: none;\r\n      line-height: 40rpx; \r\n      display: -webkit-box;\r\n      -webkit-box-orient: vertical;\r\n      -webkit-line-clamp: 2;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n  }\r\n  \r\n  .registration-time {\r\n\tfont-family: 'Alibaba PuHuiTi 3.0', sans-serif;\r\n\tfont-weight: normal; \r\n\tfont-size: 22rpx;   \r\n\tcolor: #9B9A9A;\r\n\tline-height: normal;\r\n\tmargin-top: 8rpx;\r\n\r\n\t.time-value {\r\n\t\tfont-weight: inherit;\r\n\t\tcolor: inherit;\r\n\t}\r\n  }\r\n}\r\n\r\n// 新增：底部行样式\r\n.card-bottom-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 20rpx; \r\n}\r\n\r\n// 带背景图的状态样式 - 改为蓝色背景\r\n.status-with-bg {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 90rpx;\r\n  height: 36rpx;\r\n  background: #023F98;\r\n\r\n  .status-bg-image {\r\n    display: none;\r\n  }\r\n\r\n  .status-text {\r\n    color: #ffffff;\r\n    font-size: 24rpx;\r\n    font-weight: 500;\r\n    white-space: nowrap;\r\n  }\r\n}\r\n\r\n// 已取消状态样式\r\n.status-cancelled {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 90rpx;\r\n  height: 36rpx;\r\n  background: #CBCBCB;\r\n  // border-radius: 8rpx;\r\n\r\n  .status-text {\r\n    color: #ffffff;\r\n    font-size: 24rpx;\r\n    font-weight: 500;\r\n    white-space: nowrap;\r\n  }\r\n}\r\n\r\n.registration-status-tag {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #ffffff;\r\n  font-size: 24rpx; \r\n  padding: 4rpx 12rpx;\r\n  border-radius: 8rpx; \r\n  font-weight: 500;\r\n  height: 36rpx;\r\n  box-sizing: border-box;\r\n\r\n  &.registered {\r\n    background-color: #409eff; \r\n  }\r\n\r\n  &.cancelled {\r\n    background-color: #909399;\r\n  }\r\n}\r\n\r\n.card-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.loadmore-wrapper {\r\n  padding: 20rpx 0 40rpx 0;\r\n}\r\n\r\n/* 绝对定位的取消报名按钮样式 */\r\n.cancel-btn-absolute {\r\n  position: absolute;\r\n  right: 24rpx; \r\n  bottom: 44rpx;\r\n\r\n  /* 布局和尺寸 */\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n  width: 96rpx;\r\n  height: 36rpx;\r\n  box-sizing: border-box;\r\n\r\n  /* 字体和外观 */\r\n  font-family: 'Alibaba PuHuiTi', sans-serif; \r\n  font-weight: 400;\r\n  font-size: 24rpx;\r\n  color: #66666E;\r\n  text-align: left;\r\n  font-style: normal;\r\n  text-transform: none;\r\n  letter-spacing: 0;\r\n\r\n  /* 其他样式 */\r\n  background: none;\r\n  border: none;\r\n  border-radius: 8rpx;\r\n\r\n  /* 点击效果 */\r\n  &:active {\r\n    opacity: 0.7;\r\n  }\r\n}\r\n\r\n/* 自定义取消报名弹窗样式 */\r\n.cancel-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n}\r\n\r\n.cancel-modal-content {\r\n  width: 654rpx;\r\n  height: 420rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 16rpx;\r\n  position: relative;\r\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.modal-header {\r\n  position: absolute;\r\n  top: 42rpx;\r\n  left: 48rpx;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .warning-icon {\r\n    width: 48rpx;\r\n    height: 40rpx;\r\n    margin-right: 16rpx;\r\n  }\r\n\r\n  .modal-title {\r\n    width: 142rpx;\r\n    height: 44rpx;\r\n    font-family: 'Alibaba PuHuiTi', sans-serif; \r\n    font-weight: 500;\r\n    font-size: 36rpx;\r\n    color: #23232A;\r\n    line-height: 44rpx;\r\n    text-align: center;\r\n    font-style: normal;\r\n    text-transform: none;\r\n  }\r\n}\r\n\r\n.modal-body {\r\n  position: absolute;\r\n  top: 176rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n\r\n  .modal-message {\r\n    font-size: 32rpx;\r\n    color: #23232A;\r\n    line-height: 44rpx;\r\n    text-align: center;\r\n\tfont-weight: normal;\r\n    text-transform: none;\r\n    display: block;\r\n  }\r\n}\r\n\r\n.modal-footer {\r\n  position: absolute;\r\n  top: 316rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  display: flex;\r\n  gap: 24rpx;\r\n\r\n  .modal-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: opacity 0.2s;\r\n\r\n    &:active {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n\r\n  .cancel-btn {\r\n      width: 292rpx;\r\n      height: 76rpx;\r\n      background: rgba(42, 97, 241, 0.1); \r\n      border-radius: 8rpx;\r\n      font-weight: normal; \r\n      font-size: 28rpx;\r\n      color: #23232A;\r\n      line-height: 44rpx;\r\n  }\r\n\r\n  .confirm-btn {\r\n    width: 292rpx;\r\n    height: 76rpx;\r\n    background: #023F98;\r\n    border-radius: 8rpx;\r\n    font-size: 28rpx;\r\n    font-weight: 400;\r\n    color: #ffffff;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media screen and (max-width: 750rpx) {\r\n  .order-card {\r\n    .card-left {\r\n      width: 200rpx;\r\n      height: 160rpx;\r\n    }\r\n\r\n    .card-right {\r\n      .event-title {\r\n        font-size: 28rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_profile/orders.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "computed", "getMyRegistrationsApi", "getEventDetailApi", "getFullImageUrl", "cancelRegistrationApi", "onLoad", "onShow", "onPullDownRefresh", "onShareAppMessage"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAmKA,MAAA,eAAA,MAAA;;;;AAMA,UAAA,UAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,mBAAAA,cAAAA,IAAA,CAAA,CAAA;AACA,UAAA,oBAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,kBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,oBAAAA,cAAAA,IAAA,IAAA;AAEA,UAAA,uBAAAA,cAAAA,IAAA,EAAA;AACA,UAAA,mBAAAA,cAAAA,IAAA,EAAA;AAGA,UAAA,kBAAA,CAAA,aAAA;AACA,YAAA,SAAAC,cAAAA,MAAA,eAAA,cAAA;AACA,aAAA,UAAA,OAAA,QAAA,IAAA,OAAA,QAAA,IAAA;AAAA,IACA;AAGA,UAAA,sBAAA,MAAA;AACA,2BAAA,QAAA,gBAAA,gBAAA;AACA,uBAAA,QAAA,gBAAA,UAAA;AAAA,IACA;AAGA,UAAA,iBAAAC,cAAA,SAAA,MAAA;AACA,UAAA,QAAA;AAAA,eAAA;AACA,aAAA;AAAA,IACA,CAAA;AAGA,UAAA,eAAA,MAAA;AACA,UAAA;AACA,eAAAD,oBAAA,eAAA,OAAA,KAAA;AAAA,MACA,SAAA,GAAA;AACA,eAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,aAAAC,cAAA,SAAA,MAAA;AAEA,wBAAA;AACA,aAAA,CAAA,CAAA;IACA,CAAA;AAMA,UAAA,uBAAA,YAAA;AACAD,oBAAAA,MAAA,MAAA,OAAA,6CAAA,qBAAA;AAGA,wBAAA;AAEA,YAAA,eAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,6CAAA,WAAA,CAAA,CAAA,YAAA;AAEA,UAAA,CAAA,cAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,6CAAA,mBAAA;AACA,yBAAA,QAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,SAAA;AAAA,UACA,aAAA;AAAA,UACA,YAAA;AAAA,UACA,SAAA,CAAA,QAAA;AACA,gBAAA,IAAA,SAAA;AAEAA,4BAAAA,MAAA,eAAA,iBAAA,uBAAA;AACAA,4BAAAA,MAAA,WAAA,EAAA,KAAA,qBAAA,CAAA;AAAA,YACA,OAAA;AAEAA,4BAAAA,MAAA,aAAA;AAAA,gBACA,MAAA,MAAA;AACAA,gCAAAA,MAAA,UAAA,EAAA,KAAA,qBAAA,CAAA;AAAA,gBACA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAGA,YAAA,oBAAA;AAAA,IACA;AAKA,UAAA,sBAAA,YAAA;AACA,UAAA,QAAA;AAAA;AAGA,UAAA,CAAA,WAAA,OAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,6CAAA,cAAA;AACA,yBAAA,QAAA;AACA;AAAA,MACA;AAEA,cAAA,QAAA;AAEA,UAAA;AAEA,cAAA,WAAA,MAAAE,sBAAAA;AAEA,YAAA,SAAA,SAAA,KAAA;AAEA,gBAAA,UAAA,SAAA,QAAA;AACAF,8BAAA,MAAA,OAAA,6CAAA,aAAA,QAAA,QAAA,GAAA;AAGA,cAAA,QAAA,SAAA,GAAA;AACA,6BAAA,QAAA,MAAA,uBAAA,OAAA;AAAA,UACA,OAAA;AACA,6BAAA,QAAA;UACA;AAAA,QACA,OAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,6CAAA,QAAA;AACA,2BAAA,QAAA;QACA;AAAA,MAEA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,6CAAA,aAAA,KAAA;AAGA,YAAA,MAAA,WAAA,MAAA,QAAA,SAAA,KAAA,GAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,6CAAA,0BAAA;AACAA,8BAAA,kBAAA,OAAA;AACA,4BAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,SAAA;AAAA,YACA,aAAA;AAAA,YACA,YAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACA,kBAAA,IAAA,SAAA;AAEAA,8BAAAA,MAAA,eAAA,iBAAA,uBAAA;AACAA,8BAAAA,MAAA,WAAA,EAAA,KAAA,qBAAA,CAAA;AAAA,cACA;AAAA,YACA;AAAA,UACA,CAAA;AACA;AAAA,QACA;AAEA,oBAAA,OAAA,QAAA;AAAA,MACA,UAAA;AACA,gBAAA,QAAA;AACA,qBAAA,QAAA;AAAA,MACA;AAAA,IACA;AAKA,UAAA,yBAAA,OAAA,kBAAA;AAEA,YAAA,sBAAA,cAAA,KAAA,CAAA,GAAA,MAAA;;AACA,cAAA,OAAA,mBAAA,EAAA,gBAAA,MAAA,mBAAA,cAAA;AACA,cAAA,OAAA,mBAAA,EAAA,gBAAA,MAAA,mBAAA,cAAA;AACA,eAAA,KAAA;AAAA,MACA,CAAA;AAGA,YAAA,eAAA,MAAA,QAAA;AAAA,QACA,oBAAA,IAAA,OAAA,iBAAA;AACA,cAAA;AAEA,kBAAA,sBAAA,MAAAG,eAAAA,kBAAA,aAAA,OAAA;AAEA,gBAAA,oBAAA,SAAA,OAAA,oBAAA,MAAA;AACA,oBAAA,YAAA,oBAAA;AACA,qBAAA;AAAA,gBACA,GAAA;AAAA,gBACA,OAAA,UAAA,SAAA;AAAA,gBACA,eAAAC,YAAAA,gBAAA,UAAA,aAAA;AAAA,gBACA,UAAA,UAAA,YAAA;AAAA,gBACA,WAAA,UAAA;AAAA,cACA;AAAA,YACA,OAAA;AAEA,qBAAA;AAAA,gBACA,GAAA;AAAA,gBACA,OAAA;AAAA,gBACA,eAAA;AAAA,gBACA,UAAA;AAAA,cACA;AAAA,YACA;AAAA,UACA,SAAA,OAAA;AACAJ,gCAAA,MAAA,QAAA,6CAAA,aAAA,aAAA,SAAA,KAAA;AAEA,mBAAA;AAAA,cACA,GAAA;AAAA,cACA,OAAA;AAAA,cACA,eAAA;AAAA,cACA,UAAA;AAAA,YACA;AAAA,UACA;AAAA,QACA,CAAA;AAAA,MACA;AAGA,aAAA,aACA,OAAA,YAAA,OAAA,WAAA,WAAA,EACA,IAAA,YAAA,OAAA,KAAA;AAAA,IACA;AAOA,UAAA,kBAAA,CAAA,YAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,oCAAA,OAAA;AAAA,MACA,CAAA;AAAA,IACA;AAKA,UAAA,oBAAA,CAAA,SAAA;AACA,wBAAA,QAAA;AACA,sBAAA,QAAA;AAAA,IACA;AAKA,UAAA,mBAAA,MAAA;AACA,sBAAA,QAAA;AACA,wBAAA,QAAA;AAAA,IACA;AAKA,UAAA,4BAAA,MAAA;AACA,UAAA,kBAAA,OAAA;AACA,2BAAA,kBAAA,KAAA;AACA;MACA;AAAA,IACA;AAKA,UAAA,qBAAA,OAAA,SAAA;AACA,UAAA;AAEAA,sBAAAA,MAAA,YAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAGA,cAAA,WAAA,MAAAK,4CAAA;AAAA,UACA,SAAA,KAAA;AAAA,QACA,CAAA;AAEAL,sBAAA,MAAA,YAAA;AAEA,YAAA,SAAA,SAAA,KAAA;AAEA,gBAAA,QAAA,iBAAA,MAAA;AAAA,YAAA,SACA,IAAA,YAAA,KAAA,WAAA,IAAA,WAAA,KAAA;AAAA,UACA;AAEA,cAAA,UAAA,IAAA;AAEA,6BAAA,MAAA,KAAA,EAAA,SAAA;AAAA,UACA;AAEAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,YACA,UAAA;AAAA,UACA,CAAA;AAAA,QACA,OAAA;AACA,gBAAA,IAAA,MAAA,SAAA,OAAA,QAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAA,sBAAA,MAAA,YAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,6CAAA,WAAA,KAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA,MAAA,WAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAKA,UAAA,YAAA,MAAA;AACA,mBAAA,QAAA;AACA;IACA;AAKA,UAAA,YAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA,EAAA,KAAA,+BAAA,CAAA;AAAA,IACA;AAKA,UAAA,cAAA,CAAA,MAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,6CAAA,QAAA;AAAA,IACA;AAKA,UAAA,eAAA,CAAA,MAAA;AACAA,oBAAA,MAAA,MAAA,SAAA,6CAAA,WAAA,CAAA;AAAA,IACA;AAKA,UAAA,aAAA,MAAA;AAAA,IAEA;AAQA,UAAA,gBAAA,CAAA,UAAA;AACA,UAAA,CAAA;AAAA,eAAA;AACA,UAAA,iBAAA,MAAA;AACA,eAAA,MAAA,MAAA,QAAA,CAAA,IAAA,OAAA;AAAA,MACA;AACA,UAAA,OAAA,UAAA,UAAA;AACA,cAAA,IAAA,IAAA,KAAA,KAAA;AACA,eAAA,MAAA,EAAA,QAAA,CAAA,IAAA,OAAA;AAAA,MACA;AACA,UAAA,OAAA,UAAA,UAAA;AACA,YAAA,IAAA,MAAA;AAEA,YAAA,wCAAA,KAAA,CAAA,GAAA;AACA,cAAA,EAAA,QAAA,KAAA,GAAA;AAAA,QACA;AACA,YAAA,IAAA,IAAA,KAAA,CAAA;AACA,YAAA,MAAA,EAAA,QAAA,CAAA,GAAA;AAEA,gBAAA,IAAA,EAAA,MAAA,qEAAA;AACA,cAAA,GAAA;AACA,kBAAA,IAAA,EAAA,CAAA;AACA,kBAAA,KAAA,EAAA,CAAA;AACA,kBAAA,MAAA,EAAA,CAAA;AACA,kBAAA,OAAA,EAAA,CAAA,IAAA,IAAA,EAAA,CAAA,CAAA,IAAA,EAAA,CAAA,CAAA,IAAA,EAAA,CAAA,KAAA,IAAA,KAAA;AACA,gBAAA,oBAAA,KAAA,GAAA,CAAA,IAAA,EAAA,IAAA,GAAA,GAAA,IAAA,EAAA;AAAA,UACA;AAAA,QACA;AACA,eAAA,MAAA,EAAA,QAAA,CAAA,IAAA,OAAA;AAAA,MACA;AACA,aAAA;AAAA,IACA;AAKA,UAAA,yBAAA,CAAA,SAAA;AACA,UAAA,CAAA;AAAA,eAAA;AACA,YAAA,OAAA,cAAA,IAAA;AACA,UAAA,CAAA;AAAA,eAAA;AACA,aAAA,GAAA,KAAA,YAAA,CAAA,IAAA,OAAA,KAAA,aAAA,CAAA,EAAA,SAAA,GAAA,GAAA,CAAA,IAAA,OAAA,KAAA,QAAA,CAAA,EAAA,SAAA,GAAA,GAAA,CAAA,IAAA,OAAA,KAAA,UAAA,EAAA,SAAA,GAAA,GAAA,CAAA,IAAA,OAAA,KAAA,WAAA,CAAA,EAAA,SAAA,GAAA,GAAA,CAAA;AAAA,IACA;AAKA,UAAA,2BAAA,CAAA,WAAA;AACA,aAAA,WAAA,IAAA,QAAA;AAAA,IACA;AAKA,UAAA,6BAAA,CAAA,WAAA;AACA,aAAA,WAAA,IAAA,eAAA;AAAA,IACA;AAKA,UAAA,cAAA,CAAA,OAAA,UAAA,OAAA;AACAA,0BAAA,MAAA,SAAA,6CAAA,GAAA,OAAA,QAAA,KAAA;AAEA,UAAA,UAAA;AAEA,UAAA,MAAA,MAAA;AACA,gBAAA,MAAA,MAAA;AAAA,UACA,KAAA;AACA,sBAAA;AACAA,0BAAAA,MAAA,SAAA,EAAA,KAAA,qBAAA,CAAA;AACA;AAAA,UACA,KAAA;AACA,sBAAA;AACA;AAAA,UACA,KAAA;AACA,sBAAA;AACA;AAAA,UACA,KAAA;AACA,sBAAA;AACA;AAAA,UACA;AACA,sBAAA,MAAA,WAAA;AAAA,QACA;AAAA,MACA;AAEAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,QACA,UAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGAM,kBAAAA,OAAA,MAAA;AACAN,oBAAAA,MAAA,MAAA,OAAA,6CAAA,uBAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAEA;AACA;IACA,CAAA;AAEAO,kBAAAA,OAAA,YAAA;AAIA,UAAA;AACA,cAAA,gBAAAP,cAAAA,MAAA,eAAA,eAAA;AACA,YAAA,kBAAA,yBAAA;AAEAA,8BAAA,kBAAA,eAAA;AAGA,4BAAA;AAGA,gBAAA,oBAAA;AACA;AAAA,QACA;AAAA,MACA,SAAA,GAAA;AACAA,sBAAA,MAAA,MAAA,QAAA,6CAAA,eAAA,CAAA;AAAA,MACA;AAGA;AACA;IACA,CAAA;AAEAQ,kBAAAA,kBAAA,YAAA;AACA,YAAA,UAAA;AACA,iBAAA,MAAA;AACAR,sBAAA,MAAA,oBAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA,CAAA;AAEAS,kBAAAA,kBAAA,MAAA;AACA,aAAA;IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnoBA,GAAG,WAAW,eAAe;"}