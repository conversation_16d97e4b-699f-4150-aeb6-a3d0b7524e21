{"version": 3, "file": "index.js", "sources": ["pages/profile/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZmlsZS9pbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"my-page\">\n    <!-- 顶部背景和头像/登录按钮区域 -->\n<view class=\"header-bg\">\n    <!-- 背景图片 -->\n    <image class=\"header-bg-image\" :src=\"headerBgUrl\" mode=\"aspectFill\"></image>\n    <view class=\"user-profile-box\">\n        <view class=\"avatar-container\">\n            <up-avatar \n                :size=\"54\"  \n                :src=\"displayAvatarUrl\"\n                @click=\"!isLoggedIn ? goToLogin : null\" \n            ></up-avatar>\n            <!-- 覆盖在头像上的透明按钮：微信端触发头像选择器 -->\n            <button\n              v-if=\"isLoggedIn\"\n              class=\"avatar-choose-btn\"\n              open-type=\"chooseAvatar\"\n              @chooseavatar=\"onChooseAvatar\"\n            ></button>\n        </view>\n        <view class=\"user-text-box\">\n            <view v-if=\"isLoggedIn\" class=\"user-info-container\">\n                <view class=\"username-row\">\n                    <text class=\"username-text\">{{ getUserDisplayName }}</text>\n                    <image class=\"edit-icon\" :src=\"editIconUrl\" mode=\"aspectFit\" @click=\"handleEditNickname\"></image>\n                </view>\n            </view>\n            <view v-else class=\"login-button-container\">\n                <text class=\"username-text\" @click=\"goToLogin\">请登录</text>\n            </view>\n        </view>\n    </view>\n\n    <!-- 报名订单卡片 -->\n    <view class=\"order-card-container\" @click=\"handleOrderCardClick\">\n        <!-- 背景图片 -->\n        <image class=\"order-card-bg-image\" :src=\"orderCardBgUrl\" mode=\"aspectFill\"></image>\n        <view class=\"order-card-content\">\n            <view class=\"order-card-left\">\n                <text class=\"order-card-text\">报名订单</text>\n            </view>\n            <view class=\"order-card-right\">\n                <view class=\"order-card-action-wrapper\">\n                    <text class=\"order-card-action\">查看</text>\n                    <image class=\"order-card-arrow\" :src=\"orderArrowUrl\" mode=\"aspectFit\"></image>\n                </view>\n            </view>\n        </view>\n    </view>\n</view>\n\n    <!-- 简化菜单区域 -->\n    <view class=\"menu-list-card\">\n      <up-cell-group :border=\"false\">\n        <!-- 绑定手机号，根据登录状态显示值 -->\n        <up-cell title=\"绑定手机号\" :value=\"getPhoneDisplay\" :isLink=\"false\" :border=\"false\">\n          <template #icon>\n            <image class=\"menu-icon\" :src=\"phoneIconUrl\" mode=\"aspectFit\"></image>\n          </template>\n        </up-cell>\n        <up-cell title=\"隐私政策\" :isLink=\"true\" arrow-direction=\"right\" :border=\"false\" @click=\"handleNavigate('/pages_sub/pages_other/policy?type=privacy_policy')\">\n          <template #icon>\n            <image class=\"menu-icon\" :src=\"privacyIconUrl\" mode=\"aspectFit\"></image>\n          </template>\n        </up-cell>\n        <up-cell title=\"用户协议\" :isLink=\"true\" arrow-direction=\"right\" :border=\"false\" @click=\"handleNavigate('/pages_sub/pages_other/policy?type=user_agreement')\">\n          <template #icon>\n            <image class=\"menu-icon\" :src=\"contractIconUrl\" mode=\"aspectFit\"></image>\n          </template>\n        </up-cell>\n        <!-- <up-cell title=\"注销账号\" :isLink=\"true\" arrow-direction=\"right\" :border=\"false\" @click=\"handleDeleteAccountClick\">\n          <template #icon>\n            <image class=\"menu-icon\" :src=\"deleteIconUrl\" mode=\"aspectFit\"></image>\n          </template>\n        </up-cell> -->\n      </up-cell-group>\n    </view>\n\t\n\t<view v-if=\"isLoggedIn\" class=\"logout-button-wrapper\">\n\t  <view class=\"custom-logout-btn\" @click=\"logout\">\n\t    退出登录\n\t  </view>\n\t</view>\n    \n    <!-- 自定义底部导航栏 -->\n    <CustomTabBar :current=\"4\" />\n    \n    <!-- 昵称编辑弹窗 -->\n    <view v-if=\"showNicknameEditModal\" class=\"nickname-modal-overlay\" @click=\"cancelNicknameEdit\">\n      <view class=\"nickname-modal\" @click.stop>\n        <view class=\"nickname-modal-header\">\n          <text class=\"nickname-modal-title\">修改昵称</text>\n        </view>\n        <view class=\"nickname-modal-content\">\n          <input \n            v-model=\"tempNickname\" \n            class=\"nickname-input\" \n            placeholder=\"请输入新昵称\" \n            :maxlength=\"MAX_NICKNAME_LENGTH\"\n            focus\n          />\n          <text class=\"nickname-length-tip\">{{ tempNickname.length }}/{{ MAX_NICKNAME_LENGTH }}</text>\n        </view>\n        <view class=\"nickname-modal-footer\">\n          <view class=\"nickname-btn nickname-btn-cancel\" @click=\"cancelNicknameEdit\">\n            取消\n          </view>\n          <view class=\"nickname-btn nickname-btn-confirm\" @click=\"confirmNicknameEdit\">\n            确定\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { onShow, onLoad, onShareAppMessage } from '@dcloudio/uni-app'\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue'\nimport { deleteAccountApi, updateUserInfoApi } from '@/api/data/user.js'\nimport { BASE_URL } from '@/utils/config.js'\n\nconst MAX_NICKNAME_LENGTH = 15\n\n// 定义页面名称\ndefineOptions({\n  name: 'ProfileIndex'\n})\n\n// 响应式数据\nconst isLoggedIn = ref(false) // 初始登录状态\nconst userInfo = ref(null) // 用户信息\n// 默认头像与其他图片URL （仅暗号映射，不再使用本地兜底）\nconst defaultAvatarUrl = ref('')\n// 昵称编辑弹窗相关\nconst showNicknameEditModal = ref(false)\nconst tempNickname = ref('')\n\n/**\n * 头像显示URL计算属性\n * 统一管理头像显示逻辑，确保在所有场景下都能正确显示头像\n * @returns {string} 最终应显示的头像URL\n */\nconst displayAvatarUrl = computed(() => {\n  // 规则1: 如果用户已登录，并且拥有一个有效的（非空）头像URL，则使用用户的头像\n  if (isLoggedIn.value && userInfo.value && userInfo.value.avatarUrl) {\n    return userInfo.value.avatarUrl\n  }\n  \n  // 规则2: 在所有其他情况下（未登录、或已登录但无头像），都回退到使用预设的默认头像\n  return defaultAvatarUrl.value\n})\nconst headerBgUrl = ref('')\nconst editIconUrl = ref('') \nconst orderArrowUrl = ref('') \nconst phoneIconUrl = ref('') \nconst contractIconUrl = ref('') \nconst privacyIconUrl = ref('') \nconst deleteIconUrl = ref('') \nconst orderCardBgUrl = ref('') \n\n// 解析静态资源缓存：仅读取缓存暗号\nconst resolveAssetUrl = (assetKey) => {\n  const assets = uni.getStorageSync('staticAssets')\n  return (assets && assets[assetKey]) ? assets[assetKey] : ''\n}\n\n// 刷新默认头像\nconst refreshDefaultAvatar = () => {\n  defaultAvatarUrl.value = resolveAssetUrl('default_avatar')\n}\n\n// 刷新页面内其余静态资源\nconst refreshProfileAssets = () => {\n  headerBgUrl.value = resolveAssetUrl('mybg')\n  editIconUrl.value = resolveAssetUrl('my_edit')\n  orderArrowUrl.value = resolveAssetUrl('group_right')\n  phoneIconUrl.value = resolveAssetUrl('my_phone')\n  contractIconUrl.value = resolveAssetUrl('my_contract')\n  privacyIconUrl.value = resolveAssetUrl('my_personal')\n  deleteIconUrl.value = resolveAssetUrl('my_delete')\n  orderCardBgUrl.value = resolveAssetUrl('order-card-bg')\n}\n\n// 计算属性\nconst getUserDisplayName = computed(() => {\n  if (!userInfo.value) {\n    return '用户' // 默认显示\n  }\n  \n  // 优先显示昵称，其次显示手机号，最后显示默认名称\n  if (userInfo.value.nickname) {\n    return userInfo.value.nickname\n  }\n  \n  if (userInfo.value.phoneNumber) {\n    // 如果有手机号，显示格式化的手机号\n    const phone = userInfo.value.phoneNumber\n    if (phone.length === 11) {\n      return phone.substring(0, 3) + '****' + phone.substring(7)\n    }\n    return phone\n  }\n  \n  return '用户' // 默认显示\n})\n\nconst getPhoneDisplay = computed(() => {\n  if (!isLoggedIn.value) {\n    return '' // 未登录时不显示文案\n  }\n  \n  // 检查手机号字段（可能是phone或phoneNumber）\n  const phone = userInfo.value?.phone || userInfo.value?.phoneNumber\n  \n  if (!phone) {\n    return '未绑定' // 已登录但没有手机号\n  }\n  \n  // 格式化手机号显示（中间4位用*号替换）\n  if (phone.length === 11) {\n    return phone.substring(0, 3) + '****' + phone.substring(7)\n  }\n  \n  // 如果手机号格式不标准，直接显示\n  return phone\n})\n\n// 方法定义\n// 统一的导航守卫方法\nconst handleNavigate = (url) => {\n  // 检查登录状态\n  if (!isLoggedIn.value) {\n    // 未登录时，跳转到登录页\n    console.log('用户未登录，跳转到登录页')\n    goToLogin()\n    return\n  }\n  \n  // 已登录时，正常跳转到目标页面\n  console.log('用户已登录，跳转到:', url)\n  uni.navigateTo({\n    url: url,\n    fail: (err) => {\n      console.error('页面跳转失败:', err)\n      uni.showToast({\n        title: '页面跳转失败',\n        icon: 'none',\n        duration: 2000\n      })\n    }\n  })\n}\n\n// 报名订单卡片点击处理\nconst handleOrderCardClick = () => {\n  if (!isLoggedIn.value) {\n    console.log('点击报名订单卡片，需要先登录')\n    goToLogin()\n  } else {\n    console.log('点击报名订单卡片，跳转到订单页面')\n    uni.navigateTo({\n      url: '/pages_sub/pages_profile/orders',\n      fail: (err) => {\n        console.error('跳转订单页面失败:', err)\n        uni.showToast({\n          title: '页面跳转失败',\n          icon: 'none',\n          duration: 2000\n        })\n      }\n    })\n  }\n}\n\n\n// 注销账号点击处理\nconst handleDeleteAccountClick = () => {\n  if (!isLoggedIn.value) {\n    console.log('注销账号需要先登录')\n    goToLogin()\n    return\n  }\n  \n  // 已登录时，显示注销确认弹窗\n  uni.showModal({\n    title: '注销账号',\n    content: '注销后账号将被标记为已注销，确定要继续吗？',\n    confirmText: '确定注销',\n    cancelText: '取消',\n    success: (res) => {\n      if (res.confirm) {\n        // 用户确认注销，调用注销方法\n        console.log('用户确认注销账号')\n        confirmDeleteAccount()\n      }\n    }\n  })\n}\n\n// 确认注销账号的实际逻辑\nconst confirmDeleteAccount = async () => {\n  console.log('=== 开始执行注销账号流程 ===')\n  \n  // 显示加载提示\n  uni.showLoading({ title: '正在注销...' })\n  \n  try {\n    console.log('调用后端注销接口...')\n    const result = await deleteAccountApi()\n    \n    console.log('注销接口调用成功:', result)\n    \n    // 关闭加载提示\n    uni.hideLoading()\n    \n    // 显示成功提示\n    uni.showToast({\n      title: '账号已成功注销',\n      icon: 'success',\n      duration: 2000\n    })\n    \n    // 清空本地数据但不显示额外提示（注销成功提示已经显示过了）\n    clearUserDataSilently()\n    \n    console.log('注销账号流程完成')\n    \n  } catch (error) {\n    console.error('注销账号过程中发生错误:', error)\n    \n    // 关闭加载提示\n    uni.hideLoading()\n    \n    // 显示错误提示\n    uni.showToast({\n      title: '注销失败，请稍后再试',\n      icon: 'none',\n      duration: 3000\n    })\n    \n    // 显示详细错误信息（开发调试用）\n    console.error('注销失败详情:', {\n      message: error.message,\n      stack: error.stack\n    })\n  }\n}\n\n// 检查登录状态的方法\nconst checkLoginStatus = () => {\n  // 检查实际的token和用户信息\n  const token = uni.getStorageSync('token')\n  const userInfoData = uni.getStorageSync('userInfo')\n  \n  console.log('从本地存储获取的token:', token)\n  console.log('从本地存储获取的userInfo:', userInfoData)\n  \n  const newLoginStatus = !!token\n  console.log('计算出的登录状态:', newLoginStatus)\n  console.log('当前页面登录状态:', isLoggedIn.value)\n  \n  // 强制更新登录状态和用户信息\n  isLoggedIn.value = newLoginStatus\n  userInfo.value = userInfoData || null\n  \n  if (isLoggedIn.value && userInfoData) {\n    // 如果已登录且有用户信息，可以在这里设置用户数据\n    console.log('用户已登录，用户信息:', userInfoData)\n    if (userInfoData.phoneNumber) {\n      console.log('用户手机号:', userInfoData.phoneNumber)\n    }\n  } else if (isLoggedIn.value && !userInfoData) {\n    console.log('有token但无用户信息')\n  } else {\n    console.log('用户未登录')\n  }\n}\n\n// 点击\"请登录\"按钮跳转到登录页面\nconst goToLogin = () => {\n  uni.navigateTo({\n    url: '/pages_sub/pages_other/login'\n  })\n}\n\n// 点击\"退出登录\"按钮\nconst logout = (isDeleteAccount = false) => {\n  // 如果是注销账号操作，直接清除数据，不显示确认弹窗\n  if (isDeleteAccount) {\n    clearUserData('已退出登录')\n    return\n  }\n  \n  // 正常退出登录时显示确认弹窗\n  uni.showModal({\n    title: '提示',\n    content: '确定要退出登录吗？',\n    success: (res) => {\n      if (res.confirm) {\n        clearUserData('已退出登录')\n      }\n    }\n  })\n}\n\n// 编辑昵称点击处理\nconst handleEditNickname = () => {\n  if (!isLoggedIn.value) {\n    console.log('编辑昵称需要先登录')\n    goToLogin()\n    return\n  }\n  \n  // 显示自定义昵称编辑弹窗\n  showNicknameEditModal.value = true\n  tempNickname.value = userInfo.value?.nickname || ''\n}\n\n// 确认修改昵称\nconst confirmNicknameEdit = async () => {\n  const newNickname = tempNickname.value.trim()\n  \n  if (!newNickname) {\n    uni.showToast({\n      title: '昵称不能为空',\n      icon: 'none',\n      duration: 2000\n    })\n    return\n  }\n  \n  // 关闭弹窗\n  showNicknameEditModal.value = false\n  \n  // 调用API更新昵称\n  await updateNickname(newNickname)\n}\n\n// 取消修改昵称\nconst cancelNicknameEdit = () => {\n  showNicknameEditModal.value = false\n  tempNickname.value = ''\n}\n\n// 更新昵称的方法\nconst updateNickname = async (newNickname) => {\n  console.log('=== 开始更新昵称 ===')\n  \n  // 显示加载提示\n  uni.showLoading({ title: '正在更新...' })\n  \n  try {\n    console.log('调用updateUserInfoApi更新昵称:', newNickname)\n    const result = await updateUserInfoApi({\n      nickname: newNickname\n    })\n    \n    console.log('昵称更新接口调用成功:', result)\n    \n    // 关闭加载提示\n    uni.hideLoading()\n    \n    // 更新本地用户信息\n    if (userInfo.value) {\n      userInfo.value.nickname = newNickname\n      // 同时更新本地存储\n      uni.setStorageSync('userInfo', userInfo.value)\n    }\n    \n    // 显示成功提示\n    uni.showToast({\n      title: '昵称修改成功',\n      icon: 'success',\n      duration: 2000\n    })\n    \n    console.log('昵称更新完成')\n    \n  } catch (error) {\n    console.error('更新昵称过程中发生错误:', error)\n    \n    // 关闭加载提示\n    uni.hideLoading()\n    \n    // 显示错误提示\n    uni.showToast({\n      title: error.message || '更新失败，请稍后再试',\n      icon: 'none',\n      duration: 3000\n    })\n    \n    console.error('昵称更新失败详情:', {\n      message: error.message,\n      stack: error.stack\n    })\n  }\n}\n\n// 清除用户数据的统一方法\nconst clearUserData = (message) => {\n  console.log('=== 开始清除用户数据 ===')\n  \n  // 清除token和用户信息\n  uni.removeStorageSync('token')\n  uni.removeStorageSync('userInfo')\n  \n  // 更新页面状态\n  isLoggedIn.value = false\n  userInfo.value = null\n  \n  // 显示提示信息\n  if (message) {\n    uni.showToast({\n      title: message,\n      icon: 'success',\n      duration: 1500\n    })\n  }\n  \n  console.log('用户数据已清除，UI已更新')\n}\n\n// 静默清除用户数据（不显示提示）\nconst clearUserDataSilently = () => {\n  console.log('=== 静默清除用户数据 ===')\n  \n  // 清除token和用户信息\n  uni.removeStorageSync('token')\n  uni.removeStorageSync('userInfo')\n  \n  // 更新页面状态\n  isLoggedIn.value = false\n  userInfo.value = null\n  \n  console.log('用户数据已静默清除，UI已更新')\n}\n\n// 已移除模拟登录相关逻辑\n\n// 生命周期钩子\nonShow(() => {\n  // 页面显示时隐藏原生 tabBar\n  uni.hideTabBar();\n  // 添加小延迟确保数据已保存\n  setTimeout(() => {\n    checkLoginStatus()\n    refreshDefaultAvatar()\n    refreshProfileAssets()\n  }, 100)\n})\n\nonLoad(() => {\n  console.log('个人中心页 onLoad - 页面首次加载');\n  uni.showShareMenu({\n      menus: ['shareAppMessage', 'shareTimeline']\n    });\n\n  checkLoginStatus()\n  refreshDefaultAvatar()\n  refreshProfileAssets()\n})\n\n// 选择头像回调（仅微信端可用）\nconst onChooseAvatar = (e) => {\n  const tempPath = e?.detail?.avatarUrl\n  if (!tempPath) return\n  uploadAvatar(tempPath)\n}\n\n// 上传并保存头像\nconst uploadAvatar = async (filePath) => {\n  if (!isLoggedIn.value) {\n    goToLogin()\n    return\n  }\n  const token = uni.getStorageSync('token')\n  if (!token) {\n    goToLogin()\n    return\n  }\n\n  uni.showLoading({ title: '上传中...' })\n  try {\n    await new Promise((resolve, reject) => {\n      uni.uploadFile({\n        url: BASE_URL + '/common/upload',\n        filePath,\n        name: 'file',\n        header: {\n          Authorization: 'Bearer ' + token\n        },\n        success: async (res) => {\n          try {\n            const data = JSON.parse(res.data || '{}')\n            if (res.statusCode === 200 && data.code === 200 && data.url) {\n              const url = data.url\n              await updateUserInfoApi({ avatarUrl: url })\n              if (userInfo.value) {\n                userInfo.value.avatarUrl = url\n                uni.setStorageSync('userInfo', userInfo.value)\n              }\n              uni.showToast({ title: '头像已更新', icon: 'success' })\n              resolve(true)\n            } else {\n              uni.showToast({ title: '上传失败', icon: 'none' })\n              reject(new Error('upload error'))\n            }\n          } catch (err) {\n            uni.showToast({ title: '响应解析失败', icon: 'none' })\n            reject(err)\n          }\n        },\n        fail: (err) => {\n          uni.showToast({ title: '上传失败', icon: 'none' })\n          reject(err)\n        },\n        complete: () => {\n          uni.hideLoading()\n        }\n      })\n    })\n  } catch (e) {\n    // 已提示\n  }\n}\n\nonShareAppMessage(() => {\n  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.my-page {\n  background-color: #F5F5F5;\n  min-height: 100vh;\n}\n\n/* --- 顶部区域 --- */\n.header-bg {\n    position: relative;\n    height: 452rpx;\n}\n\n.header-bg-image {\n    position: absolute;\n    top:0;\n    left: 0;\n    width: 100%;\n    height: 452rpx;\n    z-index: 1;\n}\n\n.user-profile-box {\n    position: absolute;\n    /* 使用 calc() 动态计算正确的垂直位置 */\n    top: calc(186rpx + var(--status-bar-height));\n    /* 使用我们确认的左边距 */\n    left: 32rpx;\n    right: 40rpx; /* 增加一个右边距，防止编辑按钮贴边 */\n    z-index: 2;\n    display: flex;\n    align-items: center;\n}\n\n.avatar-container {\n  position: relative;\n}\n\n/* 覆盖在头像上的透明按钮，保持可点击 */\n.avatar-choose-btn {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 54px;\n  height: 54px;\n  background: transparent;\n  border: none;\n  padding: 0;\n  opacity: 0;\n}\n\n.user-text-box {\n    display: flex;\n    flex-direction: column;\n    margin-left: 20rpx;\n    color: #ffffff;\n}\n\n.user-info-container {\n    display: flex;\n    flex-direction: column;\n    align-items: flex-start;\n}\n\n.username-row {\n    display: flex;\n    align-items: center;\n}\n\n.username-text {\n    max-width: 200rpx;\n    height: 52rpx;\n    font-weight: normal;\n    font-size: 36rpx;\n    color: #FFFFFF;\n    line-height: 52rpx;\n    text-align: left;\n    font-style: normal;\n    text-transform: none;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n}\n\n\n\n\n\n.edit-icon-box {\n    display: none;\n}\n\n.edit-icon {\n    width: 28rpx;\n    height: 28rpx;\n    margin-left: 16rpx;\n}\n\n/* --- 报名订单卡片 --- */\n.order-card-container {\n    position: absolute;\n    top: calc(348rpx + var(--status-bar-height));\n    left: 24rpx;\n    right: 24rpx;\n    height: 116rpx;\n    box-sizing: border-box;\n    z-index: 3;\n    margin: 0;\n    border-radius: 16rpx;\n    overflow: hidden;\n}\n\n.order-card-bg-image {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    z-index: 1;\n    border-radius: 16rpx;\n}\n\n.order-card-content {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 32rpx;\n    height: 100%;\n    z-index: 2;\n}\n\n.order-card-left {\n\tmargin-left:68rpx;\n    display: flex;\n    align-items: center;\n}\n\n.order-card-text {\n    font-family: \"Alibaba PuHuiTi Medium\", \"Alibaba PuHuiTi\", sans-serif;\n    color: #452D03 ;\n    font-size: 28rpx;\n    font-weight: 500;\n    margin-left: 16rpx;\n}\n\n.order-card-right {\n    display: flex;\n    align-items: center;\n}\n\n.order-card-action-wrapper {\n    position: relative;\n    display: flex;\n    align-items: center;\n    width: 116rpx;\n    height: 40rpx;\n    border-radius: 20rpx 20rpx 20rpx 20rpx;\n    border: 1rpx solid #E69D3A;\n    background-color: rgba(255, 255, 255, 0.1);\n}\n\n.order-card-action {\n    position: absolute;\n    left: 20rpx;\n    height: 36rpx;\n    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\n    font-weight: normal;\n    font-size: 24rpx;\n    color: #452D03;\n    line-height: 36rpx;\n    font-style: normal;\n    text-transform: none;\n}\n\n.order-card-arrow {\n\tposition: absolute;\n\tright: 16rpx;\n\ttop: 50%;\n\ttransform: translateY(-50%);\n\twidth: 32rpx;\n\theight: 32rpx;\n}\n\n/* --- 昵称编辑弹窗 --- */\n.nickname-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.nickname-modal {\n  width: 600rpx;\n  background-color: #ffffff;\n  border-radius: 24rpx;\n  overflow: hidden;\n}\n\n.nickname-modal-header {\n  padding: 40rpx 32rpx 20rpx 32rpx;\n  text-align: center;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.nickname-modal-title {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333333;\n}\n\n.nickname-modal-content {\n  padding: 40rpx 32rpx;\n}\n\n.nickname-input {\n  width: 100%;\n  height: 80rpx;\n  padding: 0 20rpx;\n  border: 2rpx solid #e0e0e0;\n  border-radius: 12rpx;\n  font-size: 28rpx;\n  color: #333333;\n  background-color: #fafafa;\n  box-sizing: border-box;\n}\n\n.nickname-input:focus {\n  border-color: #007aff;\n  background-color: #ffffff;\n}\n\n.nickname-length-tip {\n  display: block;\n  margin-top: 16rpx;\n  font-size: 24rpx;\n  color: #999999;\n  text-align: right;\n}\n\n.nickname-modal-footer {\n  display: flex;\n  border-top: 1rpx solid #f0f0f0;\n}\n\n.nickname-btn {\n  flex: 1;\n  height: 88rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32rpx;\n  cursor: pointer;\n}\n\n.nickname-btn-cancel {\n  color: #666666;\n  border-right: 1rpx solid #f0f0f0;\n}\n\n.nickname-btn-cancel:active {\n  background-color: #f5f5f5;\n}\n\n.nickname-btn-confirm {\n  color: #007aff;\n  font-weight: 500;\n}\n\n.nickname-btn-confirm:active {\n  background-color: #f0f8ff;\n}\n\n/* --- 白色列表 --- */\n.menu-list-card {\n    background-color: #ffffff;\n    margin: 92rpx 24rpx 0 24rpx;\n    border-radius: 20rpx;\n    padding: 10rpx 0;\n    position: relative;\n    z-index: 2;\n}\n\n/* 自定义up-cell标题文字样式 */\n:deep(.u-cell__title-text) {\n  font-family: \"Alibaba PuHuiTi\", sans-serif !important;\n  font-weight: 400 !important;\n  font-size: 28rpx !important;\n  color: #23232A !important;\n}\n\n.menu-icon {\n    width: 48rpx;\n    height: 48rpx;\n    margin-right: 20rpx;\n}\n\n.logout-button-wrapper {\n  padding: 24rpx 0;\n  margin: 0 24rpx; \n}\n\n.custom-logout-btn {\n  width: 702rpx;\n  height: 76rpx;\n  background: #FFFFFF;\n  border-radius: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  color: #333333;\n  \n  &:active {\n    background-color: #f5f5f5;\n  }\n}\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/profile/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "updateUserInfoApi", "onShow", "onLoad", "BASE_URL", "onShareAppMessage"], "mappings": ";;;;;;;;;;;;;;;;AAwHA,MAAA,eAAA,MAAA;AAIA,MAAA,sBAAA;;;;;;AAQA,UAAA,aAAAA,cAAA,IAAA,KAAA;AACA,UAAA,WAAAA,cAAA,IAAA,IAAA;AAEA,UAAA,mBAAAA,cAAA,IAAA,EAAA;AAEA,UAAA,wBAAAA,cAAA,IAAA,KAAA;AACA,UAAA,eAAAA,cAAA,IAAA,EAAA;AAOA,UAAA,mBAAAC,cAAA,SAAA,MAAA;AAEA,UAAA,WAAA,SAAA,SAAA,SAAA,SAAA,MAAA,WAAA;AACA,eAAA,SAAA,MAAA;AAAA,MACA;AAGA,aAAA,iBAAA;AAAA,IACA,CAAA;AACA,UAAA,cAAAD,cAAA,IAAA,EAAA;AACA,UAAA,cAAAA,cAAA,IAAA,EAAA;AACA,UAAA,gBAAAA,cAAA,IAAA,EAAA;AACA,UAAA,eAAAA,cAAA,IAAA,EAAA;AACA,UAAA,kBAAAA,cAAA,IAAA,EAAA;AACA,UAAA,iBAAAA,cAAA,IAAA,EAAA;AACA,UAAA,gBAAAA,cAAA,IAAA,EAAA;AACA,UAAA,iBAAAA,cAAA,IAAA,EAAA;AAGA,UAAA,kBAAA,CAAA,aAAA;AACA,YAAA,SAAAE,cAAAA,MAAA,eAAA,cAAA;AACA,aAAA,UAAA,OAAA,QAAA,IAAA,OAAA,QAAA,IAAA;AAAA,IACA;AAGA,UAAA,uBAAA,MAAA;AACA,uBAAA,QAAA,gBAAA,gBAAA;AAAA,IACA;AAGA,UAAA,uBAAA,MAAA;AACA,kBAAA,QAAA,gBAAA,MAAA;AACA,kBAAA,QAAA,gBAAA,SAAA;AACA,oBAAA,QAAA,gBAAA,aAAA;AACA,mBAAA,QAAA,gBAAA,UAAA;AACA,sBAAA,QAAA,gBAAA,aAAA;AACA,qBAAA,QAAA,gBAAA,aAAA;AACA,oBAAA,QAAA,gBAAA,WAAA;AACA,qBAAA,QAAA,gBAAA,eAAA;AAAA,IACA;AAGA,UAAA,qBAAAD,cAAA,SAAA,MAAA;AACA,UAAA,CAAA,SAAA,OAAA;AACA,eAAA;AAAA,MACA;AAGA,UAAA,SAAA,MAAA,UAAA;AACA,eAAA,SAAA,MAAA;AAAA,MACA;AAEA,UAAA,SAAA,MAAA,aAAA;AAEA,cAAA,QAAA,SAAA,MAAA;AACA,YAAA,MAAA,WAAA,IAAA;AACA,iBAAA,MAAA,UAAA,GAAA,CAAA,IAAA,SAAA,MAAA,UAAA,CAAA;AAAA,QACA;AACA,eAAA;AAAA,MACA;AAEA,aAAA;AAAA,IACA,CAAA;AAEA,UAAA,kBAAAA,cAAA,SAAA,MAAA;;AACA,UAAA,CAAA,WAAA,OAAA;AACA,eAAA;AAAA,MACA;AAGA,YAAA,UAAA,cAAA,UAAA,mBAAA,YAAA,cAAA,UAAA,mBAAA;AAEA,UAAA,CAAA,OAAA;AACA,eAAA;AAAA,MACA;AAGA,UAAA,MAAA,WAAA,IAAA;AACA,eAAA,MAAA,UAAA,GAAA,CAAA,IAAA,SAAA,MAAA,UAAA,CAAA;AAAA,MACA;AAGA,aAAA;AAAA,IACA,CAAA;AAIA,UAAA,iBAAA,CAAA,QAAA;AAEA,UAAA,CAAA,WAAA,OAAA;AAEAC,sBAAAA,MAAA,MAAA,OAAA,kCAAA,cAAA;AACA,kBAAA;AACA;AAAA,MACA;AAGAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,cAAA,GAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA;AAAA,QACA,MAAA,CAAA,QAAA;AACAA,wBAAAA,MAAA,MAAA,SAAA,kCAAA,WAAA,GAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,YACA,UAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,uBAAA,MAAA;AACA,UAAA,CAAA,WAAA,OAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,gBAAA;AACA,kBAAA;AAAA,MACA,OAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,kBAAA;AACAA,sBAAAA,MAAA,WAAA;AAAA,UACA,KAAA;AAAA,UACA,MAAA,CAAA,QAAA;AACAA,0BAAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,GAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,MAAA;AAAA,cACA,UAAA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AA6EA,UAAA,mBAAA,MAAA;AAEA,YAAA,QAAAA,cAAAA,MAAA,eAAA,OAAA;AACA,YAAA,eAAAA,cAAAA,MAAA,eAAA,UAAA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,kBAAA,KAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,qBAAA,YAAA;AAEA,YAAA,iBAAA,CAAA,CAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,cAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,WAAA,KAAA;AAGA,iBAAA,QAAA;AACA,eAAA,QAAA,gBAAA;AAEA,UAAA,WAAA,SAAA,cAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,eAAA,YAAA;AACA,YAAA,aAAA,aAAA;AACAA,wBAAA,MAAA,MAAA,OAAA,kCAAA,UAAA,aAAA,WAAA;AAAA,QACA;AAAA,MACA,WAAA,WAAA,SAAA,CAAA,cAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,cAAA;AAAA,MACA,OAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,OAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,YAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,SAAA,CAAA,kBAAA,UAAA;AAEA,UAAA,iBAAA;AACA,sBAAA,OAAA;AACA;AAAA,MACA;AAGAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,SAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACA,cAAA,IAAA,SAAA;AACA,0BAAA,OAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,qBAAA,MAAA;;AACA,UAAA,CAAA,WAAA,OAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,WAAA;AACA,kBAAA;AACA;AAAA,MACA;AAGA,4BAAA,QAAA;AACA,mBAAA,UAAA,cAAA,UAAA,mBAAA,aAAA;AAAA,IACA;AAGA,UAAA,sBAAA,YAAA;AACA,YAAA,cAAA,aAAA,MAAA,KAAA;AAEA,UAAA,CAAA,aAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAGA,4BAAA,QAAA;AAGA,YAAA,eAAA,WAAA;AAAA,IACA;AAGA,UAAA,qBAAA,MAAA;AACA,4BAAA,QAAA;AACA,mBAAA,QAAA;AAAA,IACA;AAGA,UAAA,iBAAA,OAAA,gBAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,gBAAA;AAGAA,oBAAAA,MAAA,YAAA,EAAA,OAAA,UAAA,CAAA;AAEA,UAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,4BAAA,WAAA;AACA,cAAA,SAAA,MAAAC,gCAAA;AAAA,UACA,UAAA;AAAA,QACA,CAAA;AAEAD,sBAAAA,MAAA,MAAA,OAAA,kCAAA,eAAA,MAAA;AAGAA,sBAAAA,MAAA,YAAA;AAGA,YAAA,SAAA,OAAA;AACA,mBAAA,MAAA,WAAA;AAEAA,wBAAAA,MAAA,eAAA,YAAA,SAAA,KAAA;AAAA,QACA;AAGAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,QACA,CAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,QAAA;AAAA,MAEA,SAAA,OAAA;AACAA,sBAAAA,MAAA,MAAA,SAAA,kCAAA,gBAAA,KAAA;AAGAA,sBAAAA,MAAA,YAAA;AAGAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA,MAAA,WAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,QACA,CAAA;AAEAA,sBAAAA,MAAA,MAAA,SAAA,kCAAA,aAAA;AAAA,UACA,SAAA,MAAA;AAAA,UACA,OAAA,MAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,gBAAA,CAAA,YAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,kBAAA;AAGAA,oBAAA,MAAA,kBAAA,OAAA;AACAA,oBAAA,MAAA,kBAAA,UAAA;AAGA,iBAAA,QAAA;AACA,eAAA,QAAA;AAGA,UAAA,SAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,QACA,CAAA;AAAA,MACA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,eAAA;AAAA,IACA;AAoBAE,kBAAAA,OAAA,MAAA;AAEAF,oBAAA,MAAA,WAAA;AAEA,iBAAA,MAAA;AACA,yBAAA;AACA,6BAAA;AACA,6BAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA,CAAA;AAEAG,kBAAAA,OAAA,MAAA;AACAH,oBAAAA,MAAA,MAAA,OAAA,kCAAA,uBAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAEA,uBAAA;AACA,2BAAA;AACA,2BAAA;AAAA,IACA,CAAA;AAGA,UAAA,iBAAA,CAAA,MAAA;;AACA,YAAA,YAAA,4BAAA,WAAA,mBAAA;AACA,UAAA,CAAA;AAAA;AACA,mBAAA,QAAA;AAAA,IACA;AAGA,UAAA,eAAA,OAAA,aAAA;AACA,UAAA,CAAA,WAAA,OAAA;AACA,kBAAA;AACA;AAAA,MACA;AACA,YAAA,QAAAA,cAAAA,MAAA,eAAA,OAAA;AACA,UAAA,CAAA,OAAA;AACA,kBAAA;AACA;AAAA,MACA;AAEAA,oBAAAA,MAAA,YAAA,EAAA,OAAA,SAAA,CAAA;AACA,UAAA;AACA,cAAA,IAAA,QAAA,CAAA,SAAA,WAAA;AACAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAAI,aAAA,WAAA;AAAA,YACA;AAAA,YACA,MAAA;AAAA,YACA,QAAA;AAAA,cACA,eAAA,YAAA;AAAA,YACA;AAAA,YACA,SAAA,OAAA,QAAA;AACA,kBAAA;AACA,sBAAA,OAAA,KAAA,MAAA,IAAA,QAAA,IAAA;AACA,oBAAA,IAAA,eAAA,OAAA,KAAA,SAAA,OAAA,KAAA,KAAA;AACA,wBAAA,MAAA,KAAA;AACA,wBAAAH,gCAAA,EAAA,WAAA,KAAA;AACA,sBAAA,SAAA,OAAA;AACA,6BAAA,MAAA,YAAA;AACAD,kCAAAA,MAAA,eAAA,YAAA,SAAA,KAAA;AAAA,kBACA;AACAA,gCAAA,MAAA,UAAA,EAAA,OAAA,SAAA,MAAA,WAAA;AACA,0BAAA,IAAA;AAAA,gBACA,OAAA;AACAA,gCAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,QAAA;AACA,yBAAA,IAAA,MAAA,cAAA,CAAA;AAAA,gBACA;AAAA,cACA,SAAA,KAAA;AACAA,8BAAA,MAAA,UAAA,EAAA,OAAA,UAAA,MAAA,QAAA;AACA,uBAAA,GAAA;AAAA,cACA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACAA,4BAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,QAAA;AACA,qBAAA,GAAA;AAAA,YACA;AAAA,YACA,UAAA,MAAA;AACAA,4BAAAA,MAAA,YAAA;AAAA,YACA;AAAA,UACA,CAAA;AAAA,QACA,CAAA;AAAA,MACA,SAAA,GAAA;AAAA,MAEA;AAAA,IACA;AAEAK,kBAAAA,kBAAA,MAAA;AACA,aAAA;IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtnBA,GAAG,WAAW,eAAe;"}