<template>
  <view class="page-container">
    <view class="fixed-header" :style="{ height: headerHeight + 'px' }">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="custom-nav-bar" :style="{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }">
        <view class="nav-back-button" @click="goBack">
          <uni-icons type="left" color="#000000" size="22"></uni-icons>
        </view>
        <view class="nav-title">联系顾问</view>
      </view>
    </view>

    <scroll-view scroll-y :show-scrollbar="false" class="scrollable-content" :style="{ paddingTop: headerHeight + 'px' }">
      <view v-if="loading" class="loading-state">
        <uni-load-more status="loading" :show-icon="true"></uni-load-more>
      </view>

      <view v-if="!loading && consultant" class="content-wrapper">
        <view class="header-text">
          Hi~ 很高兴为您服务
        </view>

        <view class="card">
          <view class="consultant-info">
            <image class="avatar" :src="displayAvatarUrl" mode="aspectFill" @error="onImageError('avatar')"></image>
            <view class="details">
              <text class="name">{{ consultant.name }}</text>
              <view class="badge">官方</view>
            </view>
          </view>

          <text class="intro">{{ consultant.introduction }}</text>

          <view class="more-questions">
            更多问题，请添加顾问在线共解答
          </view>

          <view class="qr-code-section">
            <image class="qr-code" :src="displayQrCodeUrl" mode="aspectFit" @error="onImageError('qrCode')" @click="previewQrCode" :show-menu-by-long-press="true"></image>
            <text class="qr-code-tip">扫一扫</text>
          </view>
        </view>
      </view>

      <view v-if="!loading && !consultant" class="empty-state">
        <image class="empty-icon" src="/static/images/empty-data.png" mode="aspectFit"></image>
        <text class="empty-text">暂无在线顾问，请稍后再试</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { getFullImageUrl } from '@/utils/image.js';

import { onLoad, onShareAppMessage } from '@dcloudio/uni-app';
import { getDisplayConsultantApi } from '@/pages_sub/pages_profile/api/platform/consultant.js';

// --- 自定义导航栏相关逻辑 ---
const navBarPaddingBottomRpx = 20;
const navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);
const statusBarHeight = ref(0);
const navBarHeight = ref(0);
const headerHeight = ref(0);

const getNavBarInfo = () => {
  try {
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    statusBarHeight.value = menuButtonInfo.top;
    navBarHeight.value = menuButtonInfo.height;
    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;
  } catch (e) {
    const systemInfo = uni.getSystemInfoSync();
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    navBarHeight.value = 44;
    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;
  }
};

const goBack = () => {
  uni.navigateBack({ delta: 1 });
};

// --- 核心业务逻辑 ---
const loading = ref(true);
const consultant = ref(null);

const displayAvatarUrl = computed(() => {
  return consultant.value ? getFullImageUrl(consultant.value.avatarUrl) : '';
});

const displayQrCodeUrl = computed(() => {
  return consultant.value ? getFullImageUrl(consultant.value.qrCodeUrl) : '';
});

const fetchConsultantData = async () => {
  try {
    const res = await getDisplayConsultantApi();
    if (res.code === 200 && res.data) {
      consultant.value = res.data;
    } else {
      consultant.value = null;
    }
  } catch (error) {
    console.error('获取顾问信息失败:', error);
    consultant.value = null;
  } finally {
    loading.value = false;
  }
};

const previewQrCode = () => {
  if (displayQrCodeUrl.value) {
    uni.previewImage({
      urls: [displayQrCodeUrl.value]
    });
  }
};

const onImageError = (type) => {
  console.error(`${type} image failed to load.`);
  if (type === 'avatar') {
    consultant.value.avatarUrl = '/static/images/default-avatar.png';
  } else if (type === 'qrCode') {
    consultant.value.qrCodeUrl = '/static/images/default-qrcode.png';
  }
};

onLoad(() => {
  console.log('联系顾问页 onLoad - 页面首次加载');
  uni.showShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    });

  getNavBarInfo();
  loading.value = true;
  fetchConsultantData();
});

onShareAppMessage(() => {
  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为
});
</script>

<style lang="scss" scoped>
/* --- 页面布局及自定义导航栏样式 --- */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(180deg, #FFDEA1 0%, #FFFFFF 50%);
  font-style: normal;
  text-transform: none;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: transparent;
}

.scrollable-content {
  flex: 1;
  height: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 32rpx;
  box-sizing: border-box;
}

.status-bar {
  width: 100%;
}

.custom-nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: content-box;
}

.nav-back-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 15rpx;
}

.nav-title {
  font-size: 32rpx;
  color: #000000;
  line-height: 44rpx;
  font-family: 'Alibaba PuHuiTi Medium', 'Alibaba PuHuiTi', sans-serif;
}
/* --- 导航栏样式结束 --- */

.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 200rpx);
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
}

.empty-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #999;
}

.content-wrapper {
  /* 此容器用于在滚动区内实现原有的布局 */
}

.header-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #23232A;
  font-family: 'Alimama ShuHeiTi', 'Alimama ShuHeiTi', sans-serif;
  margin: 20rpx 20rpx 40rpx 45rpx;
}

.card {
  background: linear-gradient(180deg, #FFDEA1 0%, #FFBF51 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);

  width: 600rpx;
  height: 920rpx;
  margin: 20rpx auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.consultant-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  flex-shrink: 0;
  border: 4rpx solid #ffffff;
  box-sizing: border-box;
}

.details {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.scrollable-content::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* [核心修改] 使用文本溢出三件套来显示省略号 */
.name {
  color: #452D03;
  font-size: 28rpx;
  font-family: 'Alibaba PuHuiTi Bold', 'Alibaba PuHuiTi', sans-serif;
  font-weight: bold;
  text-align: left;
  margin-bottom: 8rpx; /* 稍微增加与徽章的间距 */

  /* 新增/修改的样式 */
  white-space: nowrap;      /* 强制不换行 */
  overflow: hidden;         /* 隐藏溢出部分 */
  text-overflow: ellipsis;  /* 显示省略号 */
}

.badge {
  width: 72rpx;
  height: 36rpx;
  background-color: #023F98;
  border-radius: 8rpx;
  align-self: flex-start;

  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: normal;
  font-style: normal;
  text-transform: none;
  line-height: normal;

  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.intro {
  font-size: 24rpx;
  color: #452D03;
  line-height: 48rpx;
  margin-top: 20rpx;
}

.more-questions {
  font-size: 28rpx;
  color: #023F98;
  margin-top: 60rpx;
  text-align: center;
}

.qr-code-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 48rpx;
}

.qr-code {
  width: 328rpx;
  height: 328rpx;
  background-color: #fff;
  border-radius: 30rpx;
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 8rpx solid #DAE6FF;
}

.qr-code-tip {
  margin-top: 48rpx;
  font-size: 24rpx;
  color: #452D03;
}
</style>