{"version": 3, "file": "index.js", "sources": ["pages/event/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZXZlbnQvaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"event-list-page\">\n    <!-- 1. 头部背景和导航栏区域 -->\n    <view class=\"header-wrapper\">\n      <!-- 背景图片 - 完全覆盖状态栏 -->\n      <image class=\"header-bg\" :src=\"eventBgUrl\" mode=\"aspectFill\"></image>\n\n      <!-- 自定义导航栏 -->\n      <view class=\"custom-navbar\">\n        <view class=\"navbar-title\">\n          <text class=\"title-text\">热门活动列表</text>\n        </view>\n      </view>\n\n      <!-- 第一行 (Subsection + Search Bar) -->\n      <view class=\"top-controls\">\n        <up-subsection :list=\"['列表', '日历']\" :current=\"currentTab\" @change=\"tabChange\" mode=\"subsection\"\n                       activeColor=\"#f56c6c\"></up-subsection>\n\n        <view class=\"search-wrapper\">\n          <CustomSearchBox\n              v-model=\"searchKeyword\"\n              placeholder=\"搜索活动\"\n              @search=\"onSearch\"\n              @input=\"debouncedSearch\"\n          ></CustomSearchBox>\n        </view>\n      </view>\n    </view>\n\n    <!-- 2.1 列表视图的筛选栏 -->\n    <EventFilter \n      v-if=\"currentTab === 0\"\n      :initial-filters=\"appliedFiltersList\"\n      :cities=\"otherCities\"\n      @apply-filters=\"handleFiltersApply\"\n    />\n\n    <!-- 2.2 活动列表视图 -->\n    <scroll-view v-if=\"currentTab === 0\" scroll-y class=\"event-list-scroll list-scroll-with-filter\"\n                 @scrolltolower=\"onLoadMore\"\n                 refresher-enabled :refresher-triggered=\"isRefreshing\" @refresherrefresh=\"onRefresh\">\n\n      <!-- 空状态 -->\n      <view v-if=\"!isLoading && eventList.length === 0\" class=\"empty-state\">\n\n        <!-- 重试按钮 -->\n        <view v-if=\"showRetry\" class=\"retry-container\">\n          <up-button type=\"primary\" size=\"normal\" @click=\"fetchEventList\">\n            重新加载\n          </up-button>\n        </view>\n      </view>\n\n      <!-- 活动卡片列表 -->\n      <EventCard\n        v-for=\"event in eventList\"\n        :key=\"event.id\"\n        :event=\"event\"\n        @click=\"goToDetail(event)\"\n      />\n\n      <!-- 加载更多组件 -->\n      <view class=\"loadmore-wrapper\">\n        <up-loadmore v-if=\"loadMoreStatus !== 'nomore'\" :status=\"loadMoreStatus\" :loading-text=\"'正在加载...'\" :loadmore-text=\"'上拉加载更多'\"\n                     :nomore-text=\"'没有更多了'\"/>\n        <NoMoreDivider v-if=\"loadMoreStatus === 'nomore'\" />\n      </view>\n    </scroll-view>\n\n    <!-- 2.3 日历视图的筛选栏 -->\n    <EventFilter \n      v-if=\"currentTab === 1\"\n      view-type=\"calendar\"\n      :initial-filters=\"appliedFiltersCalendar\"\n      :cities=\"otherCities\"\n      :notch-left=\"calendarNotchLeft\"\n      @apply-filters=\"handleCalendarFiltersApply\"\n      @update:notch-left=\"calendarNotchLeft = $event\"\n    />\n\n    <!-- 2.4 日历视图 -->\n    <scroll-view v-if=\"currentTab === 1\" scroll-y class=\"event-list-scroll calendar-view calendar-scroll-with-filter\"\n                 @scrolltolower=\"onLoadMore\" refresher-enabled :refresher-triggered=\"isRefreshing\"\n                 @refresherrefresh=\"onRefresh\">\n\n      <!-- 空状态 -->\n      <view v-if=\"!isLoading && groupedEvents.length === 0\" class=\"empty-state\">\n        <NoMoreDivider v-if=\"!showRetry\" />\n\n        <!-- 重试按钮 -->\n        <view v-if=\"showRetry\" class=\"retry-container\">\n          <up-button type=\"primary\" size=\"normal\" @click=\"fetchEventList\">\n            重新加载\n          </up-button>\n        </view>\n      </view>\n\n      <EventCalendarTimeline\n        v-else\n        :groups=\"groupedEvents\"\n        :has-more=\"pagination.hasMore\"\n        :is-loading=\"isLoading\"\n        :notch-left=\"calendarNotchLeft\"\n        @click-item=\"goToDetail\"\n      />\n\n    </scroll-view>\n\n\n\n    <!-- 自定义底部导航栏 -->\n    <CustomTabBar :current=\"2\"/>\n  </view>\n</template>\n\n<script setup>\nimport {\n  ref,\n  computed\n} from 'vue';\nimport {\n  onLoad,\n  onReachBottom,\n  onPullDownRefresh,\n  onUnload,\n  onShow,\n  onShareAppMessage\n} from '@dcloudio/uni-app';\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue';\nimport CustomSearchBox from '@/components/home/<USER>';\nimport EventCard from '@/components/event/EventCard.vue';\nimport EventCalendarTimeline from '@/components/event/EventCalendarTimeline.vue';\nimport EventFilter from '@/components/event/EventFilter.vue';\nimport NoMoreDivider from '@/components/common/NoMoreDivider.vue';\nimport {\n  getEventListApi,\n  getEventCitiesApi\n} from '@/api/data/event.js';\nimport {\n  formatEventStatus,\n  getStatusClass,\n  calculateRemainingSpots,\n  debounce\n} from '@/utils/tools.js';\nimport { formatEventDate, parseDate } from '@/utils/date.js';\nimport {\n  PAGE_CONFIG\n} from '@/utils/config.js';\nimport {getFullImageUrl} from '@/utils/image.js';\n\n// ==================== 响应式数据定义 ====================\nconst currentTab = ref(0); // 默认显示列表视图\nconst searchKeyword = ref('');\nconst eventList = ref([]);\nconst isLoading = ref(false);\nconst isRefreshing = ref(false);\nconst showRetry = ref(false);\n\n// 静态资源 URL（不再使用本地兜底）\nconst eventBgUrl = ref('');\n\n// 分页相关 - 日历视图初始加载20个，列表视图使用默认分页大小\nconst pagination = ref({\n  pageNum: 1,\n  pageSize: currentTab.value === 1 ? 20 : PAGE_CONFIG.DEFAULT_PAGE_SIZE, // 日历视图20个，列表视图使用默认\n  total: 0,\n  hasMore: true\n});\n\n// 已应用的筛选条件（列表与日历视图分离）\nconst appliedFiltersList = ref({\n  sortBy: 1,\n  location: 1,\n  timeRange: 1,\n  status: 1\n});\nconst appliedFiltersCalendar = ref({\n  location: 1,\n  timeRange: 1\n});\n\n// 日历视图 corner-notch 位置：默认指向\"热门地区\"60rpx\nconst calendarNotchLeft = ref('60rpx');\n\n// 其他城市数据（从API获取）\nconst otherCities = ref([]);\n\n// 合并的地区选项（仅日历视图需要）\nconst options2 = computed(() => [\n  allRegionOption.value,\n  ...hotCities.value,\n  ...otherCities.value.map((city, index) => ({\n    label: city,\n    value: 100 + index // 避免与热门城市ID冲突\n  }))\n]);\n\nconst options3 = ref([{\n  label: '全部时间',\n  value: 1\n},\n  {\n    label: '1周内',\n    value: 2\n  },\n  {\n    label: '1月内',\n    value: 3\n  },\n  {\n    label: '1年内',\n    value: 4\n  }\n]);\n\n// ==================== 计算属性 ====================\nconst loadMoreStatus = computed(() => {\n  if (isLoading.value) return 'loading';\n  if (!pagination.value.hasMore) return 'nomore';\n  return 'more';\n});\n\n/**\n * 按日期分组活动数据 - 用于日历视图\n */\nconst groupedEvents = computed(() => {\n  if (!eventList.value || eventList.value.length === 0) {\n    return [];\n  }\n  const groups = new Map();\n  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\n  eventList.value.forEach(event => {\n    const eventDate = parseDate(event.startTime);\n    // 💥 关键修改：处理不同年份的日期显示\n    const year = eventDate.getFullYear();\n    const currentYear = new Date().getFullYear();\n    const month = String(eventDate.getMonth() + 1).padStart(2, '0');\n    const day = String(eventDate.getDate()).padStart(2, '0');\n\n    let displayDate;\n    if (year !== currentYear) {\n      displayDate = `${year}年${month}月${day}日`;\n    } else {\n      displayDate = `${month}.${day}`;\n    }\n\n    // 避免使用 toISOString 造成的时区偏移，改用本地日期计算 key\n    const monthLocal = String(eventDate.getMonth() + 1).padStart(2, '0');\n    const dayLocal = String(eventDate.getDate()).padStart(2, '0');\n    const dateKey = `${eventDate.getFullYear()}-${monthLocal}-${dayLocal}`;\n    if (!groups.has(dateKey)) {\n      groups.set(dateKey, {\n        date: dateKey,\n        formattedDate: displayDate, // 使用新的日期格式\n        dayOfWeek: weekdays[eventDate.getDay()],\n        events: []\n      });\n    }\n    groups.get(dateKey).events.push(event);\n  });\n  return Array.from(groups.values());\n});\n\n/**\n * 日历视图显示的活动数据 - 支持滚动加载更多\n */\nconst limitedCalendarEvents = computed(() => {\n  // 直接返回所有已加载的活动，分页由后端控制\n  return groupedEvents.value;\n});\n\n/**\n * 检查是否有更多活动需要显示 - 使用分页信息判断\n */\nconst hasMoreCalendarEvents = computed(() => {\n  return pagination.value.hasMore;\n});\n\n// ==================== 核心方法 ====================\n\n/**\n * 获取活动城市列表\n */\nconst fetchEventCities = async () => {\n  try {\n    const response = await getEventCitiesApi();\n    \n    // 尝试不同的数据提取方式\n    let cityList = null;\n    if (Array.isArray(response)) {\n      cityList = response;\n    } else if (response && Array.isArray(response.data)) {\n      cityList = response.data;\n    } else if (response && response.code === 200 && Array.isArray(response.data)) {\n      cityList = response.data;\n    }\n    \n    console.log('提取的城市列表:', cityList);\n    \n    if (cityList && Array.isArray(cityList)) {\n      // 过滤掉热门城市中已有的城市\n      const hotCityNames = ['北京', '上海', '广州', '深圳'];\n      const filteredCities = cityList.filter(city => \n        city && city.trim() && !hotCityNames.includes(city.trim())\n      );\n      \n      otherCities.value = filteredCities;\n    } else {\n      uni.showToast({\n        title: '城市数据格式错误',\n        icon: 'none',\n        duration: 2000\n      });\n    }\n  } catch (error) {\n    console.error('获取城市列表失败:', error);\n    uni.showToast({\n      title: '获取城市列表失败',\n      icon: 'none',\n      duration: 2000\n    });\n  }\n};\n\n/**\n * 格式化活动地点 - 使用统一的地点格式化函数\n * 对于直辖市，会正确显示为\"上海市\"而不是\"上海\"\n */\nconst formatEventLocation = (event) => {\n  // 直辖市列表\n  const MUNICIPALITIES = ['北京', '上海', '天津', '重庆'];\n  \n  if (!event) return '待定';\n  \n  // 优先使用city字段\n  if (event.city && event.city.trim()) {\n    const city = event.city.trim();\n    \n    // 检查是否为直辖市\n    const isMunicipality = MUNICIPALITIES.some(municipality => \n      city.includes(municipality)\n    );\n    \n    if (isMunicipality) {\n      // 对于直辖市，如果有province字段且包含直辖市名称，使用province\n      if (event.province && event.province.trim()) {\n        const province = event.province.trim();\n        const municipalityMatch = MUNICIPALITIES.find(municipality => \n          province.includes(municipality)\n        );\n        if (municipalityMatch) {\n          // 如果province字段已经包含\"市\"，直接返回；否则添加\"市\"\n          return province.endsWith('市') ? province : `${municipalityMatch}市`;\n        }\n      }\n      // 如果province字段不可用，从city字段中提取直辖市名称\n      const municipalityMatch = MUNICIPALITIES.find(municipality => \n        city.includes(municipality)\n      );\n      if (municipalityMatch) {\n        return city.endsWith('市') ? city : `${municipalityMatch}市`;\n      }\n    }\n    \n    // 对于非直辖市，直接返回city字段\n    return city;\n  }\n  \n  // 如果没有city字段，尝试使用province字段\n  if (event.province && event.province.trim()) {\n    const province = event.province.trim();\n    \n    // 检查province是否为直辖市\n    const isMunicipality = MUNICIPALITIES.some(municipality => \n      province.includes(municipality)\n    );\n    \n    if (isMunicipality) {\n      const municipalityMatch = MUNICIPALITIES.find(municipality => \n        province.includes(municipality)\n      );\n      return province.endsWith('市') ? province : `${municipalityMatch}市`;\n    }\n    \n    return province;\n  }\n  \n  return '待定';\n};\n/**\n  * 构建查询参数（按当前视图分别读取筛选条件）\n */\nconst buildQueryParams = (isLoadMore = false) => {\n  const params = {\n    pageNum: isLoadMore ? pagination.value.pageNum : 1,\n    pageSize: pagination.value.pageSize\n  };\n\n  // 搜索关键词\n  if (searchKeyword.value.trim()) {\n    params.title = searchKeyword.value.trim();\n  }\n\n  // 依据当前视图选择对应的筛选集合\n  const filters = currentTab.value === 1 ? appliedFiltersCalendar.value : appliedFiltersList.value;\n\n  // 地区筛选\n  if (filters.location > 1) {\n    // 热门城市映射\n    const hotLocationMap = {\n      2: '北京',\n      3: '上海',\n      4: '广州',\n      5: '深圳'\n    };\n    \n    if (hotLocationMap[filters.location]) {\n      // 热门城市\n      params.location = hotLocationMap[filters.location];\n    } else if (filters.location >= 100) {\n      // 其他城市（从otherCities数组获取）\n      const otherIndex = filters.location - 100;\n      if (otherIndex < otherCities.value.length) {\n        params.location = otherCities.value[otherIndex];\n      }\n    }\n  }\n\n  // 报名状态筛选（仅列表视图）\n  // 只有当用户明确选择了具体状态（非\"全部状态\"）时才传递筛选参数\n  if (currentTab.value === 0 && filters.status > 1) {\n    const registrationStatusMap = {\n      2: 0, // 即将开始 -> registrationStatus: 0\n      3: 1, // 报名中 -> registrationStatus: 1\n      4: 2  // 报名截止 -> registrationStatus: 2\n    };\n\n    if (registrationStatusMap.hasOwnProperty(filters.status)) {\n      params.registrationStatus = registrationStatusMap[filters.status];\n      console.log('用户选择了具体报名状态筛选:', filters.status, '-> registrationStatus:', params.registrationStatus);\n    } else {\n      console.warn('未知的报名状态筛选值:', filters.status);\n    }\n  } else if (currentTab.value === 0) {\n    console.log('用户选择了全部状态，不传递registrationStatus参数，显示所有已上架活动');\n  } else if (currentTab.value === 1) {\n    console.log('日历视图：不传递registrationStatus参数，显示所有状态的活动');\n  }\n\n  // 排序设置\n  if (currentTab.value === 1) {\n    // 如果是日历视图，强制按开始时间升序排序\n    params.orderBy = 'startTime';\n    params.isAsc = 'asc';\n  } else {\n    // 列表视图排序逻辑 - 支持综合排序（使用已应用的筛选条件）\n    switch (filters.sortBy) {\n      case 1: // 综合排序\n        params.orderBy = 'comprehensive';\n        break;\n      case 2: // 按时间（最近开始）\n        params.orderBy = 'startTime';\n        params.isAsc = 'asc';\n        console.log('按时间排序: 最近开始优先');\n        break;\n      case 3: // 按最新发布\n        params.orderBy = 'createTime';\n        params.isAsc = 'desc';\n        console.log('按最新发布排序: 最新创建的活动优先');\n        break;\n      default: // 默认按创建时间（最新发布）\n        params.orderBy = 'createTime';\n        params.isAsc = 'desc';\n        console.log('默认排序: 最新发布');\n    }\n  }\n\n  // 时间范围筛选 - 新的时间筛选逻辑（使用已应用的筛选条件）\n  if (filters.timeRange > 1) {\n    const timeRange = buildTimeRangeParams(filters.timeRange);\n    if (timeRange) {\n      Object.assign(params, timeRange);\n    }\n  }\n\n  return params;\n};\n\n/**\n * 构建时间范围筛选参数 - 独立的时间筛选逻辑\n * @param {number} timeRangeValue 时间范围选项值\n * @returns {object|null} 时间筛选参数对象\n */\nconst buildTimeRangeParams = (timeRangeValue) => {\n  const now = new Date();\n  let startTime = null;\n  let endTime = null;\n\n  switch (timeRangeValue) {\n    case 2: // 1周内\n      startTime = now;\n      endTime = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);\n      console.log('时间筛选: 1周内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());\n      break;\n    case 3: // 1月内\n      startTime = now;\n      endTime = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());\n      console.log('时间筛选: 1月内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());\n      break;\n    case 4: // 1年内\n      startTime = now;\n      endTime = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\n      console.log('时间筛选: 1年内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());\n      break;\n    default:\n      console.log('时间筛选: 全部时间');\n      return null;\n  }\n\n  return {\n    timeRangeStart: startTime.toISOString(),\n    timeRangeEnd: endTime.toISOString()\n  };\n};\n\n/**\n * 获取活动列表\n */\nconst fetchEventList = async (isLoadMore = false) => {\n  if (isLoading.value) return;\n\n  isLoading.value = true;\n\n  try {\n    const params = buildQueryParams(isLoadMore);\n    console.log('请求参数:', params);\n\n    // 日历视图和列表视图统一使用相同的API，确保显示所有状态的活动\n    let response;\n    response = await getEventListApi(params);\n    const {\n      rows = [], total = 0\n    } = response;\n\n    if (isLoadMore) {\n      eventList.value.push(...rows);\n    } else {\n      eventList.value = rows;\n      pagination.value.pageNum = 1;\n    }\n\n    pagination.value.total = total;\n    pagination.value.hasMore = eventList.value.length < total;\n\n    // 成功加载后隐藏重试按钮\n    showRetry.value = false;\n\n    console.log(`获取活动列表成功: ${rows.length} 条记录, 总计: ${total}`);\n\n  } catch (error) {\n    console.error('获取活动列表失败:', error);\n\n    // 根据错误类型提供不同的提示\n    let errorMessage = '获取活动列表失败';\n    if (error.message && error.message.includes('timeout')) {\n      errorMessage = '网络请求超时，请重试';\n    } else if (error.message && error.message.includes('Network')) {\n      errorMessage = '网络连接失败，请检查网络';\n    }\n\n    uni.showToast({\n      title: errorMessage,\n      icon: 'none',\n      duration: 3000\n    });\n\n    // 如果是首次加载失败，显示重试按钮\n    if (!isLoadMore && eventList.value.length === 0) {\n      showRetry.value = true;\n    }\n  } finally {\n    isLoading.value = false;\n    isRefreshing.value = false;\n  }\n};\n\n/**\n * 搜索防抖处理\n */\nconst debouncedSearch = debounce(() => {\n  fetchEventList();\n}, 500);\n\n// ==================== 事件处理方法 ====================\n/**\n * 标签页切换\n */\nconst tabChange = (index) => {\n  currentTab.value = index; // 直接使用 index，因为 u-subsection 的 @change 事件直接返回索引号\n  \n  // 根据视图类型设置不同的pageSize\n  const newPageSize = index === 1 ? 20 : PAGE_CONFIG.DEFAULT_PAGE_SIZE;\n  \n  // 关键：切换后立即重置数据，以保证用户看到即时加载效果\n  eventList.value = [];\n  pagination.value.pageNum = 1;\n  pagination.value.pageSize = newPageSize;\n  pagination.value.hasMore = true;\n  \n  // 重新调用数据获取函数，此时 buildQueryParams 会根据新的 currentTab 值应用正确的排序\n  fetchEventList();\n};\n\n/**\n * 搜索处理\n */\nconst onSearch = (value) => {\n  searchKeyword.value = value;\n  debouncedSearch();\n};\n\n/**\n * 筛选条件变更\n */\nconst onFilterChange = () => {\n  console.log('筛选条件变更，重置数据并重新加载');\n  // 重置分页状态\n  eventList.value = [];\n  pagination.value.pageNum = 1;\n  pagination.value.hasMore = true;\n  // 重新获取数据\n  fetchEventList();\n};\n\n/**\n * 处理来自EventFilter组件的筛选条件应用\n */\nconst handleFiltersApply = (newFilters) => {\n  console.log('接收到来自筛选组件的新条件:', newFilters);\n  appliedFiltersList.value = { ...newFilters };\n  // 调用API刷新列表\n  onFilterChange();\n};\n\n/**\n * 处理来自EventFilter组件的日历筛选条件应用\n */\nconst handleCalendarFiltersApply = (newFilters) => {\n  console.log('接收到来自日历筛选组件的新条件:', newFilters);\n  appliedFiltersCalendar.value = { ...newFilters };\n  // 调用API刷新列表\n  onFilterChange();\n};\n\n/**\n * 跳转到活动详情\n */\nconst goToDetail = (event) => {\n  uni.navigateTo({\n    url: `/pages_sub/pages_event/detail?id=${event.id}`\n  });\n};\n\n/**\n * 下拉刷新\n */\nconst onRefresh = () => {\n  isRefreshing.value = true;\n  pagination.value.pageNum = 1;\n  fetchEventList();\n};\n\n/**\n * 上拉加载更多\n */\nconst onLoadMore = () => {\n  if (!pagination.value.hasMore || isLoading.value) return;\n\n  pagination.value.pageNum++;\n  fetchEventList(true);\n};\n\n// ==================== 生命周期 ====================\nonLoad(() => {\n  console.log('活动列表页 onLoad - 页面首次加载');\n  uni.showShareMenu({\n      menus: ['shareAppMessage', 'shareTimeline']\n    });\n\n  // 读取静态资源配置\n  const assets = uni.getStorageSync('staticAssets');\n  eventBgUrl.value = assets?.eventbg || '';\n\n  // 获取城市列表和活动列表\n  fetchEventCities();\n  fetchEventList();\n\n  uni.$on('dataChanged', (eventData) => {\n    console.log('活动列表页收到数据变化事件，静默刷新列表...', eventData);\n\n    // 重新获取活动列表数据（静默刷新，不显示任何提示）\n    fetchEventList();\n  });\n});\n\n// 页面显示时隐藏原生 tabBar\nonShow(() => {\n  uni.hideTabBar();\n});\n\n// 页面卸载时移除事件监听\nonUnload(() => {\n  uni.$off('dataChanged');\n});\n\nonReachBottom(() => {\n  onLoadMore();\n});\n\nonPullDownRefresh(() => {\n  onRefresh();\n  setTimeout(() => {\n    uni.stopPullDownRefresh();\n  }, 1000);\n});\n\nonShareAppMessage(() => {\n  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.event-list-page {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f5f5;\n}\n\n\n.header-wrapper {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 100; \n  overflow: hidden;\n  min-height: calc(452rpx + var(--status-bar-height));\n  border-bottom-left-radius: 20rpx;\n  border-bottom-right-radius: 20rpx;\n  padding-bottom: 20rpx;\n}\n\n/* 背景图片样式 - 完全覆盖状态栏 */\n.header-bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n  object-fit: cover;\n  object-position: center;\n}\n\n/* 自定义导航栏样式 */\n.custom-navbar {\n  position: absolute;\n  top: 94rpx;\n  left: 0;\n  right: 0;\n  width: 750rpx;\n  height: 88rpx;\n  z-index: 2;\n  border-radius: 0rpx;\n}\n\n.navbar-title {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.title-text {\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n  font-weight: normal;\n  font-size: 32rpx;\n  color: #FFFFFF;\n  line-height: 44rpx;\n  text-align: center;\n  font-style: normal;\n  text-transform: none;\n}\n\n/* 确保内容在背景图片之上 */\n.top-controls,\n.filter-bar {\n  position: relative;\n  z-index: 2;\n}\n\n.top-controls {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 30rpx;\n  gap: 32rpx;\n  position: absolute;\n  top: 182rpx;\n  left: 16rpx;\n  right: 24rpx;\n  height: 60rpx; \n}\n\n.search-wrapper {\n  width: 446rpx;\n  height: 60rpx; \n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n  /* 确保与up-subsection组件基线对齐 */\n  vertical-align: middle;\n}\n\n\n.event-list-scroll.list-scroll-with-filter {\n  padding-top: 60rpx !important; \n}\n\n.calendar-scroll-with-filter {\n  padding-top: 36rpx !important; \n}\n\n/* 日历视图筛选栏样式 - 与列表视图保持一致 */\n.calendar-filter-bar {\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 102;\n  background-color: #ffffff;\n  border-top-left-radius: 32rpx;\n  border-top-right-radius: 32rpx;\n  border-bottom-left-radius: 32rpx;\n  border-bottom-right-radius: 32rpx;\n  overflow: hidden;\n  \n  /* 主筛选按钮容器 */\n  .filter-main-buttons {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    gap: 32rpx;\n    padding: 0 24rpx;\n    position: relative;\n    z-index: 2;\n    background-color: #ffffff;\n    height: 56rpx;\n  }\n  \n  /* 单个筛选按钮 */\n  .filter-button {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8rpx;\n    padding: 8rpx 16rpx;\n    background-color: transparent;\n    border: none;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    min-width: 120rpx;\n    \n    /* 激活状态样式 */\n    &.active {\n      .filter-text {\n        color: #023F98;\n      }\n      .custom-arrow {\n        border-top-color: #023F98;\n      }\n    }\n  }\n  \n  .filter-text {\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n    font-weight: normal;\n    font-size: 26rpx;\n    color: #66666E;\n    line-height: 44rpx;\n    text-align: center;\n    white-space: nowrap;\n  }\n  \n  /* 占位按钮样式 */\n  .filter-placeholder {\n    pointer-events: none; /* 禁用点击 */\n    opacity: 0; /* 隐藏但保持布局空间 */\n  }\n  \n  /* 所有面板的包裹层 */\n  .filter-panel-wrapper {\n    width: 100%;\n    position: relative;\n    z-index: 3;\n  }\n  \n  /* 筛选面板样式 */\n  .filter-panel {\n    width: 100%;\n    max-height: 60vh;\n    overflow-y: auto;\n    background: #FFFFFF;\n    border: none;\n    z-index: 1000;\n    padding: 32rpx 24rpx;\n    margin: 0;\n    max-height: 1046rpx;\n    overflow-y: auto;\n    /* 确保面板内容与容器底部圆角配合 */\n    border-bottom-left-radius: 32rpx;\n    border-bottom-right-radius: 32rpx;\n  }\n  \n  /* 遮罩层样式 */\n  .filter-mask {\n    position: fixed;\n    top: calc(268rpx + var(--status-bar-height) + 56rpx);\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.5);\n    z-index: 1;\n  }\n  \n  /* 选项网格 */\n  .option-grid {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10rpx;\n    justify-content: flex-start;\n    margin-left: 16rpx;\n  }\n  \n  /* 选项项 */\n  .option-item {\n    width: 162rpx;\n    height: 60rpx;\n    background: #F2F4FA;\n    border-radius: 8rpx 8rpx 8rpx 8rpx;\n    border: 2rpx solid transparent;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    flex-shrink: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    \n    &.active {\n      background: rgba(42, 97, 241, 0.2);\n      border-color: transparent;\n      \n      .option-text {\n        color: #023F98;\n        font-weight: normal;\n      }\n    }\n    \n    &:hover {\n      opacity: 0.8;\n    }\n  }\n  \n  .option-text {\n    width: 112rpx;\n    height: 44rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n    font-weight: normal;\n    font-size: 28rpx;\n    color: #66666E;\n    line-height: 44rpx;\n    text-align: center;\n    font-style: normal;\n    text-transform: none;\n    white-space: nowrap;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  /* 分组标题 */\n  .section-title {\n    width: 104rpx;\n    height: 44rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n    font-weight: normal;\n    font-size: 26rpx;\n    color: #23232A;\n    line-height: 44rpx;\n    text-align: left;\n    font-style: normal;\n    text-transform: none;\n    margin-bottom: 24rpx;\n    margin-left: 16rpx;\n    display: block;\n    font-weight: bold;\n  }\n  \n  /* 分割线样式 */\n  .button-divider {\n    width: 698rpx;\n    height: 2rpx;\n    background: #EEEEEE;\n    border-radius: 0rpx 0rpx 0rpx 0rpx;\n    margin: 32rpx 0 0 6rpx;\n  }\n  \n  /* 筛选按钮组 */\n  .filter-buttons {\n    display: flex;\n    justify-content: flex-start;\n    padding: 32rpx 0 24rpx;\n    margin-left: 6rpx;\n  }\n  \n  .filter-btn {\n    width: 340rpx;\n    height: 76rpx;\n    border-radius: 8rpx 8rpx 8rpx 8rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    margin-right: 24rpx;\n    border: none;\n    \n    &:last-child {\n      margin-right: 0;\n    }\n  }\n  \n  .reset-btn {\n    background: rgba(42, 97, 241, 0.1);\n    \n    .btn-text {\n      color: #23232A;\n    }\n  }\n  \n  .complete-btn {\n    background: #023F98;\n    \n    &:hover {\n      background: #1E4FD9;\n    }\n    \n    &:active {\n      background: #1A43C1;\n    }\n    \n    .btn-text {\n      color: #FFFFFF;\n    }\n  }\n  \n  .btn-text {\n    width: 56rpx;\n    height: 44rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;\n    font-weight: normal;\n    font-size: 28rpx;\n    line-height: 44rpx;\n    text-align: center;\n    font-style: normal;\n    text-transform: none;\n  }\n  \n  /* 自定义下拉箭头样式 */\n  .custom-arrow {\n    width: 0;\n    height: 0;\n    border-left: 8rpx solid transparent;\n    border-right: 8rpx solid transparent;\n    border-top: 8rpx solid #023F98;\n    transition: transform 0.3s ease;\n    border-radius: 0.4rpx;\n  }\n  \n  /* 旋转动画 */\n  .rotate-180 {\n    transform: rotate(180deg);\n    transition: transform 0.3s ease;\n  }\n}\n\n\n\n\n/* 活动列表滚动区域 - 白色内容容器 (根据蓝湖数据精确设置) */\n.event-list-scroll {\n  /* 将白色背景、圆角、外边距等样式应用到这里 */\n  background-color: #ffffff;\n  border-top-left-radius: 32rpx; /* 与筛选栏圆角一致，过渡更自然 */\n  border-top-right-radius: 32rpx;\n  margin: 0;\n  margin-top: calc(432rpx + var(--status-bar-height) - 184rpx); /* 调整为与新的筛选栏位置协调 */\n  position: relative;\n  z-index: 101;\n\n  flex: 1;\n  box-sizing: border-box;\n  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));\n  height: calc(100vh - 432rpx - var(--status-bar-height) + 184rpx); /* 调整高度计算 */\n\n  &:not(.list-scroll-with-filter) {\n    padding-top: 8rpx; \n  }\n}\n\n/* 日历视图滚动区域  */\n.calendar-scroll {\n  background-color: #ffffff;\n  border-top-left-radius: 32rpx;\n  border-top-right-radius: 32rpx;\n  margin: 0;\n  margin-top: -158rpx;\n  position: relative;\n  padding-top: 24rpx;\n\n  /* 左右内边距，让卡片和筛选栏不要贴边 */\n  padding-left: 30rpx;\n  padding-right: 30rpx;\n\n  flex: 1;\n  box-sizing: border-box;\n  padding-bottom: 120rpx;\n}\n\n.event-card {\n  width: 100%;\n  height: 272rpx;\n  background: #FFFFFF;\n  border-radius: 0rpx 0rpx 0rpx 0rpx;\n  border: 2rpx solid #EEEEEE;\n  margin-bottom: 0rpx;\n  padding: 24rpx;\n  display: flex;\n  overflow: hidden;\n  box-sizing: border-box;\n\n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.card-left {\n  position: relative;\n  width: 336rpx;\n  height: 192rpx;\n  flex-shrink: 0;\n}\n\n.event-image {\n  width: 100%;\n  height: 100%;\n  display: block;\n}\n\n.status-tag {\n  position: absolute;\n  top: 12rpx;\n  left: 12rpx;\n  width: 90rpx;\n  height: 40rpx;\n  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);\n  border-radius: 20rpx 20rpx 20rpx 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-sizing: border-box;\n\n  /* 内部文字样式 */\n  color: #23232A;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n  font-weight: normal;\n  font-size: 22rpx;\n  text-align: left;\n  font-style: normal;\n  text-transform: none;\n  line-height: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n\n  &.ended {\n    background: linear-gradient(90deg, #909399 0%, #C0C4CC 100%);\n  }\n}\n\n.card-right {\n  flex: 1;\n  padding: 16rpx 20rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.event-title {\n  width: 346rpx;\n  height: 80rpx;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n  font-weight: normal;\n  font-size: 28rpx;\n  color: #23232A;\n  text-align: justify;\n  font-style: normal;\n  text-transform: none;\n  line-height: 1.4;\n  margin-bottom: 24rpx;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  line-clamp: 2;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.event-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.event-info-row {\n  display: flex !important;\n  align-items: center !important;\n  justify-content: flex-start !important;\n  gap: 24rpx !important;\n  margin-bottom: 18rpx !important;\n  flex-wrap: nowrap !important;\n}\n\n.time-location-item {\n  display: flex !important;\n  align-items: center !important;\n  gap: 8rpx !important;\n  flex-shrink: 0 !important;\n}\n\n.event-info-icon {\n  width: 32rpx !important;\n  height: 32rpx !important;\n  flex-shrink: 0 !important;\n}\n\n.info-text {\n  width: 176rpx !important;\n  height: 32rpx !important;\n  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30' !important;\n  font-weight: normal !important;\n  font-size: 22rpx !important;\n  color: #9B9A9A !important;\n  text-align: left !important;\n  font-style: normal !important;\n  text-transform: none !important;\n  line-height: 32rpx !important;\n  overflow: hidden !important;\n  text-overflow: ellipsis !important;\n  white-space: nowrap !important;\n}\n\n.remaining-spots {\n  width: 154rpx;\n  height: 40rpx;\n  border-radius: 4rpx 4rpx 4rpx 4rpx;\n  border: 1rpx solid #FB8620;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0;\n  margin: 0;\n  box-sizing: border-box;\n  overflow: hidden;\n  flex-shrink: 0;\n\n  /**\n   * 剩余名额文字样式\n   */\n  .spots-count {\n    width: 100%;\n    height: 36rpx;\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\n    font-weight: normal;\n    font-size: 20rpx;\n    color: #FB8620;\n    text-align: center;\n    font-style: normal;\n    text-transform: none;\n    line-height: 36rpx;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n}\n\n// 空状态样式\n.empty-state {\n  padding: 100rpx 0;\n  text-align: center;\n}\n\n// 重试按钮容器\n.retry-container {\n  margin-top: 40rpx;\n  display: flex;\n  justify-content: center;\n}\n\n// 加载状态优化\n.event-list-scroll {\n  :deep(.up-loadmore) {\n    padding: 30rpx 0;\n  }\n}\n\n/* 为加载更多组件的包裹容器提供上下内边距 */\n.loadmore-wrapper {\n  padding-top: 40rpx;\n  padding-bottom: 20rpx;\n}\n\n// 搜索框优化 - 自定义高度设置\n.search-wrapper {\n  :deep(.up-search) {\n    width: 446rpx;\n    height: 40rpx;\n    background: #FFFFFF;\n    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05);\n    border-radius: 20rpx 20rpx 20rpx 20rpx;\n\n    .u-search__content {\n      width: 100%;\n      height: 100%;\n      background: #FFFFFF;\n      border-radius: 20rpx 20rpx 20rpx 20rpx;\n      box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05);\n      display: flex;\n      align-items: center;\n    }\n\n    .u-search__content__icon {\n      color: #c0c4cc;\n    }\n\n    .u-search__content__input {\n      color: #303133;\n    }\n  }\n}\n\n// 响应式优化\n@media screen and (max-width: 750rpx) {\n  .top-controls {\n    flex-direction: column;\n    gap: 16rpx;\n\n    .search-wrapper {\n      width: 446rpx;\n      align-self: center;\n    }\n  }\n}\n\n/* up-subsection */\n:deep(.u-subsection) {\n  width: 224rpx !important;\n  height: 60rpx !important;\n  background: #FFFFFF !important;\n  border-radius: 30rpx 30rpx 30rpx 30rpx !important;\n  border: none !important;\n  box-shadow: none !important;\n  overflow: hidden !important;\n  position: relative !important; \n  display: flex !important;\n  align-items: center !important;\n  vertical-align: middle !important;\n}\n\n:deep(.u-subsection__item:not(:first-child)) {\n  border-left: none !important;\n}\n\n:deep(.u-subsection__item) {\n  border: none !important;\n  padding: 0 !important;\n  background: transparent !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  width: 50% !important;\n  position: relative !important;\n  z-index: 2 !important;\n}\n\n:deep(.u-subsection__item__text) {\n  width: 64rpx !important;\n  height: 44rpx !important;\n  font-family: 'Alibaba PuHuiTi', sans-serif;\n  font-weight: normal !important;\n  font-size: 32rpx !important;\n  color: #23232A !important;\n  text-align: center !important;\n  font-style: normal !important;\n  text-transform: none !important;\n  white-space: nowrap !important;\n  line-height: 44rpx !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n}\n\n:deep(.u-subsection__item--active .u-subsection__item__text) {\n  color: #23232A !important; \n  font-weight: normal !important; \n}\n\n/* 移动色块样式 - 确保对称居中且不被覆盖 */\n:deep(.u-subsection__bar) {\n  width: 96rpx !important;\n  height: 60rpx !important;\n  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%) !important;\n  border-radius: 30rpx !important;\n  transition: all 0.3s ease !important;\n  position: absolute !important;\n  top: 0 !important;\n  left: 0 !important;\n  z-index: 1 !important;\n  box-sizing: border-box !important;\n  margin: 0 !important;\n  padding: 0 !important;\n  transform-origin: center center !important;\n}\n\n//  日历视图基础样式 \n.calendar-view {\n  background-color: #FFFFFF;\n  padding: 0;\n  padding-bottom: 0;\n  box-sizing: border-box;\n}\n\n\n\n/* 搜索框样式  */\n.search-wrapper {\n  /* 强制覆盖父容器的样式限制 */\n  height: 60rpx !important; \n  display: flex !important;\n  align-items: center !important;\n  flex-shrink: 0 !important;\n}\n\n\n.search-wrapper :deep(.u-search) {\n  height: 60rpx !important; \n  width: 446rpx !important; \n  border: none !important;\n}\n\n.search-wrapper :deep(.u-search__content) {\n  height: 60rpx !important; \n  padding: 0 20rpx !important;\n  background: #FFFFFF !important; \n  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05) !important;\n  border-radius: 30rpx !important;\n}\n\n.search-wrapper :deep(.u-search__input-wrap) {\n  height: 60rpx !important;\n  padding: 0 !important;\n}\n\n.search-wrapper :deep(.u-search__input) {\n  height: 60rpx !important;\n  line-height: 60rpx !important;\n  font-size: 28rpx !important; \n  color: #333333 !important; \n  background-color: transparent !important;\n}\n\n.search-wrapper :deep(.u-search__input::placeholder) {\n  color: #999999 !important; \n  font-size: 28rpx !important; \n}\n\n.search-wrapper :deep(.u-search__action) {\n  height: 60rpx !important;\n  padding: 0 8rpx !important;\n}\n\n.search-wrapper :deep(.u-search__action-text) {\n  font-size: 28rpx !important;\n  color: #333333 !important;\n}\n\n</style>\n", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/event/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "PAGE_CONFIG", "computed", "parseDate", "getEventCitiesApi", "uni", "getEventListApi", "debounce", "onLoad", "onShow", "onUnload", "onReachBottom", "onPullDownRefresh", "onShareAppMessage"], "mappings": ";;;;;;;;;;;;;;;;;;AAiIA,MAAA,eAAA,MAAA;AACA,MAAA,kBAAA,MAAA;AACA,MAAA,YAAA,MAAA;AACA,MAAA,wBAAA,MAAA;AACA,MAAA,cAAA,MAAA;AACA,MAAA,gBAAA,MAAA;;;;AAkBA,UAAA,aAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,gBAAAA,cAAAA,IAAA,EAAA;AACA,UAAA,YAAAA,cAAAA,IAAA,CAAA,CAAA;AACA,UAAA,YAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,YAAAA,cAAAA,IAAA,KAAA;AAGA,UAAA,aAAAA,cAAAA,IAAA,EAAA;AAGA,UAAA,aAAAA,cAAAA,IAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA,WAAA,UAAA,IAAA,KAAAC,aAAA,YAAA;AAAA;AAAA,MACA,OAAA;AAAA,MACA,SAAA;AAAA,IACA,CAAA;AAGA,UAAA,qBAAAD,cAAAA,IAAA;AAAA,MACA,QAAA;AAAA,MACA,UAAA;AAAA,MACA,WAAA;AAAA,MACA,QAAA;AAAA,IACA,CAAA;AACA,UAAA,yBAAAA,cAAAA,IAAA;AAAA,MACA,UAAA;AAAA,MACA,WAAA;AAAA,IACA,CAAA;AAGA,UAAA,oBAAAA,cAAAA,IAAA,OAAA;AAGA,UAAA,cAAAA,cAAAA,IAAA,CAAA,CAAA;AAGAE,kBAAAA,SAAA,MAAA;AAAA,MACA,gBAAA;AAAA,MACA,GAAA,UAAA;AAAA,MACA,GAAA,YAAA,MAAA,IAAA,CAAA,MAAA,WAAA;AAAA,QACA,OAAA;AAAA,QACA,OAAA,MAAA;AAAA;AAAA,MACA,EAAA;AAAA,IACA,CAAA;AAEAF,kBAAAA,IAAA;AAAA,MAAA;AAAA,QACA,OAAA;AAAA,QACA,OAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,OAAA;AAAA,QACA,OAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,OAAA;AAAA,QACA,OAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,OAAA;AAAA,QACA,OAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,iBAAAE,cAAA,SAAA,MAAA;AACA,UAAA,UAAA;AAAA,eAAA;AACA,UAAA,CAAA,WAAA,MAAA;AAAA,eAAA;AACA,aAAA;AAAA,IACA,CAAA;AAKA,UAAA,gBAAAA,cAAA,SAAA,MAAA;AACA,UAAA,CAAA,UAAA,SAAA,UAAA,MAAA,WAAA,GAAA;AACA,eAAA;MACA;AACA,YAAA,SAAA,oBAAA;AACA,YAAA,WAAA,CAAA,MAAA,MAAA,MAAA,MAAA,MAAA,MAAA,IAAA;AACA,gBAAA,MAAA,QAAA,WAAA;AACA,cAAA,YAAAC,WAAAA,UAAA,MAAA,SAAA;AAEA,cAAA,OAAA,UAAA;AACA,cAAA,eAAA,oBAAA,KAAA,GAAA,YAAA;AACA,cAAA,QAAA,OAAA,UAAA,SAAA,IAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AACA,cAAA,MAAA,OAAA,UAAA,QAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AAEA,YAAA;AACA,YAAA,SAAA,aAAA;AACA,wBAAA,GAAA,IAAA,IAAA,KAAA,IAAA,GAAA;AAAA,QACA,OAAA;AACA,wBAAA,GAAA,KAAA,IAAA,GAAA;AAAA,QACA;AAGA,cAAA,aAAA,OAAA,UAAA,SAAA,IAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AACA,cAAA,WAAA,OAAA,UAAA,QAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AACA,cAAA,UAAA,GAAA,UAAA,YAAA,CAAA,IAAA,UAAA,IAAA,QAAA;AACA,YAAA,CAAA,OAAA,IAAA,OAAA,GAAA;AACA,iBAAA,IAAA,SAAA;AAAA,YACA,MAAA;AAAA,YACA,eAAA;AAAA;AAAA,YACA,WAAA,SAAA,UAAA,QAAA;AAAA,YACA,QAAA,CAAA;AAAA,UACA,CAAA;AAAA,QACA;AACA,eAAA,IAAA,OAAA,EAAA,OAAA,KAAA,KAAA;AAAA,MACA,CAAA;AACA,aAAA,MAAA,KAAA,OAAA,OAAA,CAAA;AAAA,IACA,CAAA;AAKAD,kBAAAA,SAAA,MAAA;AAEA,aAAA,cAAA;AAAA,IACA,CAAA;AAKAA,kBAAAA,SAAA,MAAA;AACA,aAAA,WAAA,MAAA;AAAA,IACA,CAAA;AAOA,UAAA,mBAAA,YAAA;AACA,UAAA;AACA,cAAA,WAAA,MAAAE,eAAAA;AAGA,YAAA,WAAA;AACA,YAAA,MAAA,QAAA,QAAA,GAAA;AACA,qBAAA;AAAA,QACA,WAAA,YAAA,MAAA,QAAA,SAAA,IAAA,GAAA;AACA,qBAAA,SAAA;AAAA,QACA,WAAA,YAAA,SAAA,SAAA,OAAA,MAAA,QAAA,SAAA,IAAA,GAAA;AACA,qBAAA,SAAA;AAAA,QACA;AAEAC,sBAAA,MAAA,MAAA,OAAA,gCAAA,YAAA,QAAA;AAEA,YAAA,YAAA,MAAA,QAAA,QAAA,GAAA;AAEA,gBAAA,eAAA,CAAA,MAAA,MAAA,MAAA,IAAA;AACA,gBAAA,iBAAA,SAAA;AAAA,YAAA,UACA,QAAA,KAAA,UAAA,CAAA,aAAA,SAAA,KAAA,MAAA;AAAA,UACA;AAEA,sBAAA,QAAA;AAAA,QACA,OAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,YACA,UAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAsEA,UAAA,mBAAA,CAAA,aAAA,UAAA;AACA,YAAA,SAAA;AAAA,QACA,SAAA,aAAA,WAAA,MAAA,UAAA;AAAA,QACA,UAAA,WAAA,MAAA;AAAA,MACA;AAGA,UAAA,cAAA,MAAA,QAAA;AACA,eAAA,QAAA,cAAA,MAAA,KAAA;AAAA,MACA;AAGA,YAAA,UAAA,WAAA,UAAA,IAAA,uBAAA,QAAA,mBAAA;AAGA,UAAA,QAAA,WAAA,GAAA;AAEA,cAAA,iBAAA;AAAA,UACA,GAAA;AAAA,UACA,GAAA;AAAA,UACA,GAAA;AAAA,UACA,GAAA;AAAA,QACA;AAEA,YAAA,eAAA,QAAA,QAAA,GAAA;AAEA,iBAAA,WAAA,eAAA,QAAA,QAAA;AAAA,QACA,WAAA,QAAA,YAAA,KAAA;AAEA,gBAAA,aAAA,QAAA,WAAA;AACA,cAAA,aAAA,YAAA,MAAA,QAAA;AACA,mBAAA,WAAA,YAAA,MAAA,UAAA;AAAA,UACA;AAAA,QACA;AAAA,MACA;AAIA,UAAA,WAAA,UAAA,KAAA,QAAA,SAAA,GAAA;AACA,cAAA,wBAAA;AAAA,UACA,GAAA;AAAA;AAAA,UACA,GAAA;AAAA;AAAA,UACA,GAAA;AAAA;AAAA,QACA;AAEA,YAAA,sBAAA,eAAA,QAAA,MAAA,GAAA;AACA,iBAAA,qBAAA,sBAAA,QAAA,MAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,gCAAA,kBAAA,QAAA,QAAA,0BAAA,OAAA,kBAAA;AAAA,QACA,OAAA;AACAA,wBAAA,MAAA,MAAA,QAAA,gCAAA,eAAA,QAAA,MAAA;AAAA,QACA;AAAA,MACA,WAAA,WAAA,UAAA,GAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,6CAAA;AAAA,MACA,WAAA,WAAA,UAAA,GAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,wCAAA;AAAA,MACA;AAGA,UAAA,WAAA,UAAA,GAAA;AAEA,eAAA,UAAA;AACA,eAAA,QAAA;AAAA,MACA,OAAA;AAEA,gBAAA,QAAA,QAAA;AAAA,UACA,KAAA;AACA,mBAAA,UAAA;AACA;AAAA,UACA,KAAA;AACA,mBAAA,UAAA;AACA,mBAAA,QAAA;AACAA,0BAAAA,MAAA,MAAA,OAAA,gCAAA,eAAA;AACA;AAAA,UACA,KAAA;AACA,mBAAA,UAAA;AACA,mBAAA,QAAA;AACAA,0BAAAA,MAAA,MAAA,OAAA,gCAAA,oBAAA;AACA;AAAA,UACA;AACA,mBAAA,UAAA;AACA,mBAAA,QAAA;AACAA,0BAAAA,MAAA,MAAA,OAAA,gCAAA,YAAA;AAAA,QACA;AAAA,MACA;AAGA,UAAA,QAAA,YAAA,GAAA;AACA,cAAA,YAAA,qBAAA,QAAA,SAAA;AACA,YAAA,WAAA;AACA,iBAAA,OAAA,QAAA,SAAA;AAAA,QACA;AAAA,MACA;AAEA,aAAA;AAAA,IACA;AAOA,UAAA,uBAAA,CAAA,mBAAA;AACA,YAAA,MAAA,oBAAA;AACA,UAAA,YAAA;AACA,UAAA,UAAA;AAEA,cAAA,gBAAA;AAAA,QACA,KAAA;AACA,sBAAA;AACA,oBAAA,IAAA,KAAA,IAAA,QAAA,IAAA,IAAA,KAAA,KAAA,KAAA,GAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,gCAAA,eAAA,UAAA,mBAAA,GAAA,KAAA,QAAA,mBAAA,CAAA;AACA;AAAA,QACA,KAAA;AACA,sBAAA;AACA,oBAAA,IAAA,KAAA,IAAA,YAAA,GAAA,IAAA,SAAA,IAAA,GAAA,IAAA,QAAA,CAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,gCAAA,eAAA,UAAA,mBAAA,GAAA,KAAA,QAAA,mBAAA,CAAA;AACA;AAAA,QACA,KAAA;AACA,sBAAA;AACA,oBAAA,IAAA,KAAA,IAAA,YAAA,IAAA,GAAA,IAAA,SAAA,GAAA,IAAA,QAAA,CAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,gCAAA,eAAA,UAAA,mBAAA,GAAA,KAAA,QAAA,mBAAA,CAAA;AACA;AAAA,QACA;AACAA,wBAAAA,MAAA,MAAA,OAAA,gCAAA,YAAA;AACA,iBAAA;AAAA,MACA;AAEA,aAAA;AAAA,QACA,gBAAA,UAAA,YAAA;AAAA,QACA,cAAA,QAAA,YAAA;AAAA,MACA;AAAA,IACA;AAKA,UAAA,iBAAA,OAAA,aAAA,UAAA;AACA,UAAA,UAAA;AAAA;AAEA,gBAAA,QAAA;AAEA,UAAA;AACA,cAAA,SAAA,iBAAA,UAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,gCAAA,SAAA,MAAA;AAGA,YAAA;AACA,mBAAA,MAAAC,+BAAA,MAAA;AACA,cAAA;AAAA,UACA,OAAA,CAAA;AAAA,UAAA,QAAA;AAAA,QACA,IAAA;AAEA,YAAA,YAAA;AACA,oBAAA,MAAA,KAAA,GAAA,IAAA;AAAA,QACA,OAAA;AACA,oBAAA,QAAA;AACA,qBAAA,MAAA,UAAA;AAAA,QACA;AAEA,mBAAA,MAAA,QAAA;AACA,mBAAA,MAAA,UAAA,UAAA,MAAA,SAAA;AAGA,kBAAA,QAAA;AAEAD,sBAAAA,MAAA,MAAA,OAAA,gCAAA,aAAA,KAAA,MAAA,aAAA,KAAA,EAAA;AAAA,MAEA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,KAAA;AAGA,YAAA,eAAA;AACA,YAAA,MAAA,WAAA,MAAA,QAAA,SAAA,SAAA,GAAA;AACA,yBAAA;AAAA,QACA,WAAA,MAAA,WAAA,MAAA,QAAA,SAAA,SAAA,GAAA;AACA,yBAAA;AAAA,QACA;AAEAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,QACA,CAAA;AAGA,YAAA,CAAA,cAAA,UAAA,MAAA,WAAA,GAAA;AACA,oBAAA,QAAA;AAAA,QACA;AAAA,MACA,UAAA;AACA,kBAAA,QAAA;AACA,qBAAA,QAAA;AAAA,MACA;AAAA,IACA;AAKA,UAAA,kBAAAE,YAAA,SAAA,MAAA;AACA;IACA,GAAA,GAAA;AAMA,UAAA,YAAA,CAAA,UAAA;AACA,iBAAA,QAAA;AAGA,YAAA,cAAA,UAAA,IAAA,KAAAN,aAAAA,YAAA;AAGA,gBAAA,QAAA;AACA,iBAAA,MAAA,UAAA;AACA,iBAAA,MAAA,WAAA;AACA,iBAAA,MAAA,UAAA;AAGA;IACA;AAKA,UAAA,WAAA,CAAA,UAAA;AACA,oBAAA,QAAA;AACA;IACA;AAKA,UAAA,iBAAA,MAAA;AACAI,oBAAAA,MAAA,MAAA,OAAA,gCAAA,kBAAA;AAEA,gBAAA,QAAA;AACA,iBAAA,MAAA,UAAA;AACA,iBAAA,MAAA,UAAA;AAEA;IACA;AAKA,UAAA,qBAAA,CAAA,eAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,gCAAA,kBAAA,UAAA;AACA,yBAAA,QAAA,EAAA,GAAA;AAEA;IACA;AAKA,UAAA,6BAAA,CAAA,eAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,gCAAA,oBAAA,UAAA;AACA,6BAAA,QAAA,EAAA,GAAA;AAEA;IACA;AAKA,UAAA,aAAA,CAAA,UAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,oCAAA,MAAA,EAAA;AAAA,MACA,CAAA;AAAA,IACA;AAKA,UAAA,YAAA,MAAA;AACA,mBAAA,QAAA;AACA,iBAAA,MAAA,UAAA;AACA;IACA;AAKA,UAAA,aAAA,MAAA;AACA,UAAA,CAAA,WAAA,MAAA,WAAA,UAAA;AAAA;AAEA,iBAAA,MAAA;AACA,qBAAA,IAAA;AAAA,IACA;AAGAG,kBAAAA,OAAA,MAAA;AACAH,oBAAAA,MAAA,MAAA,OAAA,gCAAA,uBAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAGA,YAAA,SAAAA,cAAAA,MAAA,eAAA,cAAA;AACA,iBAAA,SAAA,iCAAA,YAAA;AAGA;AACA;AAEAA,oBAAAA,MAAA,IAAA,eAAA,CAAA,cAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,gCAAA,2BAAA,SAAA;AAGA;MACA,CAAA;AAAA,IACA,CAAA;AAGAI,kBAAAA,OAAA,MAAA;AACAJ,oBAAA,MAAA,WAAA;AAAA,IACA,CAAA;AAGAK,kBAAAA,SAAA,MAAA;AACAL,0BAAA,KAAA,aAAA;AAAA,IACA,CAAA;AAEAM,kBAAAA,cAAA,MAAA;AACA;IACA,CAAA;AAEAC,kBAAAA,kBAAA,MAAA;AACA;AACA,iBAAA,MAAA;AACAP,sBAAA,MAAA,oBAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA,CAAA;AAEAQ,kBAAAA,kBAAA,MAAA;AACA,aAAA;IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxtBA,GAAG,WAAW,eAAe;"}