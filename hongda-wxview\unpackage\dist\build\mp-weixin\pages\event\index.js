"use strict";const e=require("../../common/vendor.js"),a=require("../../api/data/event.js"),t=require("../../utils/tools.js"),o=require("../../utils/date.js"),l=require("../../utils/config.js");if(!Array){(e.resolveComponent("up-subsection")+e.resolveComponent("up-button")+e.resolveComponent("up-loadmore"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-subsection/u-subsection.js")+s+i+(()=>"../../uni_modules/uview-plus/components/u-button/u-button.js")+u+(()=>"../../uni_modules/uview-plus/components/u-loadmore/u-loadmore.js")+c+r+n)();const n=()=>"../../components/layout/CustomTabBar.js",s=()=>"../../components/home/<USER>",u=()=>"../../components/event/EventCard.js",r=()=>"../../components/event/EventCalendarTimeline.js",i=()=>"../../components/event/EventFilter.js",c=()=>"../../components/common/NoMoreDivider.js",v={__name:"index",setup(n){const s=e.ref(0),u=e.ref(""),r=e.ref([]),i=e.ref(!1),c=e.ref(!1),v=e.ref(!1),g=e.ref(""),m=e.ref({pageNum:1,pageSize:1===s.value?20:l.PAGE_CONFIG.DEFAULT_PAGE_SIZE,total:0,hasMore:!0}),d=e.ref({sortBy:1,location:1,timeRange:1,status:1}),p=e.ref({location:1,timeRange:1}),h=e.ref("60rpx"),f=e.ref([]);e.computed((()=>[allRegionOption.value,...hotCities.value,...f.value.map(((e,a)=>({label:e,value:100+a})))])),e.ref([{label:"全部时间",value:1},{label:"1周内",value:2},{label:"1月内",value:3},{label:"1年内",value:4}]);const S=e.computed((()=>i.value?"loading":m.value.hasMore?"more":"nomore")),y=e.computed((()=>{if(!r.value||0===r.value.length)return[];const e=new Map,a=["周日","周一","周二","周三","周四","周五","周六"];return r.value.forEach((t=>{const l=o.parseDate(t.startTime),n=l.getFullYear(),s=(new Date).getFullYear(),u=String(l.getMonth()+1).padStart(2,"0"),r=String(l.getDate()).padStart(2,"0");let i;i=n!==s?`${n}年${u}月${r}日`:`${u}.${r}`;const c=String(l.getMonth()+1).padStart(2,"0"),v=String(l.getDate()).padStart(2,"0"),g=`${l.getFullYear()}-${c}-${v}`;e.has(g)||e.set(g,{date:g,formattedDate:i,dayOfWeek:a[l.getDay()],events:[]}),e.get(g).events.push(t)})),Array.from(e.values())}));e.computed((()=>y.value)),e.computed((()=>m.value.hasMore));const b=e=>{const a=new Date;let t=null,o=null;switch(e){case 2:t=a,o=new Date(a.getTime()+6048e5),console.log("时间筛选: 1周内 -",t.toLocaleDateString(),"到",o.toLocaleDateString());break;case 3:t=a,o=new Date(a.getFullYear(),a.getMonth()+1,a.getDate()),console.log("时间筛选: 1月内 -",t.toLocaleDateString(),"到",o.toLocaleDateString());break;case 4:t=a,o=new Date(a.getFullYear()+1,a.getMonth(),a.getDate()),console.log("时间筛选: 1年内 -",t.toLocaleDateString(),"到",o.toLocaleDateString());break;default:return console.log("时间筛选: 全部时间"),null}return{timeRangeStart:t.toISOString(),timeRangeEnd:o.toISOString()}},w=async(t=!1)=>{if(!i.value){i.value=!0;try{const e=((e=!1)=>{const a={pageNum:e?m.value.pageNum:1,pageSize:m.value.pageSize};u.value.trim()&&(a.title=u.value.trim());const t=1===s.value?p.value:d.value;if(t.location>1){const e={2:"北京",3:"上海",4:"广州",5:"深圳"};if(e[t.location])a.location=e[t.location];else if(t.location>=100){const e=t.location-100;e<f.value.length&&(a.location=f.value[e])}}if(0===s.value&&t.status>1){const e={2:0,3:1,4:2};e.hasOwnProperty(t.status)?(a.registrationStatus=e[t.status],console.log("用户选择了具体报名状态筛选:",t.status,"-> registrationStatus:",a.registrationStatus)):console.warn("未知的报名状态筛选值:",t.status)}else 0===s.value?console.log("用户选择了全部状态，不传递registrationStatus参数，显示所有已上架活动"):1===s.value&&console.log("日历视图：不传递registrationStatus参数，显示所有状态的活动");if(1===s.value)a.orderBy="startTime",a.isAsc="asc";else switch(t.sortBy){case 1:a.orderBy="comprehensive";break;case 2:a.orderBy="startTime",a.isAsc="asc",console.log("按时间排序: 最近开始优先");break;case 3:a.orderBy="createTime",a.isAsc="desc",console.log("按最新发布排序: 最新创建的活动优先");break;default:a.orderBy="createTime",a.isAsc="desc",console.log("默认排序: 最新发布")}if(t.timeRange>1){const e=b(t.timeRange);e&&Object.assign(a,e)}return a})(t);let o;console.log("请求参数:",e),o=await a.getEventListApi(e);const{rows:l=[],total:n=0}=o;t?r.value.push(...l):(r.value=l,m.value.pageNum=1),m.value.total=n,m.value.hasMore=r.value.length<n,v.value=!1,console.log(`获取活动列表成功: ${l.length} 条记录, 总计: ${n}`)}catch(o){console.error("获取活动列表失败:",o);let a="获取活动列表失败";o.message&&o.message.includes("timeout")?a="网络请求超时，请重试":o.message&&o.message.includes("Network")&&(a="网络连接失败，请检查网络"),e.index.showToast({title:a,icon:"none",duration:3e3}),t||0!==r.value.length||(v.value=!0)}finally{i.value=!1,c.value=!1}}},A=t.debounce((()=>{w()}),500),D=e=>{s.value=e;const a=1===e?20:l.PAGE_CONFIG.DEFAULT_PAGE_SIZE;r.value=[],m.value.pageNum=1,m.value.pageSize=a,m.value.hasMore=!0,w()},x=e=>{u.value=e,A()},M=()=>{console.log("筛选条件变更，重置数据并重新加载"),r.value=[],m.value.pageNum=1,m.value.hasMore=!0,w()},_=e=>{console.log("接收到来自筛选组件的新条件:",e),d.value={...e},M()},T=e=>{console.log("接收到来自日历筛选组件的新条件:",e),p.value={...e},M()},j=a=>{e.index.navigateTo({url:`/pages_sub/pages_event/detail?id=${a.id}`})},E=()=>{c.value=!0,m.value.pageNum=1,w()},C=()=>{m.value.hasMore&&!i.value&&(m.value.pageNum++,w(!0))};return e.onLoad((()=>{console.log("活动列表页 onLoad - 页面首次加载"),e.index.showShareMenu({menus:["shareAppMessage","shareTimeline"]});const t=e.index.getStorageSync("staticAssets");g.value=(null==t?void 0:t.eventbg)||"",(async()=>{try{const t=await a.getEventCitiesApi();let o=null;if(Array.isArray(t)?o=t:(t&&Array.isArray(t.data)||t&&200===t.code&&Array.isArray(t.data))&&(o=t.data),console.log("提取的城市列表:",o),o&&Array.isArray(o)){const e=["北京","上海","广州","深圳"],a=o.filter((a=>a&&a.trim()&&!e.includes(a.trim())));f.value=a}else e.index.showToast({title:"城市数据格式错误",icon:"none",duration:2e3})}catch(t){console.error("获取城市列表失败:",t),e.index.showToast({title:"获取城市列表失败",icon:"none",duration:2e3})}})(),w(),e.index.$on("dataChanged",(e=>{console.log("活动列表页收到数据变化事件，静默刷新列表...",e),w()}))})),e.onShow((()=>{e.index.hideTabBar()})),e.onUnload((()=>{e.index.$off("dataChanged")})),e.onReachBottom((()=>{C()})),e.onPullDownRefresh((()=>{E(),setTimeout((()=>{e.index.stopPullDownRefresh()}),1e3)})),e.onShareAppMessage((()=>({}))),(a,t)=>e.e({a:g.value,b:e.o(D),c:e.p({list:["列表","日历"],current:s.value,mode:"subsection",activeColor:"#f56c6c"}),d:e.o(x),e:e.o(e.unref(A)),f:e.o((e=>u.value=e)),g:e.p({placeholder:"搜索活动",modelValue:u.value}),h:0===s.value},0===s.value?{i:e.o(_),j:e.p({"initial-filters":d.value,cities:f.value})}:{},{k:0===s.value},0===s.value?e.e({l:!i.value&&0===r.value.length},i.value||0!==r.value.length?{}:e.e({m:v.value},v.value?{n:e.o(w),o:e.p({type:"primary",size:"normal"})}:{}),{p:e.f(r.value,((a,t,o)=>({a:a.id,b:e.o((e=>j(a)),a.id),c:"a2b67ae8-4-"+o,d:e.p({event:a})}))),q:"nomore"!==S.value},"nomore"!==S.value?{r:e.p({status:S.value,"loading-text":"正在加载...","loadmore-text":"上拉加载更多","nomore-text":"没有更多了"})}:{},{s:"nomore"===S.value},(S.value,{}),{t:e.o(C),v:c.value,w:e.o(E)}):{},{x:1===s.value},1===s.value?{y:e.o(T),z:e.o((e=>h.value=e)),A:e.p({"view-type":"calendar","initial-filters":p.value,cities:f.value,"notch-left":h.value})}:{},{B:1===s.value},1===s.value?e.e({C:!i.value&&0===y.value.length},i.value||0!==y.value.length?{H:e.o(j),I:e.p({groups:y.value,"has-more":m.value.hasMore,"is-loading":i.value,"notch-left":h.value})}:e.e({D:!v.value},(v.value,{}),{E:v.value},v.value?{F:e.o(w),G:e.p({type:"primary",size:"normal"})}:{}),{J:e.o(C),K:c.value,L:e.o(E)}):{},{M:e.p({current:2})})}},g=e._export_sfc(v,[["__scopeId","data-v-a2b67ae8"]]);v.__runtimeHooks=2,wx.createPage(g);
