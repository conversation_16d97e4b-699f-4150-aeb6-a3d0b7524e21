<template>
  <view class="orders-page">
    <!-- 顶部导航栏 -->
    <up-navbar 
      title="报名订单" 
        :autoBack="true" 
        :fixed="true" 
        :safeAreaInsetTop="true" 
        :placeholder="true"
        bgColor="#ffffff" 
        leftIcon="arrow-left" 
        leftIconColor="#333333"
        titleStyle="font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30'; font-weight: normal; font-size: 32rpx; color: #000000; line-height: 44rpx; text-align: center; font-style: normal; text-transform: none;"
    />
    
    <!-- 主内容区域 -->
    <scroll-view 
      scroll-y 
      class="orders-list-scroll" 
      @scrolltolower="onLoadMore"
      refresher-enabled 
      :refresher-triggered="isRefreshing" 
      @refresherrefresh="onRefresh"
    >
      <!-- 加载状态 -->
      <view v-if="loading && registrationList.length === 0" class="loading-state">
        <up-loading-page :loading="true" loading-text="加载中..." />
      </view>
      
      <!-- 空状态 -->
      <view v-else-if="!loading && registrationList.length === 0" class="empty-state">
        <up-empty 
          mode="list" 
          :text="isLoggedIn ? '暂无报名记录' : '请先登录查看报名记录'" 
          textColor="#909399" 
          iconSize="120"
        >
          <template #bottom v-if="!isLoggedIn">
            <up-button type="primary" text="去登录" @click="goToLogin" />
          </template>
        </up-empty>
      </view>
      
      <!-- 报名订单列表 -->
      <view v-else class="orders-list">
        <view
          v-for="item in registrationList"
          :key="item.id"
          class="order-card"
          @click="goToEventDetail(item.eventId)"
        >
          <!-- 左侧：活动封面 -->
          <view class="card-left">
            <image
              :src="item.coverImageUrl"
              mode="aspectFill"
              class="event-image"
              :lazy-load="true"
              @error="onImageError"
              @load="onImageLoad"
            />
          </view>

          <!-- 右侧：活动信息 -->
          <view class="card-right">
            <!-- 活动标题 -->
            <text class="event-title">{{ item.title || '活动标题' }}</text>

            <!-- 报名时间 -->
            <view class="event-info registration-time">
              <text class="time-label">报名时间：</text>
              <text class="time-value">{{ formatRegistrationTime(item.registrationTime) }}</text>
            </view>

            <!-- 底部行：只显示状态标签 -->
            <view class="card-bottom-row">
              <view>
                <!-- 已报名状态使用背景图片 -->
                <view
                  v-if="item.status === 0"
                  class="status-with-bg"
                >
                  <image
                    class="status-bg-image"
                    :src="orderStatusBgUrl"
                    mode="aspectFit"
                  ></image>
                  <text class="status-text">已报名</text>
                </view>
                <!-- 已取消状态使用灰色背景 -->
                <view
                  v-else-if="item.status === 1"
                  class="status-cancelled"
                >
                  <text class="status-text">已取消</text>
                </view>
                <!-- 其他状态使用普通标签 -->
                <view
                  v-else
                  :class="['registration-status-tag', getRegistrationStatusClass(item.status)]"
                >
                  {{ formatRegistrationStatus(item.status) }}
                </view>
              </view>
            </view>
          </view>

          <!-- 独立的取消报名按钮 - 基于卡片容器绝对定位 -->
          <view
            v-if="item.status === 0"
            class="cancel-btn-absolute"
            @click.stop="showCancelConfirm(item)"
          >
            取消报名
          </view>
        </view>
      </view>
      
      <!-- 加载更多组件 -->
      <view class="loadmore-wrapper" v-if="registrationList.length > 0">
        <up-loadmore 
          :status="loadMoreStatus" 
          :loading-text="'正在加载...'" 
          :loadmore-text="'上拉加载更多'"
          :nomore-text="'没有更多了'" 
        />
      </view>
    </scroll-view>
    
    <!-- 自定义底部导航栏 -->
    <CustomTabBar :current="4" />

    <!-- 自定义取消报名确认弹窗 -->
    <view v-if="showCancelModal" class="cancel-modal-overlay" @click="closeCancelModal">
      <view class="cancel-modal-content" @click.stop>
        <!-- 警告图标和标题 -->
        <view class="modal-header">
          <image class="warning-icon" :src="ordersWarningIconUrl" mode="aspectFit"></image>
          <text class="modal-title">操作提示</text>
        </view>

        <!-- 提示内容 -->
        <view class="modal-body">
          <text class="modal-message">是否取消报名？</text>
        </view>

        <!-- 按钮组 -->
        <view class="modal-footer">
          <view class="modal-btn cancel-btn" @click="closeCancelModal">
            暂不取消
          </view>
          <view class="modal-btn confirm-btn" @click="confirmCancelRegistration">
            确认取消
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {computed, ref} from 'vue';
import {onLoad, onPullDownRefresh, onShow, onShareAppMessage} from '@dcloudio/uni-app';
import CustomTabBar from '@/components/layout/CustomTabBar.vue';
import {getMyRegistrationsApi, cancelRegistrationApi} from '@/api/data/registration.js';
import {getEventDetailApi} from '@/api/data/event.js';
import {getFullImageUrl} from '@/utils/image.js';

// ==================== 响应式数据定义 ====================
const loading = ref(false);
const isRefreshing = ref(false);
const registrationList = ref([]);
const loginCheckTrigger = ref(0); 
const showCancelModal = ref(false);
const currentCancelItem = ref(null);
// 订单页静态资源（暗号映射）
const ordersWarningIconUrl = ref(''); // 暗号：orders_warning
const orderStatusBgUrl = ref(''); // 暗号：order_bg

// 解析静态资源缓存：仅读取缓存暗号
const resolveAssetUrl = (assetKey) => {
  const assets = uni.getStorageSync('staticAssets');
  return (assets && assets[assetKey]) ? assets[assetKey] : '';
};

// 刷新本页静态资源（暗号：orders_warning、order_bg）
const refreshOrdersAssets = () => {
  ordersWarningIconUrl.value = resolveAssetUrl('orders_warning');
  orderStatusBgUrl.value = resolveAssetUrl('order_bg');
};

// ==================== 计算属性 ====================
const loadMoreStatus = computed(() => {
  if (loading.value) return 'loading';
  return 'nomore'; // 报名记录通常不需要分页
});

// 从本地存储获取用户token
const getUserToken = () => {
  try {
    return uni.getStorageSync('token') || null;
  } catch (e) {
    return null;
  }
};

// 检查用户是否已登录
const isLoggedIn = computed(() => {
  // 通过 loginCheckTrigger 强制重新计算
  loginCheckTrigger.value;
  return !!getUserToken();
});

// ==================== 核心方法 ====================
/**
 * 检查登录状态并刷新数据
 */
const checkLoginAndRefresh = async () => {
  console.log('=== 检查登录状态并刷新数据 ===');
  
  // 强制触发登录状态重新计算
  loginCheckTrigger.value++;
  
  const currentToken = getUserToken();
  console.log('当前登录状态:', !!currentToken);
  
  if (!currentToken) {
    console.log('用户未登录，清空数据并跳转到登录页');
    registrationList.value = [];
    
    uni.showModal({
      title: '提示',
      content: '请先登录后查看报名记录',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 记录登录前的页面，便于登录成功后正确返回
          uni.setStorageSync('loginBackPage', '/pages/profile/orders');
          uni.navigateTo({ url: '/pages/login/index' });
        } else {
          // 用户取消，尝试返回上一页，如果失败则跳转到首页
          uni.navigateBack({
            fail: () => {
              uni.switchTab({ url: '/pages/index/index' });
            }
          });
        }
      }
    });
    return;
  }
  
  // 用户已登录，刷新数据
  await loadMyRegistrations();
};

/**
 * 获取我的报名订单 
 */
const loadMyRegistrations = async () => {
  if (loading.value) return;
  
  // 首先检查登录状态
  if (!isLoggedIn.value) {
    console.log('用户未登录，跳过数据加载');
    registrationList.value = [];
    return;
  }
  
  loading.value = true;
  
  try {
    // 直接调用现有接口
    const response = await getMyRegistrationsApi();
    
    if (response.code === 200) {
      // 直接使用返回数据，按时间倒序
      const rawData = response.data || [];
      console.log('获取报名记录成功:', rawData.length, '条');
      
      // 如果有数据，尝试获取活动标题信息
      if (rawData.length > 0) {
        registrationList.value = await enrichRegistrationData(rawData);
      } else {
        registrationList.value = [];
      }
    } else {
      console.log('暂无报名记录');
      registrationList.value = [];
    }
    
  } catch (error) {
    console.error('获取报名订单失败:', error);
    
    // 如果是401错误（未登录），特殊处理
    if (error.message && error.message.includes('未登录')) {
      console.log('检测到登录状态异常，清除token并提示重新登录');
      uni.removeStorageSync('token');
      loginCheckTrigger.value++; // 触发登录状态重新计算
      
      uni.showModal({
        title: '登录过期',
        content: '登录状态已过期，请重新登录',
        confirmText: '重新登录',
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            // 记录登录前的页面，便于登录成功后正确返回
            uni.setStorageSync('loginBackPage', '/pages/profile/orders');
            uni.navigateTo({ url: '/pages/login/index' });
          }
        }
      });
      return;
    }
    
    handleError(error, '获取报名记录');
  } finally {
    loading.value = false;
    isRefreshing.value = false;
  }
};

/**
 * 简化数据增强：只获取必要的活动信息
 */
const enrichRegistrationData = async (registrations) => {
  // 先按报名时间倒序排列
  const sortedRegistrations = registrations.sort((a, b) => {
    const db = parseSafeDate(b.registrationTime)?.getTime() ?? 0;
    const da = parseSafeDate(a.registrationTime)?.getTime() ?? 0;
    return db - da;
  });
  
  // 异步获取每个活动的基本信息
  const enrichedData = await Promise.allSettled(
    sortedRegistrations.map(async (registration) => {
      try {
        // 调用活动详情接口获取标题和封面
        const eventDetailResponse = await getEventDetailApi(registration.eventId);
        
        if (eventDetailResponse.code === 200 && eventDetailResponse.data) {
          const eventInfo = eventDetailResponse.data;
          return {
            ...registration,
            title: eventInfo.title || '活动标题',
            coverImageUrl: getFullImageUrl(eventInfo.coverImageUrl),
            location: eventInfo.location || '待定',
            startTime: eventInfo.startTime
          };
        } else {
          // 如果获取失败，使用默认值
          return {
            ...registration,
            title: '活动标题',
            coverImageUrl: '',
            location: '待定'
          };
        }
      } catch (error) {
        console.warn('获取活动详情失败:', registration.eventId, error);
        // 出错时使用默认值
        return {
          ...registration,
          title: '活动标题',
            coverImageUrl: '',
          location: '待定'
        };
      }
    })
  );
  
  // 过滤成功的结果
  return enrichedData
    .filter(result => result.status === 'fulfilled')
    .map(result => result.value);
};



/**
 * 跳转到活动详情页
 */
const goToEventDetail = (eventId) => {
  uni.navigateTo({
    url: `/pages_sub/pages_event/detail?id=${eventId}`
  });
};

/**
 * 显示取消报名确认弹窗
 */
const showCancelConfirm = (item) => {
  currentCancelItem.value = item;
  showCancelModal.value = true;
};

/**
 * 关闭自定义取消弹窗
 */
const closeCancelModal = () => {
  showCancelModal.value = false;
  currentCancelItem.value = null;
};

/**
 * 确认取消报名
 */
const confirmCancelRegistration = () => {
  if (currentCancelItem.value) {
    cancelRegistration(currentCancelItem.value);
    closeCancelModal();
  }
};

/**
 * 取消报名
 */
const cancelRegistration = async (item) => {
  try {
    // 显示加载提示
    uni.showLoading({
      title: '取消中...',
      mask: true
    });

    // 调用取消报名API
    const response = await cancelRegistrationApi({
      eventId: item.eventId
    });

    uni.hideLoading();

    if (response.code === 200) {
      // 取消成功，更新本地数据
      const index = registrationList.value.findIndex(reg => 
        reg.eventId === item.eventId && reg.userId === item.userId
      );
      
      if (index !== -1) {
        // 更新状态为已取消
        registrationList.value[index].status = 1;
      }

      uni.showToast({
        title: '取消报名成功',
        icon: 'success',
        duration: 2000
      });
    } else {
      throw new Error(response.msg || '取消报名失败');
    }
  } catch (error) {
    uni.hideLoading();
    console.error('取消报名失败:', error);
    
    uni.showToast({
      title: error.message || '取消报名失败，请稍后重试',
      icon: 'none',
      duration: 3000
    });
  }
};

/**
 * 下拉刷新
 */
const onRefresh = () => {
  isRefreshing.value = true;
  checkLoginAndRefresh();
};

/**
 * 跳转到登录页
 */
const goToLogin = () => {
  uni.navigateTo({ url: '/pages_sub/pages_other/login' });
};

/**
 * 图片加载成功事件
 */
const onImageLoad = (e) => {
  console.log('图片加载成功');
};

/**
 * 图片加载失败事件
 */
const onImageError = (e) => {
  console.error('图片加载失败:', e);
};

/**
 * 上拉加载更多（暂时不需要）
 */
const onLoadMore = () => {
  // 报名记录通常不需要分页
};

/**
 * 兼容 iOS 的安全日期解析：
 * - 优先将 "yyyy-MM-dd HH:mm:ss" 转为 "yyyy-MM-ddTHH:mm:ss"
 * - 若仍失败，回退为 "yyyy/MM/dd HH:mm:ss" 形式
 * - 同时兼容时间戳与 Date 输入
 */
const parseSafeDate = (input) => {
  if (!input) return null;
  if (input instanceof Date) {
    return isNaN(input.getTime()) ? null : input;
  }
  if (typeof input === 'number') {
    const d = new Date(input);
    return isNaN(d.getTime()) ? null : d;
  }
  if (typeof input === 'string') {
    let s = input.trim();
    // 将 "yyyy-MM-dd HH:mm:ss" 转为 "yyyy-MM-ddTHH:mm:ss"
    if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(s)) {
      s = s.replace(' ', 'T');
    }
    let d = new Date(s);
    if (isNaN(d.getTime())) {
      // 回退：替换日期分隔符为斜杠，适配 iOS
      const m = s.match(/^(\d{4})-(\d{1,2})-(\d{1,2})(?:[ T](\d{1,2}):(\d{2})(?::(\d{2}))?)?/);
      if (m) {
        const y = m[1];
        const mo = m[2];
        const day = m[3];
        const rest = m[4] ? ` ${m[4]}:${m[5]}:${m[6] || '00'}` : '';
        d = new Date(`${y}/${mo}/${day}${rest}`);
      }
    }
    return isNaN(d.getTime()) ? null : d;
  }
  return null;
};

/**
 * 格式化报名时间
 */
const formatRegistrationTime = (time) => {
  if (!time) return '';
  const date = parseSafeDate(time);
  if (!date) return '';
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

/**
 * 格式化报名状态
 */
const formatRegistrationStatus = (status) => {
  return status === 0 ? '已报名' : '已取消';
};

/**
 * 获取报名状态样式类
 */
const getRegistrationStatusClass = (status) => {
  return status === 0 ? 'registered' : 'cancelled';
};

/**
 * 统一错误处理
 */
const handleError = (error, context = '') => {
  console.error(`${context} 错误:`, error);
  
  let message = '操作失败，请稍后重试';
  
  if (error.code) {
    switch (error.code) {
      case 401:
        message = '登录已过期，请重新登录';
        uni.reLaunch({ url: '/pages/login/index' });
        return;
      case 403:
        message = '权限不足';
        break;
      case 404:
        message = '请求的资源不存在';
        break;
      case 500:
        message = '服务器错误，请稍后重试';
        break;
      default:
        message = error.message || message;
    }
  }
  
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 3000
  });
};

// ==================== 生命周期 ====================
onLoad(() => {
  console.log('报名订单页 onLoad - 页面首次加载');
  uni.showShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    });

  refreshOrdersAssets();
  checkLoginAndRefresh();
});

onShow(async () => {
  // console.log('=== 我的报名订单页面 onShow 触发 ===');
  
  // 检查是否从登录页返回
  try {
    const loginBackPage = uni.getStorageSync('loginBackPage');
    if (loginBackPage === '/pages/profile/orders') {
      // console.log('检测到从登录页返回，清除标记并刷新数据');
      uni.removeStorageSync('loginBackPage');
      
      // 强制触发登录状态重新计算
      loginCheckTrigger.value++;
      
      // 直接加载数据，不需要再次检查登录状态
      await loadMyRegistrations();
      return;
    }
  } catch (e) {
    console.warn('检查登录返回标记失败:', e);
  }
  
  // 正常的登录状态检查
  checkLoginAndRefresh();
  refreshOrdersAssets();
});

onPullDownRefresh(async () => {
  await onRefresh();
  setTimeout(() => {
    uni.stopPullDownRefresh();
  }, 1000);
});

onShareAppMessage(() => {
  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为
});
</script>

<style lang="scss" scoped>
.orders-page {
  width: 750rpx;
    /* 如果希望高度是固定的，请使用 height。如果希望高度能自适应内容，请使用 min-height */
    height: 1624rpx; 
    background: #FFFFFF;
    border-radius: 0rpx; /* border-radius: 0rpx 0rpx 0rpx 0rpx; 的简写 */
    
    /* 以下为原有的flex布局，建议保留以维持页面结构 */
    display: flex;
    flex-direction: column;
}

.orders-list-scroll {
  flex: 1;
   box-sizing: border-box;
   padding: 0; /* 移除所有内边距，特别是左右的 */
   padding-bottom: 180rpx; // 仅保留底部的，为导航栏留出空间
}

.loading-state {
  padding: 100rpx 0;
}

.empty-state {
  padding: 100rpx 0;
  text-align: center;
}

.orders-list {
  .order-card {
    position: relative; 
    display: flex;
    padding: 24rpx; 
    margin: 0 0 2rpx 0;
    background-color: #ffffff;
    border-radius: 0; 
    box-shadow: none;
    border-bottom: 1rpx solid #F0F0F0; 
    & + .order-card {
      margin-top: 24rpx;
    }
  }
}



.card-left {
 position: relative;
   width: 336rpx;
   height: 192rpx;
   flex-shrink: 0;
 
   .event-image {
     width: 100%;
     height: 100%;
     display: block;
     border-radius: 19rpx; 
   }

}

.card-right {
  flex: 1;
  padding: 20rpx 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-top: -15rpx;
  
  .event-title {
    width: 346rpx;
      height: 80rpx;
      font-family: 'Alibaba PuHuiTi', sans-serif; 
      font-weight: 400;
      font-size: 28rpx;
      color: #23232A;
      text-align: justified;
      font-style: normal;
      text-transform: none;
      line-height: 40rpx; 
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
  }
  
  .registration-time {
	font-family: 'Alibaba PuHuiTi 3.0', sans-serif;
	font-weight: normal; 
	font-size: 22rpx;   
	color: #9B9A9A;
	line-height: normal;
	margin-top: 8rpx;

	.time-value {
		font-weight: inherit;
		color: inherit;
	}
  }
}

// 新增：底部行样式
.card-bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx; 
}

// 带背景图的状态样式 - 改为蓝色背景
.status-with-bg {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 36rpx;
  background: #023F98;

  .status-bg-image {
    display: none;
  }

  .status-text {
    color: #ffffff;
    font-size: 24rpx;
    font-weight: 500;
    white-space: nowrap;
  }
}

// 已取消状态样式
.status-cancelled {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 36rpx;
  background: #CBCBCB;
  // border-radius: 8rpx;

  .status-text {
    color: #ffffff;
    font-size: 24rpx;
    font-weight: 500;
    white-space: nowrap;
  }
}

.registration-status-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 24rpx; 
  padding: 4rpx 12rpx;
  border-radius: 8rpx; 
  font-weight: 500;
  height: 36rpx;
  box-sizing: border-box;

  &.registered {
    background-color: #409eff; 
  }

  &.cancelled {
    background-color: #909399;
  }
}

.card-actions {
  display: flex;
  align-items: center;
}

.loadmore-wrapper {
  padding: 20rpx 0 40rpx 0;
}

/* 绝对定位的取消报名按钮样式 */
.cancel-btn-absolute {
  position: absolute;
  right: 24rpx; 
  bottom: 44rpx;

  /* 布局和尺寸 */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 96rpx;
  height: 36rpx;
  box-sizing: border-box;

  /* 字体和外观 */
  font-family: 'Alibaba PuHuiTi', sans-serif; 
  font-weight: 400;
  font-size: 24rpx;
  color: #66666E;
  text-align: left;
  font-style: normal;
  text-transform: none;
  letter-spacing: 0;

  /* 其他样式 */
  background: none;
  border: none;
  border-radius: 8rpx;

  /* 点击效果 */
  &:active {
    opacity: 0.7;
  }
}

/* 自定义取消报名弹窗样式 */
.cancel-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.cancel-modal-content {
  width: 654rpx;
  height: 420rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  position: relative;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.modal-header {
  position: absolute;
  top: 42rpx;
  left: 48rpx;
  display: flex;
  align-items: center;

  .warning-icon {
    width: 48rpx;
    height: 40rpx;
    margin-right: 16rpx;
  }

  .modal-title {
    width: 142rpx;
    height: 44rpx;
    font-family: 'Alibaba PuHuiTi', sans-serif; 
    font-weight: 500;
    font-size: 36rpx;
    color: #23232A;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
}

.modal-body {
  position: absolute;
  top: 176rpx;
  left: 50%;
  transform: translateX(-50%);

  .modal-message {
    font-size: 32rpx;
    color: #23232A;
    line-height: 44rpx;
    text-align: center;
	font-weight: normal;
    text-transform: none;
    display: block;
  }
}

.modal-footer {
  position: absolute;
  top: 316rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24rpx;

  .modal-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: opacity 0.2s;

    &:active {
      opacity: 0.8;
    }
  }

  .cancel-btn {
      width: 292rpx;
      height: 76rpx;
      background: rgba(42, 97, 241, 0.1); 
      border-radius: 8rpx;
      font-weight: normal; 
      font-size: 28rpx;
      color: #23232A;
      line-height: 44rpx;
  }

  .confirm-btn {
    width: 292rpx;
    height: 76rpx;
    background: #023F98;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: #ffffff;
  }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .order-card {
    .card-left {
      width: 200rpx;
      height: 160rpx;
    }

    .card-right {
      .event-title {
        font-size: 28rpx;
      }
    }
  }
}
</style>