"use strict";
const common_vendor = require("../../common/vendor.js");
const api_data_event = require("../../api/data/event.js");
const utils_tools = require("../../utils/tools.js");
const utils_date = require("../../utils/date.js");
const utils_config = require("../../utils/config.js");
if (!Array) {
  const _easycom_up_subsection2 = common_vendor.resolveComponent("up-subsection");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  const _easycom_up_loadmore2 = common_vendor.resolveComponent("up-loadmore");
  (_easycom_up_subsection2 + _easycom_up_button2 + _easycom_up_loadmore2)();
}
const _easycom_up_subsection = () => "../../uni_modules/uview-plus/components/u-subsection/u-subsection.js";
const _easycom_up_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_up_loadmore = () => "../../uni_modules/uview-plus/components/u-loadmore/u-loadmore.js";
if (!Math) {
  (_easycom_up_subsection + CustomSearchBox + EventFilter + _easycom_up_button + EventCard + _easycom_up_loadmore + NoMoreDivider + EventCalendarTimeline + CustomTabBar)();
}
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const CustomSearchBox = () => "../../components/home/<USER>";
const EventCard = () => "../../components/event/EventCard.js";
const EventCalendarTimeline = () => "../../components/event/EventCalendarTimeline.js";
const EventFilter = () => "../../components/event/EventFilter.js";
const NoMoreDivider = () => "../../components/common/NoMoreDivider.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    const searchKeyword = common_vendor.ref("");
    const eventList = common_vendor.ref([]);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const showRetry = common_vendor.ref(false);
    const eventBgUrl = common_vendor.ref("");
    const pagination = common_vendor.ref({
      pageNum: 1,
      pageSize: currentTab.value === 1 ? 20 : utils_config.PAGE_CONFIG.DEFAULT_PAGE_SIZE,
      // 日历视图20个，列表视图使用默认
      total: 0,
      hasMore: true
    });
    const appliedFiltersList = common_vendor.ref({
      sortBy: 1,
      location: 1,
      timeRange: 1,
      status: 1
    });
    const appliedFiltersCalendar = common_vendor.ref({
      location: 1,
      timeRange: 1
    });
    const calendarNotchLeft = common_vendor.ref("60rpx");
    const otherCities = common_vendor.ref([]);
    common_vendor.computed(() => [
      allRegionOption.value,
      ...hotCities.value,
      ...otherCities.value.map((city, index) => ({
        label: city,
        value: 100 + index
        // 避免与热门城市ID冲突
      }))
    ]);
    common_vendor.ref([
      {
        label: "全部时间",
        value: 1
      },
      {
        label: "1周内",
        value: 2
      },
      {
        label: "1月内",
        value: 3
      },
      {
        label: "1年内",
        value: 4
      }
    ]);
    const loadMoreStatus = common_vendor.computed(() => {
      if (isLoading.value)
        return "loading";
      if (!pagination.value.hasMore)
        return "nomore";
      return "more";
    });
    const groupedEvents = common_vendor.computed(() => {
      if (!eventList.value || eventList.value.length === 0) {
        return [];
      }
      const groups = /* @__PURE__ */ new Map();
      const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      eventList.value.forEach((event) => {
        const eventDate = utils_date.parseDate(event.startTime);
        const year = eventDate.getFullYear();
        const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
        const month = String(eventDate.getMonth() + 1).padStart(2, "0");
        const day = String(eventDate.getDate()).padStart(2, "0");
        let displayDate;
        if (year !== currentYear) {
          displayDate = `${year}年${month}月${day}日`;
        } else {
          displayDate = `${month}.${day}`;
        }
        const monthLocal = String(eventDate.getMonth() + 1).padStart(2, "0");
        const dayLocal = String(eventDate.getDate()).padStart(2, "0");
        const dateKey = `${eventDate.getFullYear()}-${monthLocal}-${dayLocal}`;
        if (!groups.has(dateKey)) {
          groups.set(dateKey, {
            date: dateKey,
            formattedDate: displayDate,
            // 使用新的日期格式
            dayOfWeek: weekdays[eventDate.getDay()],
            events: []
          });
        }
        groups.get(dateKey).events.push(event);
      });
      return Array.from(groups.values());
    });
    common_vendor.computed(() => {
      return groupedEvents.value;
    });
    common_vendor.computed(() => {
      return pagination.value.hasMore;
    });
    const fetchEventCities = async () => {
      try {
        const response = await api_data_event.getEventCitiesApi();
        let cityList = null;
        if (Array.isArray(response)) {
          cityList = response;
        } else if (response && Array.isArray(response.data)) {
          cityList = response.data;
        } else if (response && response.code === 200 && Array.isArray(response.data)) {
          cityList = response.data;
        }
        common_vendor.index.__f__("log", "at pages/event/index.vue:299", "提取的城市列表:", cityList);
        if (cityList && Array.isArray(cityList)) {
          const hotCityNames = ["北京", "上海", "广州", "深圳"];
          const filteredCities = cityList.filter(
            (city) => city && city.trim() && !hotCityNames.includes(city.trim())
          );
          otherCities.value = filteredCities;
        } else {
          common_vendor.index.showToast({
            title: "城市数据格式错误",
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/event/index.vue:317", "获取城市列表失败:", error);
        common_vendor.index.showToast({
          title: "获取城市列表失败",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const buildQueryParams = (isLoadMore = false) => {
      const params = {
        pageNum: isLoadMore ? pagination.value.pageNum : 1,
        pageSize: pagination.value.pageSize
      };
      if (searchKeyword.value.trim()) {
        params.title = searchKeyword.value.trim();
      }
      const filters = currentTab.value === 1 ? appliedFiltersCalendar.value : appliedFiltersList.value;
      if (filters.location > 1) {
        const hotLocationMap = {
          2: "北京",
          3: "上海",
          4: "广州",
          5: "深圳"
        };
        if (hotLocationMap[filters.location]) {
          params.location = hotLocationMap[filters.location];
        } else if (filters.location >= 100) {
          const otherIndex = filters.location - 100;
          if (otherIndex < otherCities.value.length) {
            params.location = otherCities.value[otherIndex];
          }
        }
      }
      if (currentTab.value === 0 && filters.status > 1) {
        const registrationStatusMap = {
          2: 0,
          // 即将开始 -> registrationStatus: 0
          3: 1,
          // 报名中 -> registrationStatus: 1
          4: 2
          // 报名截止 -> registrationStatus: 2
        };
        if (registrationStatusMap.hasOwnProperty(filters.status)) {
          params.registrationStatus = registrationStatusMap[filters.status];
          common_vendor.index.__f__("log", "at pages/event/index.vue:441", "用户选择了具体报名状态筛选:", filters.status, "-> registrationStatus:", params.registrationStatus);
        } else {
          common_vendor.index.__f__("warn", "at pages/event/index.vue:443", "未知的报名状态筛选值:", filters.status);
        }
      } else if (currentTab.value === 0) {
        common_vendor.index.__f__("log", "at pages/event/index.vue:446", "用户选择了全部状态，不传递registrationStatus参数，显示所有已上架活动");
      } else if (currentTab.value === 1) {
        common_vendor.index.__f__("log", "at pages/event/index.vue:448", "日历视图：不传递registrationStatus参数，显示所有状态的活动");
      }
      if (currentTab.value === 1) {
        params.orderBy = "startTime";
        params.isAsc = "asc";
      } else {
        switch (filters.sortBy) {
          case 1:
            params.orderBy = "comprehensive";
            break;
          case 2:
            params.orderBy = "startTime";
            params.isAsc = "asc";
            common_vendor.index.__f__("log", "at pages/event/index.vue:465", "按时间排序: 最近开始优先");
            break;
          case 3:
            params.orderBy = "createTime";
            params.isAsc = "desc";
            common_vendor.index.__f__("log", "at pages/event/index.vue:470", "按最新发布排序: 最新创建的活动优先");
            break;
          default:
            params.orderBy = "createTime";
            params.isAsc = "desc";
            common_vendor.index.__f__("log", "at pages/event/index.vue:475", "默认排序: 最新发布");
        }
      }
      if (filters.timeRange > 1) {
        const timeRange = buildTimeRangeParams(filters.timeRange);
        if (timeRange) {
          Object.assign(params, timeRange);
        }
      }
      return params;
    };
    const buildTimeRangeParams = (timeRangeValue) => {
      const now = /* @__PURE__ */ new Date();
      let startTime = null;
      let endTime = null;
      switch (timeRangeValue) {
        case 2:
          startTime = now;
          endTime = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1e3);
          common_vendor.index.__f__("log", "at pages/event/index.vue:504", "时间筛选: 1周内 -", startTime.toLocaleDateString(), "到", endTime.toLocaleDateString());
          break;
        case 3:
          startTime = now;
          endTime = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
          common_vendor.index.__f__("log", "at pages/event/index.vue:509", "时间筛选: 1月内 -", startTime.toLocaleDateString(), "到", endTime.toLocaleDateString());
          break;
        case 4:
          startTime = now;
          endTime = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
          common_vendor.index.__f__("log", "at pages/event/index.vue:514", "时间筛选: 1年内 -", startTime.toLocaleDateString(), "到", endTime.toLocaleDateString());
          break;
        default:
          common_vendor.index.__f__("log", "at pages/event/index.vue:517", "时间筛选: 全部时间");
          return null;
      }
      return {
        timeRangeStart: startTime.toISOString(),
        timeRangeEnd: endTime.toISOString()
      };
    };
    const fetchEventList = async (isLoadMore = false) => {
      if (isLoading.value)
        return;
      isLoading.value = true;
      try {
        const params = buildQueryParams(isLoadMore);
        common_vendor.index.__f__("log", "at pages/event/index.vue:537", "请求参数:", params);
        let response;
        response = await api_data_event.getEventListApi(params);
        const {
          rows = [],
          total = 0
        } = response;
        if (isLoadMore) {
          eventList.value.push(...rows);
        } else {
          eventList.value = rows;
          pagination.value.pageNum = 1;
        }
        pagination.value.total = total;
        pagination.value.hasMore = eventList.value.length < total;
        showRetry.value = false;
        common_vendor.index.__f__("log", "at pages/event/index.vue:559", `获取活动列表成功: ${rows.length} 条记录, 总计: ${total}`);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/event/index.vue:562", "获取活动列表失败:", error);
        let errorMessage = "获取活动列表失败";
        if (error.message && error.message.includes("timeout")) {
          errorMessage = "网络请求超时，请重试";
        } else if (error.message && error.message.includes("Network")) {
          errorMessage = "网络连接失败，请检查网络";
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none",
          duration: 3e3
        });
        if (!isLoadMore && eventList.value.length === 0) {
          showRetry.value = true;
        }
      } finally {
        isLoading.value = false;
        isRefreshing.value = false;
      }
    };
    const debouncedSearch = utils_tools.debounce(() => {
      fetchEventList();
    }, 500);
    const tabChange = (index) => {
      currentTab.value = index;
      const newPageSize = index === 1 ? 20 : utils_config.PAGE_CONFIG.DEFAULT_PAGE_SIZE;
      eventList.value = [];
      pagination.value.pageNum = 1;
      pagination.value.pageSize = newPageSize;
      pagination.value.hasMore = true;
      fetchEventList();
    };
    const onSearch = (value) => {
      searchKeyword.value = value;
      debouncedSearch();
    };
    const onFilterChange = () => {
      common_vendor.index.__f__("log", "at pages/event/index.vue:627", "筛选条件变更，重置数据并重新加载");
      eventList.value = [];
      pagination.value.pageNum = 1;
      pagination.value.hasMore = true;
      fetchEventList();
    };
    const handleFiltersApply = (newFilters) => {
      common_vendor.index.__f__("log", "at pages/event/index.vue:640", "接收到来自筛选组件的新条件:", newFilters);
      appliedFiltersList.value = { ...newFilters };
      onFilterChange();
    };
    const handleCalendarFiltersApply = (newFilters) => {
      common_vendor.index.__f__("log", "at pages/event/index.vue:650", "接收到来自日历筛选组件的新条件:", newFilters);
      appliedFiltersCalendar.value = { ...newFilters };
      onFilterChange();
    };
    const goToDetail = (event) => {
      common_vendor.index.navigateTo({
        url: `/pages_sub/pages_event/detail?id=${event.id}`
      });
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      pagination.value.pageNum = 1;
      fetchEventList();
    };
    const onLoadMore = () => {
      if (!pagination.value.hasMore || isLoading.value)
        return;
      pagination.value.pageNum++;
      fetchEventList(true);
    };
    common_vendor.onLoad(() => {
      common_vendor.index.__f__("log", "at pages/event/index.vue:686", "活动列表页 onLoad - 页面首次加载");
      common_vendor.index.showShareMenu({
        menus: ["shareAppMessage", "shareTimeline"]
      });
      const assets = common_vendor.index.getStorageSync("staticAssets");
      eventBgUrl.value = (assets == null ? void 0 : assets.eventbg) || "";
      fetchEventCities();
      fetchEventList();
      common_vendor.index.$on("dataChanged", (eventData) => {
        common_vendor.index.__f__("log", "at pages/event/index.vue:700", "活动列表页收到数据变化事件，静默刷新列表...", eventData);
        fetchEventList();
      });
    });
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
    });
    common_vendor.onUnload(() => {
      common_vendor.index.$off("dataChanged");
    });
    common_vendor.onReachBottom(() => {
      onLoadMore();
    });
    common_vendor.onPullDownRefresh(() => {
      onRefresh();
      setTimeout(() => {
        common_vendor.index.stopPullDownRefresh();
      }, 1e3);
    });
    common_vendor.onShareAppMessage(() => {
      return {};
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: eventBgUrl.value,
        b: common_vendor.o(tabChange),
        c: common_vendor.p({
          list: ["列表", "日历"],
          current: currentTab.value,
          mode: "subsection",
          activeColor: "#f56c6c"
        }),
        d: common_vendor.o(onSearch),
        e: common_vendor.o(common_vendor.unref(debouncedSearch)),
        f: common_vendor.o(($event) => searchKeyword.value = $event),
        g: common_vendor.p({
          placeholder: "搜索活动",
          modelValue: searchKeyword.value
        }),
        h: currentTab.value === 0
      }, currentTab.value === 0 ? {
        i: common_vendor.o(handleFiltersApply),
        j: common_vendor.p({
          ["initial-filters"]: appliedFiltersList.value,
          cities: otherCities.value
        })
      } : {}, {
        k: currentTab.value === 0
      }, currentTab.value === 0 ? common_vendor.e({
        l: !isLoading.value && eventList.value.length === 0
      }, !isLoading.value && eventList.value.length === 0 ? common_vendor.e({
        m: showRetry.value
      }, showRetry.value ? {
        n: common_vendor.o(fetchEventList),
        o: common_vendor.p({
          type: "primary",
          size: "normal"
        })
      } : {}) : {}, {
        p: common_vendor.f(eventList.value, (event, k0, i0) => {
          return {
            a: event.id,
            b: common_vendor.o(($event) => goToDetail(event), event.id),
            c: "8e954d49-4-" + i0,
            d: common_vendor.p({
              event
            })
          };
        }),
        q: loadMoreStatus.value !== "nomore"
      }, loadMoreStatus.value !== "nomore" ? {
        r: common_vendor.p({
          status: loadMoreStatus.value,
          ["loading-text"]: "正在加载...",
          ["loadmore-text"]: "上拉加载更多",
          ["nomore-text"]: "没有更多了"
        })
      } : {}, {
        s: loadMoreStatus.value === "nomore"
      }, loadMoreStatus.value === "nomore" ? {} : {}, {
        t: common_vendor.o(onLoadMore),
        v: isRefreshing.value,
        w: common_vendor.o(onRefresh)
      }) : {}, {
        x: currentTab.value === 1
      }, currentTab.value === 1 ? {
        y: common_vendor.o(handleCalendarFiltersApply),
        z: common_vendor.o(($event) => calendarNotchLeft.value = $event),
        A: common_vendor.p({
          ["view-type"]: "calendar",
          ["initial-filters"]: appliedFiltersCalendar.value,
          cities: otherCities.value,
          ["notch-left"]: calendarNotchLeft.value
        })
      } : {}, {
        B: currentTab.value === 1
      }, currentTab.value === 1 ? common_vendor.e({
        C: !isLoading.value && groupedEvents.value.length === 0
      }, !isLoading.value && groupedEvents.value.length === 0 ? common_vendor.e({
        D: !showRetry.value
      }, !showRetry.value ? {} : {}, {
        E: showRetry.value
      }, showRetry.value ? {
        F: common_vendor.o(fetchEventList),
        G: common_vendor.p({
          type: "primary",
          size: "normal"
        })
      } : {}) : {
        H: common_vendor.o(goToDetail),
        I: common_vendor.p({
          groups: groupedEvents.value,
          ["has-more"]: pagination.value.hasMore,
          ["is-loading"]: isLoading.value,
          ["notch-left"]: calendarNotchLeft.value
        })
      }, {
        J: common_vendor.o(onLoadMore),
        K: isRefreshing.value,
        L: common_vendor.o(onRefresh)
      }) : {}, {
        M: common_vendor.p({
          current: 2
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8e954d49"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/event/index.js.map
