"use strict";const e=require("../../common/vendor.js"),o={__name:"index",setup(o){const r=e.ref("");return e.onLoad((o=>{o&&o.url?r.value=decodeURIComponent(o.url):(console.error("No url provided for webview."),e.index.showToast({title:"链接地址无效",icon:"error",duration:2e3}),e.index.setNavigationBarTitle({title:"页面加载失败"}))})),(o,t)=>e.e({a:r.value},r.value?{b:r.value}:{})}},r=e._export_sfc(o,[["__scopeId","data-v-ef3f8cdc"]]);wx.createPage(r);
