{"version": 3, "file": "contact.js", "sources": ["pages_sub/pages_profile/contact.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX3Byb2ZpbGVcY29udGFjdC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <view class=\"fixed-header\" :style=\"{ height: headerHeight + 'px' }\">\r\n      <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n      <view class=\"custom-nav-bar\" :style=\"{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }\">\r\n        <view class=\"nav-back-button\" @click=\"goBack\">\r\n          <uni-icons type=\"left\" color=\"#000000\" size=\"22\"></uni-icons>\r\n        </view>\r\n        <view class=\"nav-title\">联系顾问</view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y :show-scrollbar=\"false\" class=\"scrollable-content\" :style=\"{ paddingTop: headerHeight + 'px' }\">\r\n      <view v-if=\"loading\" class=\"loading-state\">\r\n        <uni-load-more status=\"loading\" :show-icon=\"true\"></uni-load-more>\r\n      </view>\r\n\r\n      <view v-if=\"!loading && consultant\" class=\"content-wrapper\">\r\n        <view class=\"header-text\">\r\n          Hi~ 很高兴为您服务\r\n        </view>\r\n\r\n        <view class=\"card\">\r\n          <view class=\"consultant-info\">\r\n            <image class=\"avatar\" :src=\"displayAvatarUrl\" mode=\"aspectFill\" @error=\"onImageError('avatar')\"></image>\r\n            <view class=\"details\">\r\n              <text class=\"name\">{{ consultant.name }}</text>\r\n              <view class=\"badge\">官方</view>\r\n            </view>\r\n          </view>\r\n\r\n          <text class=\"intro\">{{ consultant.introduction }}</text>\r\n\r\n          <view class=\"more-questions\">\r\n            更多问题，请添加顾问在线共解答\r\n          </view>\r\n\r\n          <view class=\"qr-code-section\">\r\n            <image class=\"qr-code\" :src=\"displayQrCodeUrl\" mode=\"aspectFit\" @error=\"onImageError('qrCode')\" @click=\"previewQrCode\" :show-menu-by-long-press=\"true\"></image>\r\n            <text class=\"qr-code-tip\">扫一扫</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view v-if=\"!loading && !consultant\" class=\"empty-state\">\r\n        <image class=\"empty-icon\" src=\"/static/images/empty-data.png\" mode=\"aspectFit\"></image>\r\n        <text class=\"empty-text\">暂无在线顾问，请稍后再试</text>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue';\r\nimport { getFullImageUrl } from '@/utils/image.js';\r\n\r\nimport { onLoad, onShareAppMessage } from '@dcloudio/uni-app';\r\nimport { getDisplayConsultantApi } from '@/pages_sub/pages_profile/api/platform/consultant.js';\r\n\r\n// --- 自定义导航栏相关逻辑 ---\r\nconst navBarPaddingBottomRpx = 20;\r\nconst navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);\r\nconst statusBarHeight = ref(0);\r\nconst navBarHeight = ref(0);\r\nconst headerHeight = ref(0);\r\n\r\nconst getNavBarInfo = () => {\r\n  try {\r\n    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n    statusBarHeight.value = menuButtonInfo.top;\r\n    navBarHeight.value = menuButtonInfo.height;\r\n    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;\r\n  } catch (e) {\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    statusBarHeight.value = systemInfo.statusBarHeight || 20;\r\n    navBarHeight.value = 44;\r\n    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;\r\n  }\r\n};\r\n\r\nconst goBack = () => {\r\n  uni.navigateBack({ delta: 1 });\r\n};\r\n\r\n// --- 核心业务逻辑 ---\r\nconst loading = ref(true);\r\nconst consultant = ref(null);\r\n\r\nconst displayAvatarUrl = computed(() => {\r\n  return consultant.value ? getFullImageUrl(consultant.value.avatarUrl) : '';\r\n});\r\n\r\nconst displayQrCodeUrl = computed(() => {\r\n  return consultant.value ? getFullImageUrl(consultant.value.qrCodeUrl) : '';\r\n});\r\n\r\nconst fetchConsultantData = async () => {\r\n  try {\r\n    const res = await getDisplayConsultantApi();\r\n    if (res.code === 200 && res.data) {\r\n      consultant.value = res.data;\r\n    } else {\r\n      consultant.value = null;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取顾问信息失败:', error);\r\n    consultant.value = null;\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst previewQrCode = () => {\r\n  if (displayQrCodeUrl.value) {\r\n    uni.previewImage({\r\n      urls: [displayQrCodeUrl.value]\r\n    });\r\n  }\r\n};\r\n\r\nconst onImageError = (type) => {\r\n  console.error(`${type} image failed to load.`);\r\n  if (type === 'avatar') {\r\n    consultant.value.avatarUrl = '/static/images/default-avatar.png';\r\n  } else if (type === 'qrCode') {\r\n    consultant.value.qrCodeUrl = '/static/images/default-qrcode.png';\r\n  }\r\n};\r\n\r\nonLoad(() => {\r\n  console.log('联系顾问页 onLoad - 页面首次加载');\r\n  uni.showShareMenu({\r\n      menus: ['shareAppMessage', 'shareTimeline']\r\n    });\r\n\r\n  getNavBarInfo();\r\n  loading.value = true;\r\n  fetchConsultantData();\r\n});\r\n\r\nonShareAppMessage(() => {\r\n  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* --- 页面布局及自定义导航栏样式 --- */\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background: linear-gradient(180deg, #FFDEA1 0%, #FFFFFF 50%);\r\n  font-style: normal;\r\n  text-transform: none;\r\n}\r\n\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n  background-color: transparent;\r\n}\r\n\r\n.scrollable-content {\r\n  flex: 1;\r\n  height: 0;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  padding: 32rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.status-bar {\r\n  width: 100%;\r\n}\r\n\r\n.custom-nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  box-sizing: content-box;\r\n}\r\n\r\n.nav-back-button {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15rpx;\r\n}\r\n\r\n.nav-title {\r\n  font-size: 32rpx;\r\n  color: #000000;\r\n  line-height: 44rpx;\r\n  font-family: 'Alibaba PuHuiTi Medium', 'Alibaba PuHuiTi', sans-serif;\r\n}\r\n/* --- 导航栏样式结束 --- */\r\n\r\n.loading-state, .empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: calc(100vh - 200rpx);\r\n}\r\n\r\n.empty-icon {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n}\r\n\r\n.empty-text {\r\n  margin-top: 24rpx;\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n.content-wrapper {\r\n  /* 此容器用于在滚动区内实现原有的布局 */\r\n}\r\n\r\n.header-text {\r\n  font-size: 48rpx;\r\n  font-weight: bold;\r\n  color: #23232A;\r\n  font-family: 'Alimama ShuHeiTi', 'Alimama ShuHeiTi', sans-serif;\r\n  margin: 20rpx 20rpx 40rpx 45rpx;\r\n}\r\n\r\n.card {\r\n  background: linear-gradient(180deg, #FFDEA1 0%, #FFBF51 100%);\r\n  border-radius: 24rpx;\r\n  padding: 40rpx;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\r\n  width: 600rpx;\r\n  height: 920rpx;\r\n  margin: 20rpx auto;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.consultant-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.avatar {\r\n  width: 88rpx;\r\n  height: 88rpx;\r\n  border-radius: 50%;\r\n  margin-right: 24rpx;\r\n  flex-shrink: 0;\r\n  border: 4rpx solid #ffffff;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.scrollable-content::-webkit-scrollbar {\r\n  display: none;\r\n  width: 0;\r\n  height: 0;\r\n  color: transparent;\r\n}\r\n\r\n/* [核心修改] 使用文本溢出三件套来显示省略号 */\r\n.name {\r\n  color: #452D03;\r\n  font-size: 28rpx;\r\n  font-family: 'Alibaba PuHuiTi Bold', 'Alibaba PuHuiTi', sans-serif;\r\n  font-weight: bold;\r\n  text-align: left;\r\n  margin-bottom: 8rpx; /* 稍微增加与徽章的间距 */\r\n\r\n  /* 新增/修改的样式 */\r\n  white-space: nowrap;      /* 强制不换行 */\r\n  overflow: hidden;         /* 隐藏溢出部分 */\r\n  text-overflow: ellipsis;  /* 显示省略号 */\r\n}\r\n\r\n.badge {\r\n  width: 72rpx;\r\n  height: 36rpx;\r\n  background-color: #023F98;\r\n  border-radius: 8rpx;\r\n  align-self: flex-start;\r\n\r\n  color: #FFFFFF;\r\n  font-size: 24rpx;\r\n  font-weight: normal;\r\n  font-style: normal;\r\n  text-transform: none;\r\n  line-height: normal;\r\n\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0;\r\n}\r\n\r\n.intro {\r\n  font-size: 24rpx;\r\n  color: #452D03;\r\n  line-height: 48rpx;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.more-questions {\r\n  font-size: 28rpx;\r\n  color: #023F98;\r\n  margin-top: 60rpx;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 48rpx;\r\n}\r\n\r\n.qr-code {\r\n  width: 328rpx;\r\n  height: 328rpx;\r\n  background-color: #fff;\r\n  border-radius: 30rpx;\r\n  box-sizing: border-box;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n  border: 8rpx solid #DAE6FF;\r\n}\r\n\r\n.qr-code-tip {\r\n  margin-top: 48rpx;\r\n  font-size: 24rpx;\r\n  color: #452D03;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_profile/contact.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ref", "computed", "getFullImageUrl", "getDisplayConsultantApi", "onLoad", "onShareAppMessage"], "mappings": ";;;;;;;;;;;;;;;AA4DA,MAAA,yBAAA;;;;AACA,UAAA,wBAAAA,cAAA,MAAA,OAAA,sBAAA;AACA,UAAA,kBAAAC,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AAEA,UAAA,gBAAA,MAAA;AACA,UAAA;AACA,cAAA,iBAAAD,oBAAA;AACA,wBAAA,QAAA,eAAA;AACA,qBAAA,QAAA,eAAA;AACA,qBAAA,QAAA,eAAA,SAAA;AAAA,MACA,SAAA,GAAA;AACA,cAAA,aAAAA,oBAAA;AACA,wBAAA,QAAA,WAAA,mBAAA;AACA,qBAAA,QAAA;AACA,qBAAA,QAAA,gBAAA,QAAA,aAAA,QAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,SAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA,EAAA,OAAA,EAAA,CAAA;AAAA,IACA;AAGA,UAAA,UAAAC,cAAAA,IAAA,IAAA;AACA,UAAA,aAAAA,cAAAA,IAAA,IAAA;AAEA,UAAA,mBAAAC,cAAA,SAAA,MAAA;AACA,aAAA,WAAA,QAAAC,YAAA,gBAAA,WAAA,MAAA,SAAA,IAAA;AAAA,IACA,CAAA;AAEA,UAAA,mBAAAD,cAAA,SAAA,MAAA;AACA,aAAA,WAAA,QAAAC,YAAA,gBAAA,WAAA,MAAA,SAAA,IAAA;AAAA,IACA,CAAA;AAEA,UAAA,sBAAA,YAAA;AACA,UAAA;AACA,cAAA,MAAA,MAAAC,gDAAAA;AACA,YAAA,IAAA,SAAA,OAAA,IAAA,MAAA;AACA,qBAAA,QAAA,IAAA;AAAA,QACA,OAAA;AACA,qBAAA,QAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAJ,sBAAA,MAAA,MAAA,SAAA,8CAAA,aAAA,KAAA;AACA,mBAAA,QAAA;AAAA,MACA,UAAA;AACA,gBAAA,QAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,gBAAA,MAAA;AACA,UAAA,iBAAA,OAAA;AACAA,sBAAAA,MAAA,aAAA;AAAA,UACA,MAAA,CAAA,iBAAA,KAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,eAAA,CAAA,SAAA;AACAA,oBAAA,MAAA,MAAA,SAAA,8CAAA,GAAA,IAAA,wBAAA;AACA,UAAA,SAAA,UAAA;AACA,mBAAA,MAAA,YAAA;AAAA,MACA,WAAA,SAAA,UAAA;AACA,mBAAA,MAAA,YAAA;AAAA,MACA;AAAA,IACA;AAEAK,kBAAAA,OAAA,MAAA;AACAL,oBAAAA,MAAA,MAAA,OAAA,8CAAA,uBAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAEA;AACA,cAAA,QAAA;AACA;IACA,CAAA;AAEAM,kBAAAA,kBAAA,MAAA;AACA,aAAA;IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7IA,GAAG,WAAW,eAAe;"}