"use strict";const e=require("../../common/vendor.js"),a=require("../../api/content/article.js"),l=require("../../api/content/tag.js"),o=require("../../utils/image.js"),t=require("../../api/content/region.js"),n=require("../../utils/tools.js");if(!Array){(e.resolveComponent("uni-easyinput")+e.resolveComponent("u-tabs")+e.resolveComponent("u-loading-icon")+e.resolveComponent("u-icon")+e.resolveComponent("u-image")+e.resolveComponent("u-empty")+e.resolveComponent("u-loadmore"))()}Math||((()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../uni_modules/uview-plus/components/u-tabs/u-tabs.js")+(()=>"../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-image/u-image.js")+(()=>"../../uni_modules/uview-plus/components/u-empty/u-empty.js")+u+(()=>"../../uni_modules/uview-plus/components/u-loadmore/u-loadmore.js")+r)();const r=()=>"../../components/layout/CustomTabBar.js",u=()=>"../../components/common/NoMoreDivider.js",i={__name:"index",setup(r){const u=e.ref([]),i=e.ref([{id:null,name:"全部"}]),s=e.ref(0),c=e.ref("loadmore"),m=e.ref(""),d=e.ref(null),v=e.ref({}),p=e=>{if(!e||"number"!=typeof e.id)return"tag-color-1";return`tag-color-${e.id%4}`},g=e.reactive({region:null,pageNum:1,pageSize:10,title:null,tagIds:null,orderByColumn:"sort_order",isAsc:"asc",status:"1","params[beginPublishTime]":null,"params[endPublishTime]":null}),f=e.reactive({sort:"default",region:"all",time:"all"}),h=e.reactive({sort:"default",region:"all",time:"all"}),y=[{name:"综合排序",value:"default"},{name:"最新发布",value:"publish_time"},{name:"最多人看",value:"view_count"}],w=e.ref([]);e.computed((()=>v.value.icon_search_input||""));const _=[{name:"全部",value:"all"},{name:"1周内",value:"week"},{name:"1月内",value:"month"},{name:"1年内",value:"year"}],b=e.computed((()=>{const e=v.value.bg_article_header;return e?{backgroundImage:`url('${e}')`}:{}})),A=e.computed((()=>{const e=y.find((e=>e.value===h.sort));return e?e.name:"综合排序"})),T=e.computed((()=>"all"!==h.time)),x=e.computed((()=>{const e=_.find((e=>e.value===h.time));return e?e.name:"发布时间"})),S=e=>{d.value===e?d.value=null:(f.sort=h.sort,f.region=h.region,f.time=h.time,d.value=e)},j=()=>{d.value=null},C=()=>{h.sort=f.sort;const e=h.sort;g.orderByColumn="default"===e?"sort_order":e,g.isAsc="default"===e?"asc":"desc",j(),N(!0)},M=()=>{f.sort="default",h.sort="default",g.orderByColumn="sort_order",g.isAsc="asc",j(),N(!0)},D=()=>{h.region="all",h.time="all",f.region="all",f.time="all",B(),j(),N(!0)},z=()=>{h.region=f.region,h.time=f.time,B(),j(),N(!0)},B=()=>{g.region="all"===h.region?null:h.region;const e=new Date;let a=null;if("all"!==h.time){const l=new Date;"week"===h.time?l.setDate(e.getDate()-7):"month"===h.time?l.setMonth(e.getMonth()-1):"year"===h.time&&l.setFullYear(e.getFullYear()-1),a=`${l.getFullYear()}-${String(l.getMonth()+1).padStart(2,"0")}-${String(l.getDate()).padStart(2,"0")}`}g["params[beginPublishTime]"]=a},N=async(l=!1)=>{if(l||"nomore"!==c.value){l&&(g.pageNum=1,u.value=[]),c.value="loading";try{const o=await a.getArticleList(g),t=o.rows.map((e=>{let a=[];if(e.tags&&"string"==typeof e.tags)try{a=JSON.parse(e.tags)}catch(l){console.error("解析文章标签JSON失败:",e.id,e.tags,l)}return{...e,parsedTags:Array.isArray(a)?a:[]}}));l?u.value=t:u.value.push(...t),o.rows.length<g.pageSize||u.value.length>=o.total?c.value="nomore":c.value="loadmore"}catch(o){c.value="loadmore",console.error("加载文章列表失败:",o),e.index.showToast({title:"加载失败",icon:"none"})}finally{e.index.stopPullDownRefresh()}}},k=async()=>{try{const e=await l.listAllTag(),a=Array.isArray(e.data)?e.data:[];i.value=[{id:null,name:"最新"},...a]}catch(e){console.error("加载标签列表失败:",e)}},q=async()=>{try{const e=await t.listAllRegion(),a=Array.isArray(e.data)?e.data:[],l=a.filter((e=>"全球"!==e.name)).map((e=>({name:e.name,value:e.code})));w.value=[{name:"全部地区",value:"all"},...l]}catch(e){console.error("加载地区列表失败:",e),w.value=[{name:"全部地区",value:"all"}]}},F=e=>{s.value=e.index,g.tagIds=null===e.id?null:String(e.id),N(!0)},I=n.debounce((()=>{const e=m.value.trim();g.title=e||null,N(!0)}),500),P=e=>{m.value=e,e&&""!==e.trim()?I():L()},$=()=>{const e=m.value.trim();g.title=e||null,N(!0)},L=()=>{m.value="",g.title=null,N(!0),setTimeout((()=>e.index.hideKeyboard()),300)},R=()=>{},H=()=>{},J=()=>{"loadmore"===c.value&&(g.pageNum++,N())};return e.onLoad((()=>{console.log("文章列表页 onLoad - 页面首次加载"),e.index.showShareMenu({menus:["shareAppMessage","shareTimeline"]})})),e.onMounted((()=>{v.value=e.index.getStorageSync("staticAssets")||{},(async()=>{try{await Promise.all([k(),q(),N(!0)])}catch(a){console.error("页面初始化失败:",a),e.index.showToast({title:"页面加载失败",icon:"none"})}})()})),e.onShow((()=>{e.index.hideTabBar()})),e.onPullDownRefresh((()=>{N(!0)})),e.onReachBottom((()=>{J()})),e.onShareAppMessage((()=>({}))),(a,l)=>e.e({a:e.t(A.value),b:"sort"===d.value?1:"",c:"sort"===d.value?1:"",d:e.o((e=>S("sort"))),e:e.t(x.value),f:"filter"===d.value?1:"",g:T.value},(T.value,{}),{h:"filter"===d.value||T.value?1:"",i:e.o((e=>S("filter"))),j:e.o($),k:e.o(L),l:e.o(P),m:e.o($),n:e.o((e=>m.value=e)),o:e.p({"suffix-icon":"search",placeholder:"搜索资讯",clearable:!0,modelValue:m.value}),p:e.s(b.value),q:i.value.length>1},i.value.length>1?{r:e.o(F),s:e.p({list:i.value,current:s.value,keyName:"name",lineColor:"#023F98",lineHeight:3,lineWidth:20,"show-scrollbar":!1,activeStyle:{color:"#023F98",fontSize:"32rpx"},inactiveStyle:{color:"#9B9A9A",fontSize:"32rpx"}})}:{},{t:e.f(u.value,((a,l,t)=>{return e.e({a:"ff362ccc-3-"+t+",ff362ccc-2-"+t,b:"ff362ccc-4-"+t+",ff362ccc-2-"+t,c:e.o(H,a.id),d:e.o(R,a.id),e:"ff362ccc-2-"+t,f:e.p({src:e.unref(o.getFullImageUrl)(a.coverImageUrl),width:"100%",height:"190rpx",radius:"16",fade:!0,"lazy-load":!0}),g:e.t(a.title),h:a.parsedTags&&a.parsedTags.length>0},a.parsedTags&&a.parsedTags.length>0?{i:e.f(a.parsedTags,((a,l,o)=>({a:e.t(a.name),b:a.id,c:e.n(p(a))})))}:{},{j:e.t(a.source),k:e.t((n=a.publishTime,n?n.split(" ")[0]:"")),l:a.id,m:e.o((l=>{return o=a.id,void e.index.navigateTo({url:`/pages_sub/pages_article/detail?id=${o}`});var o}),a.id)});var n})),v:e.p({color:"#667eea",size:"20"}),w:e.p({name:"photo-off",color:"#9ca3af",size:"24"}),x:"nomore"===c.value&&0===u.value.length},"nomore"===c.value&&0===u.value.length?{y:e.p({mode:"news",text:"暂无资讯",marginTop:"100"})}:"nomore"===c.value&&u.value.length>0?{}:{A:e.p({status:c.value,line:"true"})},{z:"nomore"===c.value&&u.value.length>0,B:e.o(J),C:d.value},d.value?e.e({D:e.o(j),E:"sort"===d.value},"sort"===d.value?{F:e.f(y,((a,l,o)=>({a:e.t(a.name),b:a.value,c:f.sort===a.value?1:"",d:e.o((e=>f.sort=a.value),a.value)}))),G:e.o(M),H:e.o(C)}:{},{I:"filter"===d.value},"filter"===d.value?{J:e.f(_,((a,l,o)=>({a:e.t(a.name),b:a.value,c:f.time===a.value?1:"",d:e.o((e=>f.time=a.value),a.value)}))),K:e.o(D),L:e.o(z)}:{},{M:d.value?1:""}):{},{N:e.p({current:1})})}},s=e._export_sfc(i,[["__scopeId","data-v-ff362ccc"]]);i.__runtimeHooks=2,wx.createPage(s);
