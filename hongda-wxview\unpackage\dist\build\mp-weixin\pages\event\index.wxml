<view class="event-list-page data-v-a2b67ae8"><view class="header-wrapper data-v-a2b67ae8"><image class="header-bg data-v-a2b67ae8" src="{{a}}" mode="aspectFill"></image><view class="custom-navbar data-v-a2b67ae8"><view class="navbar-title data-v-a2b67ae8"><text class="title-text data-v-a2b67ae8">热门活动列表</text></view></view><view class="top-controls data-v-a2b67ae8"><up-subsection wx:if="{{c}}" class="data-v-a2b67ae8" bindchange="{{b}}" u-i="a2b67ae8-0" bind:__l="__l" u-p="{{c}}"></up-subsection><view class="search-wrapper data-v-a2b67ae8"><custom-search-box wx:if="{{g}}" class="data-v-a2b67ae8" bindsearch="{{d}}" bindinput="{{e}}" u-i="a2b67ae8-1" bind:__l="__l" bindupdateModelValue="{{f}}" u-p="{{g}}"></custom-search-box></view></view></view><event-filter wx:if="{{h}}" class="data-v-a2b67ae8" bindapplyFilters="{{i}}" u-i="a2b67ae8-2" bind:__l="__l" u-p="{{j}}"/><scroll-view wx:if="{{k}}" scroll-y class="event-list-scroll list-scroll-with-filter data-v-a2b67ae8" bindscrolltolower="{{t}}" refresher-enabled refresher-triggered="{{v}}" bindrefresherrefresh="{{w}}"><view wx:if="{{l}}" class="empty-state data-v-a2b67ae8"><view wx:if="{{m}}" class="retry-container data-v-a2b67ae8"><up-button wx:if="{{o}}" class="data-v-a2b67ae8" u-s="{{['d']}}" bindclick="{{n}}" u-i="a2b67ae8-3" bind:__l="__l" u-p="{{o}}"> 重新加载 </up-button></view></view><event-card wx:for="{{p}}" wx:for-item="event" wx:key="a" class="data-v-a2b67ae8" bindclick="{{event.b}}" u-i="{{event.c}}" bind:__l="__l" u-p="{{event.d}}"/><view class="loadmore-wrapper data-v-a2b67ae8"><up-loadmore wx:if="{{q}}" class="data-v-a2b67ae8" u-i="a2b67ae8-5" bind:__l="__l" u-p="{{r}}"/><no-more-divider wx:if="{{s}}" class="data-v-a2b67ae8" u-i="a2b67ae8-6" bind:__l="__l"/></view></scroll-view><event-filter wx:if="{{x}}" class="data-v-a2b67ae8" bindapplyFilters="{{y}}" bindupdateNotchLeft="{{z}}" u-i="a2b67ae8-7" bind:__l="__l" u-p="{{A}}"/><scroll-view wx:if="{{B}}" scroll-y class="event-list-scroll calendar-view calendar-scroll-with-filter data-v-a2b67ae8" bindscrolltolower="{{J}}" refresher-enabled refresher-triggered="{{K}}" bindrefresherrefresh="{{L}}"><view wx:if="{{C}}" class="empty-state data-v-a2b67ae8"><no-more-divider wx:if="{{D}}" class="data-v-a2b67ae8" u-i="a2b67ae8-8" bind:__l="__l"/><view wx:if="{{E}}" class="retry-container data-v-a2b67ae8"><up-button wx:if="{{G}}" class="data-v-a2b67ae8" u-s="{{['d']}}" bindclick="{{F}}" u-i="a2b67ae8-9" bind:__l="__l" u-p="{{G}}"> 重新加载 </up-button></view></view><event-calendar-timeline wx:else class="data-v-a2b67ae8" bindclickItem="{{H}}" u-i="a2b67ae8-10" bind:__l="__l" u-p="{{I||''}}"/></scroll-view><custom-tab-bar wx:if="{{M}}" class="data-v-a2b67ae8" u-i="a2b67ae8-11" bind:__l="__l" u-p="{{M}}"/></view>