<template>
  <view class="page-container">

    <view class="header-section" :style="headerStyle">
      <view class="custom-nav-bar">
        <view class="status-bar"></view>
        <view class="nav-title">资讯列表</view>
        <view class="filter-bar">
          <view class="sort-button" :class="{ 'is-active': activePanel === 'sort' }" @click="togglePanel('sort')">
            {{ sortButtonText }}
            <view class="custom-arrow" :class="{ 'rotate-180': activePanel === 'sort' }"></view>
          </view>
          <view class="filter-button" :class="{ 'is-active': activePanel === 'filter' || isFilterActive }"
                @click="togglePanel('filter')">
            {{ filterButtonText }}
            <view class="custom-arrow" :class="{ 'rotate-180': activePanel === 'filter' }"></view>
            <view class="active-dot" v-if="isFilterActive"></view>
          </view>
          <view class="search-box">
            <uni-easyinput
                class="search-input"
                suffix-icon="search"
                v-model="searchKeyword"
                placeholder="搜索资讯"
                :clearable="true"
                @confirm="handleSearch"
                @clear="handleClear"
                @input="onSearchInput"
                @iconClick="handleSearch"  ></uni-easyinput>
          </view>
        </view>
      </view>
    </view>

    <view class="content-section">
      <view class="tabs-container">
        <u-tabs
            v-if="tagList.length > 1"
            :list="tagList"
            :current="currentTabIndex"
            keyName="name"
            @change="handleTabChange"
            lineColor="#023F98"
            :lineHeight="3"
            :lineWidth="20"
            :show-scrollbar="false"
            :activeStyle="{
              color: '#023F98',
              fontSize: '32rpx',
            }"
            :inactiveStyle="{
              color: '#9B9A9A',
              fontSize: '32rpx',
            }"
        ></u-tabs>
      </view>

      <scroll-view scroll-y class="article-list-scroll" @scrolltolower="loadMore" enable-flex>
        <view class="article-list">
          <view class="article-card" v-for="item in articleList" :key="item.id" @click="gotoDetail(item.id)">
            <view class="card-cover">
              <u-image
                  :src="getFullImageUrl(item.coverImageUrl)"
                  width="100%"
                  height="190rpx"
                  radius="16"
                  :fade="true"
                  :lazy-load="true"
                  @error="onImageError"
                  @load="onImageLoad"
              >
                <template v-slot:loading>
                  <view class="image-loading">
                    <u-loading-icon color="#667eea" size="20"></u-loading-icon>
                    <text class="loading-text">加载中...</text>
                  </view>
                </template>
                <template v-slot:error>
                  <view class="image-error">
                    <u-icon name="photo-off" color="#9ca3af" size="24"></u-icon>
                    <text class="error-text">图片加载失败</text>
                  </view>
                </template>
              </u-image>
            </view>
            <view class="card-content">
              <text class="card-title">{{ item.title }}</text>
              <view class="card-tags" v-if="item.parsedTags && item.parsedTags.length > 0">
                <text
                    v-for="tag in item.parsedTags"
                    :key="tag.id"
                    :class="['tag-item', getTagColorClass(tag)]"
                >
                  {{ tag.name }}
                </text>
              </view>
              <view class="card-meta">
                <text class="meta-source">{{ item.source }}</text>
                <text class="meta-date">{{ formatDate(item.publishTime) }}</text>
              </view>
            </view>
          </view>
        </view>

        <u-empty v-if="loadStatus === 'nomore' && articleList.length === 0"
                 mode="news" text="暂无资讯"
                 marginTop="100"></u-empty>
        <NoMoreDivider v-else-if="loadStatus === 'nomore' && articleList.length > 0" />
        <u-loadmore v-else :status="loadStatus" line="true"/>
      </scroll-view>
    </view>

    <view class="dropdown-wrapper" v-if="activePanel">
      <view class="dropdown-mask" @click="closePanel"></view>
      <view class="dropdown-panel" :class="{ show: activePanel }">
        <view v-if="activePanel === 'sort'" class="filter-panel">
          <scroll-view scroll-y class="filter-scroll">
            <view class="panel-section">
              <text class="section-title">排序方式</text>
              <view class="panel-options">
                <view
                    class="option-btn"
                    v-for="sort in sortActions"
                    :key="sort.value"
                    :class="{ 'active': tempFilters.sort === sort.value }"
                    @click="tempFilters.sort = sort.value"
                >{{ sort.name }}
                </view>
              </view>
            </view>
          </scroll-view>
          <view class="panel-footer">
            <button class="footer-btn reset" @click="handleSortReset">重置</button>
            <button class="footer-btn confirm" @click="handleSortConfirm">确定</button>
          </view>
        </view>

        <view v-if="activePanel === 'filter'" class="filter-panel">
          <scroll-view scroll-y class="filter-scroll">
            <view class="panel-section">
              <text class="section-title">发布时间</text>
              <view class="panel-options">
                <view
                    class="option-btn"
                    v-for="time in timeActions"
                    :key="time.value"
                    :class="{ 'active': tempFilters.time === time.value }"
                    @click="tempFilters.time = time.value"
                >{{ time.name }}
                </view>
              </view>
            </view>
          </scroll-view>
          <view class="panel-footer">
            <button class="footer-btn reset" @click="handleFilterReset">重置</button>
            <button class="footer-btn confirm" @click="handleFilterConfirm">确定</button>
          </view>
        </view>
      </view>
    </view>

    <CustomTabBar :current="1"/>
  </view>
</template>

<script setup>
// Script 部分无需任何修改，保持原样即可
import { ref, reactive, onMounted, computed } from 'vue';
import CustomTabBar from '@/components/layout/CustomTabBar.vue';
import NoMoreDivider from '@/components/common/NoMoreDivider.vue';
import { onPullDownRefresh, onReachBottom, onShow, onLoad, onShareAppMessage } from '@dcloudio/uni-app';
import { getArticleList } from '@/api/content/article.js';
import { listAllTag } from '@/api/content/tag.js';
import { getFullImageUrl } from '@/utils/image.js';
import { listAllRegion } from "@/api/content/region";
import { debounce } from '@/utils/tools.js';

// --- 响应式状态 ---
const articleList = ref([]);
const tagList = ref([{ id: null, name: '全部' }]);
const currentTabIndex = ref(0);
const loadStatus = ref('loadmore');
const searchKeyword = ref('');
const activePanel = ref(null);
const assets = ref({});

// 在 <script setup> 的任意位置添加这个方法
/**
 * 根据标签ID计算出一个固定的颜色类名
 * @param {object} tag - 标签对象，必须包含id属性
 * @returns {string} - 返回如 'tag-color-0' 的类名
 */
const getTagColorClass = (tag) => {
  if (!tag || typeof tag.id !== 'number') {
    // 如果没有ID或ID不是数字，返回一个默认颜色
    return 'tag-color-1';
  }
  // 使用标签ID对4取模，得到 0, 1, 2, 3
  const colorIndex = tag.id % 4;
  return `tag-color-${colorIndex}`;
};

// --- 查询参数 ---
const queryParams = reactive({
  region: null,
  pageNum: 1,
  pageSize: 10,
  title: null,
  tagIds: null,
  orderByColumn: 'sort_order',
  isAsc: 'asc',
  status: '1',
  'params[beginPublishTime]': null,
  'params[endPublishTime]': null,
});

// --- 筛选状态管理 ---
const tempFilters = reactive({
  sort: 'default',
  region: 'all',
  time: 'all',
});

const appliedFilters = reactive({
  sort: 'default',
  region: 'all',
  time: 'all',
});

// --- 筛选选项静态配置 ---
const sortActions = [
  { name: '综合排序', value: 'default' },
  { name: '最新发布', value: 'publish_time' },
  { name: '最多人看', value: 'view_count' },
];

const regionActions = ref([]);
const searchIconUrl = computed(() => assets.value.icon_search_input || '');

const timeActions = [
  { name: '全部', value: 'all' }, // ✨ 修改完成
  { name: '1周内', value: 'week' },
  { name: '1月内', value: 'month' },
  { name: '1年内', value: 'year' },
];

// --- Computed Properties for UI ---
const headerStyle = computed(() => {
  const imageUrl = assets.value.bg_article_header;
  if (imageUrl) {
    return {
      backgroundImage: `url('${imageUrl}')`
    };
  }
  return {};
});

const sortButtonText = computed(() => {
  const sortItem = sortActions.find(item => item.value === appliedFilters.sort);
  return sortItem ? sortItem.name : '综合排序';
});

const isFilterActive = computed(() => appliedFilters.time !== 'all');

const filterButtonText = computed(() => {
  const timeItem = timeActions.find(item => item.value === appliedFilters.time);
  return timeItem ? timeItem.name : '发布时间';
});

// --- Methods ---
const togglePanel = (panelName) => {
  if (activePanel.value === panelName) {
    activePanel.value = null;
  } else {
    tempFilters.sort = appliedFilters.sort;
    tempFilters.region = appliedFilters.region;
    tempFilters.time = appliedFilters.time;
    activePanel.value = panelName;
  }
};

const closePanel = () => {
  activePanel.value = null;
};

// ✨ 新增：处理排序面板的“确定”按钮点击
const handleSortConfirm = () => {
  // 1. 将临时选中的排序应用到实际的筛选参数中
  appliedFilters.sort = tempFilters.sort;

  // 2. 根据应用的排序值，更新API查询参数
  const sortValue = appliedFilters.sort;
  queryParams.orderByColumn = sortValue === 'default' ? 'sort_order' : sortValue;
  queryParams.isAsc = sortValue === 'default' ? 'asc' : 'desc';

  // 3. 关闭面板并重新加载列表
  closePanel();
  loadArticles(true);
};

// ✨ 新增：处理排序面板的“重置”按钮点击
const handleSortReset = () => {
  // 1. 将临时和应用的排序都重置为默认值
  tempFilters.sort = 'default';
  appliedFilters.sort = 'default';

  // 2. 更新API查询参数为默认排序
  queryParams.orderByColumn = 'sort_order';
  queryParams.isAsc = 'asc';

  // 3. 关闭面板并重新加载列表
  closePanel();
  loadArticles(true);
};

const handleFilterReset = () => {
  appliedFilters.region = 'all';
  appliedFilters.time = 'all';
  tempFilters.region = 'all';
  tempFilters.time = 'all';
  updateFilterParams();
  closePanel();
  loadArticles(true);
};

const handleFilterConfirm = () => {
  appliedFilters.region = tempFilters.region;
  appliedFilters.time = tempFilters.time;
  updateFilterParams();
  closePanel();
  loadArticles(true);
};

const updateFilterParams = () => {
  queryParams.region = appliedFilters.region === 'all' ? null : appliedFilters.region;
  const now = new Date();
  let beginDate = null;
  if (appliedFilters.time !== 'all') {
    const endDate = new Date();
    if (appliedFilters.time === 'week') endDate.setDate(now.getDate() - 7);
    else if (appliedFilters.time === 'month') endDate.setMonth(now.getMonth() - 1);
    else if (appliedFilters.time === 'year') endDate.setFullYear(now.getFullYear() - 1);
    beginDate = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;
  }
  queryParams['params[beginPublishTime]'] = beginDate;
};

const parseArticles = (rows) => {
  return rows.map((article) => {
    let parsedTags = [];
    if (article.tags && typeof article.tags === 'string') {
      try {
        parsedTags = JSON.parse(article.tags);
      } catch (e) {
        console.error('解析文章标签JSON失败:', article.id, article.tags, e);
      }
    }
    return { ...article, parsedTags: Array.isArray(parsedTags) ? parsedTags : [] };
  });
};

const loadArticles = async (isRefresh = false) => {
  if (!isRefresh && loadStatus.value === 'nomore') return;
  if (isRefresh) {
    queryParams.pageNum = 1;
    articleList.value = [];
  }
  loadStatus.value = 'loading';
  try {
    const response = await getArticleList(queryParams);
    const newArticles = parseArticles(response.rows);
    if (isRefresh) {
      articleList.value = newArticles;
    } else {
      articleList.value.push(...newArticles);
    }
    if (response.rows.length < queryParams.pageSize || articleList.value.length >= response.total) {
      loadStatus.value = 'nomore';
    } else {
      loadStatus.value = 'loadmore';
    }
  } catch (error) {
    loadStatus.value = 'loadmore';
    console.error('加载文章列表失败:', error);
    uni.showToast({ title: '加载失败', icon: 'none' });
  } finally {
    uni.stopPullDownRefresh();
  }
};

const loadTags = async () => {
  try {
    const response = await listAllTag();
    const backendTags = Array.isArray(response.data) ? response.data : [];
    tagList.value = [{ id: null, name: '最新' }, ...backendTags];
  } catch (error) {
    console.error('加载标签列表失败:', error);
  }
};

const loadRegions = async () => {
  try {
    const response = await listAllRegion();
    const backendRegions = Array.isArray(response.data) ? response.data : [];
    const filteredRegions = backendRegions.filter(item => item.name !== '全球');
    const formattedRegions = filteredRegions.map(item => ({
      name: item.name,
      value: item.code
    }));
    regionActions.value = [{ name: '全部地区', value: 'all' }, ...formattedRegions];
  } catch (error) {
    console.error('加载地区列表失败:', error);
    regionActions.value = [{ name: '全部地区', value: 'all' }];
  }
};

const handleTabChange = (tab) => {
  currentTabIndex.value = tab.index;
  queryParams.tagIds = tab.id === null ? null : String(tab.id);
  loadArticles(true);
};

// 防抖搜索函数
const debouncedSearch = debounce(() => {
  const keyword = searchKeyword.value.trim();
  queryParams.title = keyword || null;
  loadArticles(true);
}, 500);

const onSearchInput = (value) => {
  searchKeyword.value = value;
  if (!value || value.trim() === '') {
    handleClear();
  } else {
    // 实时搜索：当用户输入时触发防抖搜索
    debouncedSearch();
  }
};

const handleSearch = () => {
  const keyword = searchKeyword.value.trim();
  queryParams.title = keyword || null;
  loadArticles(true);
};

const handleClear = () => {
  searchKeyword.value = '';
  queryParams.title = null;
  loadArticles(true);
  setTimeout(() => uni.hideKeyboard(), 300);
};

const onImageLoad = () => {};
const onImageError = () => {};

const gotoDetail = (id) => {
  uni.navigateTo({
    url: `/pages_sub/pages_article/detail?id=${id}`,
  });
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  return dateString.split(' ')[0];
};

const loadMore = () => {
  if (loadStatus.value === 'loadmore') {
    queryParams.pageNum++;
    loadArticles();
  }
};

const initPageData = async () => {
  try {
    await Promise.all([
      loadTags(),
      loadRegions(),
      loadArticles(true)
    ]);
  } catch (error) {
    console.error('页面初始化失败:', error);
    uni.showToast({title: '页面加载失败', icon: 'none'});
  }
};

onLoad(() => {
  console.log('文章列表页 onLoad - 页面首次加载');
  uni.showShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    });
});

onMounted(() => {
  assets.value = uni.getStorageSync('staticAssets') || {};
  initPageData();
});

onShow(() => {
  uni.hideTabBar();
});

onPullDownRefresh(() => {
  loadArticles(true);
});

onReachBottom(() => {
  loadMore();
});

onShareAppMessage(() => {
  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为
});
</script>

<style lang="scss" scoped>
/* --- Style 部分已根据您的最新需求重构 --- */
:root {
  --radius-small: 8rpx;
  --radius-medium: 16rpx;
  --radius-large: 24rpx;
  --radius-xl: 32rpx;
  --separator-color: #E5E7EB;
  --content-padding: 32rpx;
  font-style: normal;
  text-transform: none;
}
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #FFFFFF;
}
.header-section {
  position: relative;
  flex-shrink: 0;
  z-index: 10;
  background-color: #FFFFFF;
  background-size: cover;
  background-repeat: no-repeat;
  padding-bottom: 24rpx;
}
.custom-nav-bar {
  padding: 0 var(--content-padding) 32rpx;
  .status-bar {
    height: var(--status-bar-height);
  }
  .nav-title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 184rpx;
    box-sizing: border-box;
    font-size: 32rpx;
    color: #FFFFFF;
  }
}
.filter-bar {
  display: flex;
  align-items: center;
  gap: 60rpx;
}
.sort-button,
.filter-button {
  font-weight: normal;
  font-size: 26rpx;
  color: #FFFFFF;
  line-height: 44rpx;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8rpx;
  position: relative;
  transition: all 0.3s ease;
  &.is-active {
    color: #FFFFFF;
    .custom-arrow {
      border-top-color: #FFFFFF;
    }
  }
}
.filter-button .active-dot {
  position: absolute;
  top: -4rpx;
  right: -16rpx;
  width: 12rpx;
  height: 12rpx;
  background-color: #ef4444;
  border-radius: 50%;
  border: 2rpx solid #0F4CA7;
}
.search-box {
  flex: 1;
  height: 60rpx;
  background: #FFFFFF;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  padding: 0 12rpx;
  :deep(.uni-easyinput__content) {
    background: transparent !important;
    border: none !important;
  }
  :deep(.uni-easyinput__placeholder) {
    color: #9B9A9A !important;
    font-weight: normal;
    font-size: 28rpx;
    font-style: normal;
    text-transform: none;
    line-height: 44rpx;
  }
}
.dropdown-wrapper {
  position: fixed;
  z-index: 999;
  top: 326rpx;
  left: 0;
  right: 0;
  bottom: 0;
  border-top-left-radius: var(--radius-large);
  border-top-right-radius: var(--radius-large);
  overflow: hidden;
}
.dropdown-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s ease;
}
.dropdown-panel {
  position: absolute;
  top: 0;
  width: 100%;
  background-color: #FFFFFF;
  border-radius: var(--radius-large);
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(-20px);
  pointer-events: none;
  transition: transform 0.25s ease, opacity 0.25s ease;
  &.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }
}
.sort-panel {
  padding: 16rpx 0;
  .sort-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 28rpx 32rpx;
    font-size: 28rpx;
    color: #66666E;
    transition: all 0.2s ease;
    &.active {
      color: #023F98;
      font-weight: 500;
      background-color: rgba(42, 97, 241, 0.05);
    }
  }
}
.filter-panel {
  display: flex;
  flex-direction: column;
  max-height: 70vh;
  .filter-scroll {
    flex: 1;
    padding: 32rpx;
  }
  .panel-section {
    margin-bottom: 48rpx;
    &:last-child {
      margin-bottom: 24rpx;
    }
    .section-title {
      font-weight: 500;
      font-size: 30rpx;
      color: #23232A;
      margin-bottom: 28rpx;
      display: block;
    }
    .panel-options {
      display: flex;
      flex-wrap: wrap;
      gap: 24rpx;
    }
    .option-btn {
      width: 200rpx;
      height: 68rpx;
      background: #F2F4FA;
      border-radius: var(--radius-small);
      font-size: 28rpx;
      color: #66666E;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0;
      transition: all 0.2s ease;
      &.active {
        background: rgba(42, 97, 241, 0.1);
        color: #023F98;
      }
    }
  }
  .panel-footer {
    display: flex;
    gap: 24rpx;
    padding: 24rpx 32rpx 32rpx;
    border-top: 1rpx solid #F0F2F5;
    .footer-btn {
      flex: 1;
      height: 80rpx;
      margin: 0;
      font-size: 30rpx;
      border-radius: var(--radius-small);
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      /* --- [核心修改] 开始 --- */
      /* 添加下面这个选择器，即可去掉按钮的默认边框 */
      &::after {
        border: none;
      }
      /* --- [核心修改] 结束 --- */
      &.reset {
        background: rgba(42, 97, 241, 0.1);
        color: #23232A;
      }
      &.confirm {
        background: #023F98;
        color: #FFFFFF;
      }
    }
  }
}
.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: #FFFFFF;
  margin-top: -24rpx;
  border-top-left-radius: var(--radius-large);
  border-top-right-radius: var(--radius-large);
  position: relative;
  z-index: 20;
}

/* --- [核心修改] 开始 --- */
/* 1. 为 tabs 容器添加 4rpx 粗的灰色底部边框 */
.tabs-container {
  flex-shrink: 0;
  width: 100%;
  background: #ffffff;
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
  position: relative;
  border-bottom: 2rpx solid var(--separator-color);
  padding-left: -30rpx;
}

/* --- [新的解决方案] 开始 --- */
/* 这次我们直接修改包裹标签和下划线的导航容器的高度 */
.tabs-container :deep(.u-tabs__wrapper__nav) {
  height: 75rpx !important;
}
/* --- [新的解决方案] 结束 --- */

/* 2. 使用 :deep() 精准定位蓝色下划线，并将其下移与灰色边框重叠 */
.tabs-container :deep(.u-tabs__wrapper__nav__line) {
  /* 关键：将下划线向下移动。
     计算方法：(灰色边框厚度 / 2) - (蓝色下划线厚度 / 2) = (4rpx / 2) - (2rpx / 2) = 1rpx。
     我们把它移动到灰色边框的垂直中心线下方1rpx的位置，也就是设置 bottom 为 -3rpx
   */
  bottom: -3rpx !important;
}
/* --- [核心修改] 结束 --- */


.tabs-container :deep(::-webkit-scrollbar) {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}

/* --- [新增] 修改 u-tabs 标签项的间距 --- */
.tabs-container :deep(.u-tabs__wrapper__nav__item) {
  /* 设置左右内边距，例如各43rpx，总间距就是86rpx */
  padding: 0 43rpx !important;
}

.article-list-scroll {
  flex: 1;
  height: 0;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: calc(160rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(160rpx + env(safe-area-inset-bottom));
}
.article-list {
  background: #FFFFFF;
  padding-top: 40rpx; /* 使用 padding-top 代替 margin-top */
}
.article-card {
  display: flex;
  gap: 30rpx;
  padding: 32rpx var(--content-padding);
  background: #ffffff;
  position: relative;
  &:not(:last-child) {
    border-bottom: 1rpx solid var(--separator-color);
  }
  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
  }
  .card-title {
    width: 346rpx;
    height: 80rpx;
    font-family: "Alibaba PuHuiTi 3.0", sans-serif;
    font-weight: normal;
    font-size: 28rpx;
    color: #23232A;
    text-align: justified;
    line-height: 1.5;
    font-style: normal;
    text-transform: none;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom: auto;
  }
  .card-meta {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-top: 24rpx;
    font-size: 24rpx;
    color: #9B9A9A;
  }
  .card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    margin-top: 16rpx;
  }
  .tag-item {
    /* 基础样式：动态宽度、固定高度、圆角等 */
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16rpx; /* 使用内边距代替固定宽度 */
    border-radius: 4rpx;
    font-size: 22rpx;
    white-space: nowrap;
    border-width: 1rpx;
    border-style: solid;
  }
  .card-cover {
    flex-shrink: 0;
    width: 336rpx;
    height: 190rpx;
    border-radius: var(--radius-medium);
    overflow: hidden;
  }
}

/* ✨ 新增：预设的4种颜色样式 */
.tag-color-0 {
  border-color: #14BA67;
  color: #14BA67;
}
.tag-color-1 {
  border-color: #023F98;
  color: #023F98;
}
.tag-color-2 {
  border-color: #FB8620;
  color: #FB8620;
}
.tag-color-3 {
  border-color: #CFA616;
  color: #CFA616;
}


.image-loading,
.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: var(--radius-medium);
}
.loading-text,
.error-text {
  font-size: 24rpx;
  color: #9ca3af;
  margin-top: 8rpx;
}
:deep(.u-loadmore__content__text) {
  font-size: 24rpx !important;
  color: #9B9A9A !important;
  line-height: 34rpx !important;
}
.custom-arrow {
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #FFFFFF;
  transition: all 0.3s ease;
  border-radius: 0.4rpx;
}
.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}
</style>