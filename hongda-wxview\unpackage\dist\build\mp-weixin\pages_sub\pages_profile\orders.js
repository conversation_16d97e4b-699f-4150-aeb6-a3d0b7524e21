"use strict";const e=require("../../common/vendor.js"),t=require("../../api/data/registration.js"),o=require("../../api/data/event.js"),a=require("../../utils/image.js");if(!Array){(e.resolveComponent("up-navbar")+e.resolveComponent("up-loading-page")+e.resolveComponent("up-button")+e.resolveComponent("up-empty")+e.resolveComponent("up-loadmore"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-navbar/u-navbar.js")+(()=>"../../uni_modules/uview-plus/components/u-loading-page/u-loading-page.js")+(()=>"../../uni_modules/uview-plus/components/u-button/u-button.js")+(()=>"../../uni_modules/uview-plus/components/u-empty/u-empty.js")+(()=>"../../uni_modules/uview-plus/components/u-loadmore/u-loadmore.js")+n)();const n=()=>"../../components/layout/CustomTabBar.js",l={__name:"orders",setup(n){const l=e.ref(!1),r=e.ref(!1),i=e.ref([]),s=e.ref(0),u=e.ref(!1),c=e.ref(null),d=e.ref(""),g=e.ref(""),v=t=>{const o=e.index.getStorageSync("staticAssets");return o&&o[t]?o[t]:""},m=()=>{d.value=v("orders_warning"),g.value=v("order_bg")},p=e.computed((()=>l.value?"loading":"nomore")),f=()=>{try{return e.index.getStorageSync("token")||null}catch(t){return null}},x=e.computed((()=>(s.value,!!f()))),h=async()=>{console.log("=== 检查登录状态并刷新数据 ==="),s.value++;const t=f();if(console.log("当前登录状态:",!!t),!t)return console.log("用户未登录，清空数据并跳转到登录页"),i.value=[],void e.index.showModal({title:"提示",content:"请先登录后查看报名记录",confirmText:"去登录",cancelText:"取消",success:t=>{t.confirm?(e.index.setStorageSync("loginBackPage","/pages/profile/orders"),e.index.navigateTo({url:"/pages/login/index"})):e.index.navigateBack({fail:()=>{e.index.switchTab({url:"/pages/index/index"})}})}});await w()},w=async()=>{if(!l.value){if(!x.value)return console.log("用户未登录，跳过数据加载"),void(i.value=[]);l.value=!0;try{const e=await t.getMyRegistrationsApi();if(200===e.code){const t=e.data||[];console.log("获取报名记录成功:",t.length,"条"),t.length>0?i.value=await y(t):i.value=[]}else console.log("暂无报名记录"),i.value=[]}catch(o){if(console.error("获取报名订单失败:",o),o.message&&o.message.includes("未登录"))return console.log("检测到登录状态异常，清除token并提示重新登录"),e.index.removeStorageSync("token"),s.value++,void e.index.showModal({title:"登录过期",content:"登录状态已过期，请重新登录",confirmText:"重新登录",showCancel:!1,success:t=>{t.confirm&&(e.index.setStorageSync("loginBackPage","/pages/profile/orders"),e.index.navigateTo({url:"/pages/login/index"}))}});M(o,"获取报名记录")}finally{l.value=!1,r.value=!1}}},y=async e=>{const t=e.sort(((e,t)=>{var o,a;return((null==(o=A(t.registrationTime))?void 0:o.getTime())??0)-((null==(a=A(e.registrationTime))?void 0:a.getTime())??0)}));return(await Promise.allSettled(t.map((async e=>{try{const t=await o.getEventDetailApi(e.eventId);if(200===t.code&&t.data){const o=t.data;return{...e,title:o.title||"活动标题",coverImageUrl:a.getFullImageUrl(o.coverImageUrl),location:o.location||"待定",startTime:o.startTime}}return{...e,title:"活动标题",coverImageUrl:"",location:"待定"}}catch(t){return console.warn("获取活动详情失败:",e.eventId,t),{...e,title:"活动标题",coverImageUrl:"",location:"待定"}}})))).filter((e=>"fulfilled"===e.status)).map((e=>e.value))},T=()=>{u.value=!1,c.value=null},S=()=>{c.value&&(b(c.value),T())},b=async o=>{try{e.index.showLoading({title:"取消中...",mask:!0});const a=await t.cancelRegistrationApi({eventId:o.eventId});if(e.index.hideLoading(),200!==a.code)throw new Error(a.msg||"取消报名失败");{const t=i.value.findIndex((e=>e.eventId===o.eventId&&e.userId===o.userId));-1!==t&&(i.value[t].status=1),e.index.showToast({title:"取消报名成功",icon:"success",duration:2e3})}}catch(a){e.index.hideLoading(),console.error("取消报名失败:",a),e.index.showToast({title:a.message||"取消报名失败，请稍后重试",icon:"none",duration:3e3})}},I=()=>{r.value=!0,h()},_=()=>{e.index.navigateTo({url:"/pages_sub/pages_other/login"})},k=e=>{console.log("图片加载成功")},$=e=>{console.error("图片加载失败:",e)},j=()=>{},A=e=>{if(!e)return null;if(e instanceof Date)return isNaN(e.getTime())?null:e;if("number"==typeof e){const t=new Date(e);return isNaN(t.getTime())?null:t}if("string"==typeof e){let t=e.trim();/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(t)&&(t=t.replace(" ","T"));let o=new Date(t);if(isNaN(o.getTime())){const e=t.match(/^(\d{4})-(\d{1,2})-(\d{1,2})(?:[ T](\d{1,2}):(\d{2})(?::(\d{2}))?)?/);if(e){const t=e[1],a=e[2],n=e[3],l=e[4]?` ${e[4]}:${e[5]}:${e[6]||"00"}`:"";o=new Date(`${t}/${a}/${n}${l}`)}}return isNaN(o.getTime())?null:o}return null},C=e=>{if(!e)return"";const t=A(e);return t?`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`:""},P=e=>0===e?"registered":"cancelled",M=(t,o="")=>{console.error(`${o} 错误:`,t);let a="操作失败，请稍后重试";if(t.code)switch(t.code){case 401:return a="登录已过期，请重新登录",void e.index.reLaunch({url:"/pages/login/index"});case 403:a="权限不足";break;case 404:a="请求的资源不存在";break;case 500:a="服务器错误，请稍后重试";break;default:a=t.message||a}e.index.showToast({title:a,icon:"none",duration:3e3})};return e.onLoad((()=>{console.log("报名订单页 onLoad - 页面首次加载"),e.index.showShareMenu({menus:["shareAppMessage","shareTimeline"]}),m(),h()})),e.onShow((async()=>{try{if("/pages/profile/orders"===e.index.getStorageSync("loginBackPage"))return e.index.removeStorageSync("loginBackPage"),s.value++,void(await w())}catch(t){console.warn("检查登录返回标记失败:",t)}h(),m()})),e.onPullDownRefresh((async()=>{await I(),setTimeout((()=>{e.index.stopPullDownRefresh()}),1e3)})),e.onShareAppMessage((()=>({}))),(t,o)=>e.e({a:e.p({title:"报名订单",autoBack:!0,fixed:!0,safeAreaInsetTop:!0,placeholder:!0,bgColor:"#ffffff",leftIcon:"arrow-left",leftIconColor:"#333333",titleStyle:"font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30'; font-weight: normal; font-size: 32rpx; color: #000000; line-height: 44rpx; text-align: center; font-style: normal; text-transform: none;"}),b:l.value&&0===i.value.length},l.value&&0===i.value.length?{c:e.p({loading:!0,"loading-text":"加载中..."})}:l.value||0!==i.value.length?{i:e.f(i.value,((t,o,a)=>{return e.e({a:t.coverImageUrl,b:e.o($,t.id),c:e.o(k,t.id),d:e.t(t.title||"活动标题"),e:e.t(C(t.registrationTime)),f:0===t.status},0===t.status?{g:g.value}:1===t.status?{}:{i:e.t((n=t.status,0===n?"已报名":"已取消")),j:e.n(P(t.status))},{h:1===t.status,k:0===t.status},0===t.status?{l:e.o((e=>(e=>{c.value=e,u.value=!0})(t)),t.id)}:{},{m:t.id,n:e.o((o=>{return a=t.eventId,void e.index.navigateTo({url:`/pages_sub/pages_event/detail?id=${a}`});var a}),t.id)});var n}))}:e.e({e:!x.value},x.value?{}:{f:e.o(_),g:e.p({type:"primary",text:"去登录"})},{h:e.p({mode:"list",text:x.value?"暂无报名记录":"请先登录查看报名记录",textColor:"#909399",iconSize:"120"})}),{d:!l.value&&0===i.value.length,j:i.value.length>0},i.value.length>0?{k:e.p({status:p.value,"loading-text":"正在加载...","loadmore-text":"上拉加载更多","nomore-text":"没有更多了"})}:{},{l:e.o(j),m:r.value,n:e.o(I),o:e.p({current:4}),p:u.value},u.value?{q:d.value,r:e.o(T),s:e.o(S),t:e.o((()=>{})),v:e.o(T)}:{})}},r=e._export_sfc(l,[["__scopeId","data-v-9fa5f6b8"]]);l.__runtimeHooks=2,wx.createPage(r);
