<view wx:if="{{a}}" class="page-container data-v-9537b0b7"><view class="fixed-header data-v-9537b0b7" style="{{'height:' + g}}"><view class="status-bar data-v-9537b0b7" style="{{'height:' + b}}"></view><view class="custom-nav-bar data-v-9537b0b7" style="{{'height:' + e + ';' + ('padding-bottom:' + f)}}"><view class="nav-back-button data-v-9537b0b7" bindtap="{{d}}"><u-icon wx:if="{{c}}" class="data-v-9537b0b7" u-i="9537b0b7-0" bind:__l="__l" u-p="{{c}}"></u-icon></view><view class="nav-title data-v-9537b0b7">资讯详情</view></view></view><scroll-view scroll-y class="scrollable-content data-v-9537b0b7" style="{{'padding-top:' + y}}"><view class="hero-section data-v-9537b0b7"><image class="hero-image data-v-9537b0b7" src="{{h}}" mode="aspectFill"></image></view><view class="main-content data-v-9537b0b7"><view class="article-title data-v-9537b0b7">{{i}}</view><view class="article-meta-new data-v-9537b0b7"><view class="meta-left data-v-9537b0b7"><text class="meta-text data-v-9537b0b7">{{j}}</text><text class="meta-text data-v-9537b0b7">{{k}}</text></view><view class="meta-right data-v-9537b0b7"><text class="meta-text data-v-9537b0b7">{{l}} 次阅读</text></view></view><view wx:if="{{m}}" class="summary-card data-v-9537b0b7" style="{{o}}"><text class="summary-text data-v-9537b0b7">{{n}}</text></view><view class="content-card data-v-9537b0b7"><view class="content-body data-v-9537b0b7"><mp-html wx:if="{{p}}" class="data-v-9537b0b7" u-i="9537b0b7-1" bind:__l="__l" u-p="{{p}}"/></view></view></view><view wx:if="{{q}}" class="tags-section-new data-v-9537b0b7"><text class="tags-label data-v-9537b0b7">分类：</text><text wx:for="{{r}}" wx:for-item="tag" wx:key="c" class="tag-item-new data-v-9537b0b7">{{tag.a}}{{tag.b}}</text></view><view class="comment-container data-v-9537b0b7"><view class="comment-header-section data-v-9537b0b7"><text class="comment-main-title data-v-9537b0b7">留言 ({{s}})</text></view><view class="comment-input-card data-v-9537b0b7" bindtap="{{v}}"><image class="comment-input-icon data-v-9537b0b7" src="{{t}}"></image><text class="comment-input-placeholder data-v-9537b0b7">写留言</text></view><view wx:if="{{w}}" class="comment-list-container data-v-9537b0b7"><comment-item wx:for="{{x}}" wx:for-item="comment" wx:key="a" class="data-v-9537b0b7" u-i="{{comment.b}}" bind:__l="__l" u-p="{{comment.c}}"/></view><view wx:else class="empty-state data-v-9537b0b7"><text class="empty-title data-v-9537b0b7">暂无评论</text></view></view></scroll-view><u-popup wx:if="{{I}}" class="data-v-9537b0b7" u-s="{{['d']}}" bindclose="{{H}}" u-i="9537b0b7-3" bind:__l="__l" u-p="{{I}}"><view class="comment-popup data-v-9537b0b7"><view class="popup-header data-v-9537b0b7"><text class="popup-title data-v-9537b0b7">发表您的看法</text><text class="popup-close data-v-9537b0b7" bindtap="{{z}}">×</text></view><view class="popup-body data-v-9537b0b7"><view class="textarea-wrapper data-v-9537b0b7"><block wx:if="{{r0}}"><textarea class="comment-input data-v-9537b0b7" placeholder="分享你的想法..." auto-height="{{true}}" maxlength="300" value="{{A}}" bindinput="{{B}}"></textarea></block><text class="comment-counter data-v-9537b0b7">{{C}}/300</text></view><view class="popup-footer data-v-9537b0b7"><button class="{{['comment-submit', 'data-v-9537b0b7', E && 'comment-submit-active']}}" disabled="{{F}}" bindtap="{{G}}">{{D}}</button></view></view></view></u-popup></view>