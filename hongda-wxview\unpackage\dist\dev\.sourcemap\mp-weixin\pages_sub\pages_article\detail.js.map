{"version": 3, "file": "detail.js", "sources": ["pages_sub/pages_article/detail.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX2FydGljbGVcZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view v-if=\"article\" class=\"page-container\">\r\n    <view class=\"fixed-header\" :style=\"{ height: headerHeight + 'px' }\">\r\n      <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n      <view class=\"custom-nav-bar\"\r\n            :style=\"{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }\">\r\n        <view class=\"nav-back-button\" @click=\"goBack\">\r\n          <u-icon name=\"arrow-left\" color=\"#000000\" size=\"22\"></u-icon>\r\n        </view>\r\n        <view class=\"nav-title\">资讯详情</view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y class=\"scrollable-content\" :style=\"{ paddingTop: headerHeight + 'px' }\">\r\n      <view class=\"hero-section\">\r\n        <image class=\"hero-image\" :src=\"getFullImageUrl(article.coverImageUrl)\" mode=\"aspectFill\"></image>\r\n      </view>\r\n\r\n      <view class=\"main-content\">\r\n        <view class=\"article-title\">{{ article.title }}</view>\r\n\r\n        <view class=\"article-meta-new\">\r\n          <view class=\"meta-left\">\r\n            <text class=\"meta-text\">{{ article.source }}</text>\r\n            <text class=\"meta-text\">{{ formatDate(article.publishTime, 'YYYY-MM-DD') }}</text>\r\n          </view>\r\n          <view class=\"meta-right\">\r\n            <text class=\"meta-text\">{{ article.viewCount }} 次阅读</text>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"summary-card\" v-if=\"article.summary\" :style=\"summaryCardStyle\">\r\n          <text class=\"summary-text\">{{ article.summary }}</text>\r\n        </view>\r\n\r\n        <view class=\"content-card\">\r\n          <view class=\"content-body\">\r\n            <mp-html :content=\"article.content\" :domain=\"imageBaseUrl\" :tag-style=\"tagStyle\" :preview-img=\"true\"\r\n                     lazy-load/>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"tags-section-new\" v-if=\"article.parsedTags && article.parsedTags.length > 0\">\r\n        <text class=\"tags-label\">分类：</text>\r\n        <text v-for=\"(tag, index) in article.parsedTags\" :key=\"tag.id || tag.name\" class=\"tag-item-new\">\r\n          {{ tag.name || tag }}{{ index < article.parsedTags.length - 1 ? ' / ' : '' }}\r\n        </text>\r\n      </view>\r\n\r\n      <view class=\"comment-container\">\r\n        <view class=\"comment-header-section\">\r\n          <text class=\"comment-main-title\">留言 ({{ commentTotal }})</text>\r\n        </view>\r\n        <view class=\"comment-input-card\" @click=\"handleOpenCommentInput\">\r\n          <image\r\n              class=\"comment-input-icon\"\r\n              :src=\"commentInputIconUrl\"\r\n          ></image>\r\n          <text class=\"comment-input-placeholder\">写留言</text>\r\n        </view>\r\n\r\n        <view v-if=\"commentList.length > 0\" class=\"comment-list-container\">\r\n          <CommentItem\r\n              v-for=\"comment in commentList\"\r\n              :key=\"comment.id\"\r\n              :comment=\"comment\"\r\n          />\r\n        </view>\r\n        <view v-else class=\"empty-state\">\r\n          <text class=\"empty-title\">暂无评论</text>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n\r\n    <u-popup :show=\"showNewCommentModal\" @close=\"closeNewCommentModal\" mode=\"bottom\" round=\"20\" :safe-area-inset-bottom=\"true\">\r\n      <view class=\"comment-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">发表您的看法</text>\r\n          <text class=\"popup-close\" @click=\"closeNewCommentModal\">×</text>\r\n        </view>\r\n        <view class=\"popup-body\">\r\n          <view class=\"textarea-wrapper\">\r\n            <textarea\r\n                class=\"comment-input\"\r\n                v-model=\"newComment.content\"\r\n                placeholder=\"分享你的想法...\"\r\n                :auto-height=\"true\"\r\n                maxlength=\"300\"\r\n            ></textarea>\r\n            <text class=\"comment-counter\">{{ newComment.content.length }}/300</text>\r\n          </view>\r\n\r\n          <view class=\"popup-footer\">\r\n            <button\r\n                class=\"comment-submit\"\r\n                :class=\"{'comment-submit-active': newComment.content.trim()}\"\r\n                :disabled=\"!newComment.content.trim() || newComment.isSubmitting\"\r\n                @click=\"postNewComment\"\r\n            >\r\n              {{ newComment.isSubmitting ? '发布中...' : '发布' }}\r\n            </button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { computed, nextTick, reactive, ref } from 'vue';\r\nimport {onLoad, onBackPress, onShareAppMessage} from '@dcloudio/uni-app'\r\nimport mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue';\r\nimport { getArticleDetail } from '@/api/content/article.js';\r\nimport { addComment, getCommentList } from '@/pages_sub/pages_article/api/content/comment.js';\r\nimport { tagStyle } from '@/pages_sub/pages_article/api/common/mpHtmlStyles.js';\r\nimport config from '@/utils/config.js';\r\nimport { getFullImageUrl } from '@/utils/image.js';\r\nimport { formatDate } from '@/utils/date.js';\r\nimport CommentItem from '@/components/common/CommentItem.vue';\r\nimport { formatRichText } from '@/utils/tools.js';\r\n\r\nconst navBarPaddingBottomRpx = 20;\r\nconst navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);\r\nconst statusBarHeight = ref(0);\r\nconst navBarHeight = ref(0);\r\nconst headerHeight = ref(0);\r\nconst showNewCommentModal = ref(false);\r\nconst loadError = ref(false);\r\nconst assets = ref(uni.getStorageSync('staticAssets') || {});\r\n\r\nconst commentInputIconUrl = computed(() => {\r\n  return assets.value.icon_article_comment_input || '';\r\n});\r\n\r\nconst summaryCardStyle = computed(() => {\r\n  const imageUrl = assets.value.bg_article_summary_card;\r\n  if (imageUrl) {\r\n    return {\r\n      backgroundImage: `url('${imageUrl}')`\r\n    };\r\n  }\r\n  return {};\r\n});\r\n\r\nconst getNavBarInfo = () => {\r\n  try {\r\n    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n    statusBarHeight.value = menuButtonInfo.top;\r\n    navBarHeight.value = menuButtonInfo.height;\r\n    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;\r\n  } catch (e) {\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    statusBarHeight.value = systemInfo.statusBarHeight || 20;\r\n    navBarHeight.value = 44;\r\n    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;\r\n  }\r\n};\r\n\r\nconst getCurrentPageUrl = () => {\r\n  try {\r\n    const pages = getCurrentPages();\r\n    const current = pages[pages.length - 1];\r\n    const route = '/' + current.route;\r\n    const options = current.options || {};\r\n    const query = Object.keys(options)\r\n        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options[key])}`)\r\n        .join('&');\r\n    return query ? `${route}?${query}` : route;\r\n  } catch (e) {\r\n    return '/pages_sub/pages_article/detail';\r\n  }\r\n};\r\n\r\nconst ensureLoggedInForAction = () => {\r\n  try {\r\n    const token = uni.getStorageSync('token');\r\n    if (!token) {\r\n      const backUrl = getCurrentPageUrl();\r\n      try { uni.setStorageSync('loginBackPage', backUrl); } catch (e) {}\r\n      uni.navigateTo({ url: '/pages_sub/pages_other/login' });\r\n      return false;\r\n    }\r\n    return true;\r\n  } catch (e) {\r\n    uni.navigateTo({ url: '/pages_sub/pages_other/login' });\r\n    return false;\r\n  }\r\n};\r\n\r\nconst handleOpenCommentInput = () => {\r\n  if (!ensureLoggedInForAction()) return;\r\n  newComment.content = '';\r\n  showNewCommentModal.value = true;\r\n};\r\n\r\nconst closeNewCommentModal = () => {\r\n  showNewCommentModal.value = false;\r\n};\r\n\r\nconst goBack = () => {\r\n  const pages = getCurrentPages();\r\n  if (pages.length > 1) {\r\n    uni.navigateBack({\r\n      delta: 1\r\n    });\r\n  } else {\r\n    uni.switchTab({\r\n      url: '/pages/article/index'\r\n    });\r\n  }\r\n};\r\n\r\nonBackPress(() => {\r\n  const pages = getCurrentPages();\r\n  if (pages.length > 1) {\r\n    return false;\r\n  } else {\r\n    uni.switchTab({\r\n      url: '/pages/article/index'\r\n    });\r\n    return true;\r\n  }\r\n});\r\n\r\nconst article = ref(null);\r\nconst articleId = ref(null);\r\nconst commentList = ref([]);\r\nconst commentTotal = ref(0);\r\nconst newComment = reactive({ content: '', isSubmitting: false });\r\n\r\nconst isCommentsLoading = ref(false);\r\n\r\nconst imageBaseUrl = computed(() => {\r\n  if (!config.imageBaseUrl) return '';\r\n  return config.imageBaseUrl.replace(/\\/$/, '');\r\n});\r\n\r\nconst fetchArticleData = async (id) => {\r\n  loadError.value = false;\r\n  try {\r\n    const response = await getArticleDetail(id);\r\n    if (response.code === 200 && response.data) {\r\n      const rawArticle = response.data;\r\n      rawArticle.content = formatRichText(rawArticle.content);\r\n      let parsedTags = [];\r\n      if (rawArticle.tags && typeof rawArticle.tags === 'string') {\r\n        try {\r\n          parsedTags = JSON.parse(rawArticle.tags);\r\n        } catch (e) {\r\n          console.error('标签解析失败:', e);\r\n        }\r\n      } else if (Array.isArray(rawArticle.tags)) {\r\n        parsedTags = rawArticle.tags;\r\n      }\r\n      if (!Array.isArray(parsedTags)) parsedTags = [];\r\n      article.value = { ...rawArticle, parsedTags };\r\n    } else {\r\n      uni.showToast({ title: response.msg || '获取文章失败', icon: 'none' });\r\n      loadError.value = true;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取文章失败:', error);\r\n    uni.showToast({ title: '网络请求失败', icon: 'error' });\r\n    loadError.value = true;\r\n  }\r\n};\r\n\r\nconst fetchComments = async () => {\r\n  if (!articleId.value) return;\r\n  isCommentsLoading.value = true;\r\n  try {\r\n    const response = await getCommentList({ relatedId: articleId.value, relatedType: 'article' });\r\n    if (response.code === 200 && response.data) {\r\n      commentList.value = response.data.comments || [];\r\n      commentTotal.value = response.data.total || 0;\r\n    } else {\r\n      commentList.value = [];\r\n      commentTotal.value = 0;\r\n    }\r\n  } catch (error) {\r\n    commentList.value = [];\r\n    commentTotal.value = 0;\r\n  } finally {\r\n    isCommentsLoading.value = false;\r\n  }\r\n};\r\n\r\nconst handlePostComment = async ({ content, stateObject }) => {\r\n  if (!content.trim() || stateObject.isSubmitting) return;\r\n  stateObject.isSubmitting = true;\r\n  try {\r\n    const response = await addComment({\r\n      relatedId: articleId.value,\r\n      relatedType: 'article',\r\n      content: content.trim()\r\n    });\r\n    if (response.code === 200) {\r\n      uni.showToast({ title: '发布成功', icon: 'success' });\r\n      await fetchComments();\r\n      return true;\r\n    } else {\r\n      uni.showToast({ title: response.msg || '评论失败', icon: 'error' });\r\n      return false;\r\n    }\r\n  } catch (error) {\r\n    return false;\r\n  } finally {\r\n    stateObject.isSubmitting = false;\r\n  }\r\n};\r\n\r\nconst postNewComment = async () => {\r\n  if (!ensureLoggedInForAction()) return;\r\n  const success = await handlePostComment({ content: newComment.content, stateObject: newComment });\r\n  if (success) {\r\n    newComment.content = '';\r\n    closeNewCommentModal();\r\n  }\r\n};\r\n\r\nconst initPageData = async () => {\r\n  if (!articleId.value) {\r\n    loadError.value = true;\r\n    return;\r\n  }\r\n  await fetchArticleData(articleId.value);\r\n  if (article.value) {\r\n    await nextTick();\r\n    getNavBarInfo();\r\n    await fetchComments();\r\n  }\r\n};\r\n\r\nconst retryLoad = () => {\r\n  initPageData();\r\n};\r\n\r\nonLoad((options) => {\r\n  console.log('文章详情页 onLoad - 页面首次加载');\r\n  uni.showShareMenu({\r\n      menus: ['shareAppMessage', 'shareTimeline']\r\n    });\r\n\r\n  articleId.value = options.id;\r\n  initPageData();\r\n});\r\n\r\nonShareAppMessage(() => {\r\n  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 样式无需修改 */\r\n.page-container {\r\n  height: 100vh;\r\n  background-color: #FFFFFF;\r\n  font-style: normal;\r\n  text-transform: none;\r\n}\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n  background-color: #FFFFFF;\r\n}\r\n.scrollable-content {\r\n  height: 100%;\r\n  box-sizing: border-box;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n.error-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100vh;\r\n  background-color: #FFFFFF;\r\n  .empty-icon {\r\n    font-size: 80rpx;\r\n    margin-bottom: 24rpx;\r\n    opacity: 0.5;\r\n  }\r\n  .empty-title {\r\n    font-size: 30rpx;\r\n    font-weight: 500;\r\n    color: #606266;\r\n    margin-bottom: 12rpx;\r\n  }\r\n  .empty-desc {\r\n    font-size: 26rpx;\r\n    color: #909399;\r\n  }\r\n  .retry-btn {\r\n    margin-top: 40rpx;\r\n    padding: 0 60rpx;\r\n    height: 70rpx;\r\n    line-height: 70rpx;\r\n    font-size: 28rpx;\r\n    color: #ffffff;\r\n    background-color: #3c9cff;\r\n    border-radius: 35rpx;\r\n  }\r\n}\r\n.status-bar {\r\n  width: 100%;\r\n}\r\n.custom-nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  box-sizing: content-box;\r\n}\r\n.nav-back-button {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15rpx;\r\n}\r\n.nav-title {\r\n  font-size: 32rpx;\r\n  font-family: 'Alibaba PuHuiTi Medium', 'Alibaba PuHuiTi', sans-serif;\r\n  color: #000000;\r\n  line-height: 44rpx;\r\n}\r\n.hero-section {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 500rpx;\r\n  .hero-image {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n.main-content {\r\n  padding: 0;\r\n  margin-top: 0;\r\n  position: relative;\r\n  z-index: 3;\r\n}\r\n.article-title {\r\n  font-size: 40rpx;\r\n  color: #23232A;\r\n  font-weight: bold;\r\n  line-height: 1.5;\r\n  padding: 30rpx 30rpx 0 30rpx;\r\n  margin-bottom: 24rpx;\r\n}\r\n.article-meta-new {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 30rpx;\r\n  margin-bottom: 30rpx;\r\n  .meta-left {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 24rpx;\r\n  }\r\n  .meta-text {\r\n    font-size: 24rpx;\r\n    color: #9B9A9A;\r\n  }\r\n}\r\n.summary-card {\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  margin: 0 30rpx 0 30rpx;\r\n  padding: 30rpx;\r\n  .summary-text {\r\n    font-size: 28rpx;\r\n    font-weight: normal;\r\n    color: #23232A;\r\n    line-height: 48rpx;\r\n  }\r\n}\r\n.content-card {\r\n  background: #FFFFFF;\r\n  border-radius: 24rpx;\r\n  margin: 0 30rpx 0 30rpx;\r\n}\r\n.tags-section-new {\r\n  margin: 0 30rpx 0 30rpx;\r\n  padding: 20rpx 24rpx;\r\n  background: #F2F4FA;\r\n  border-radius: 8rpx 8rpx 8rpx 8rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  .tags-label {\r\n    font-size: 24rpx;\r\n    color: #66666E;\r\n    margin-right: 6rpx;\r\n    line-height: 48rpx;\r\n  }\r\n  .tag-item-new {\r\n    font-size: 24rpx;\r\n    color: #66666E;\r\n  }\r\n}\r\n.comment-container {\r\n  margin-top: 20rpx;\r\n  padding: 0 30rpx 40rpx 30rpx;\r\n  background: #ffffff;\r\n}\r\n.comment-header-section {\r\n  padding: 40rpx 0 0 0;\r\n  .comment-main-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    color: #23232A;\r\n  }\r\n}\r\n.comment-input-card {\r\n  width: 702rpx;\r\n  height: 72rpx;\r\n  background: #F7F7F7;\r\n  border-radius: 8rpx;\r\n  margin: 30rpx 0;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 24rpx;\r\n  box-sizing: border-box;\r\n  .comment-input-icon {\r\n    width: 32rpx;\r\n    height: 32rpx;\r\n    margin-right: 16rpx;\r\n  }\r\n  .comment-input-placeholder {\r\n    font-size: 28rpx;\r\n    color: #9B9A9A;\r\n  }\r\n}\r\n.empty-state {\r\n  padding: 80rpx 0;\r\n  text-align: center;\r\n  .empty-title {\r\n    font-size: 30rpx;\r\n    font-weight: 500;\r\n    color: #606266;\r\n    margin-bottom: 12rpx;\r\n  }\r\n}\r\n.comment-popup {\r\n  background: #ffffff;\r\n  .popup-header {\r\n    position: relative;\r\n    padding: 30rpx;\r\n    border-bottom: 2rpx solid #f1f5f9;\r\n    text-align: center;\r\n    .popup-title {\r\n      font-size: 30rpx;\r\n      font-weight: 500;\r\n      color: #606266;\r\n    }\r\n    .popup-close {\r\n      position: absolute;\r\n      right: 30rpx;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      font-size: 40rpx;\r\n      color: #909399;\r\n    }\r\n  }\r\n\r\n  /* --- 样式修改从这里开始 --- */\r\n  .popup-body {\r\n    padding: 30rpx;\r\n\r\n    .textarea-wrapper {\r\n      position: relative;\r\n      width: 100%;\r\n    }\r\n\r\n    .comment-input {\r\n      width: 100%;\r\n      min-height: 200rpx;\r\n      background: #f8f9fa;\r\n      border: 2rpx solid #e5e7eb;\r\n      border-radius: 16rpx;\r\n      padding: 24rpx;\r\n      padding-bottom: 50rpx;\r\n      font-size: 28rpx;\r\n      line-height: 1.6;\r\n      color: #303133;\r\n      box-sizing: border-box;\r\n    }\r\n\r\n    .popup-footer {\r\n      margin-top: 30rpx;\r\n    }\r\n\r\n    .comment-counter {\r\n      position: absolute;\r\n      bottom: 24rpx;\r\n      right: 24rpx;\r\n      font-size: 24rpx;\r\n      color: #909399;\r\n      background-color: #f8f9fa;\r\n      padding-left: 10rpx;\r\n    }\r\n\r\n    .comment-submit {\r\n      width: 100%;\r\n      height: 80rpx;\r\n      line-height: 80rpx;\r\n      padding: 0;\r\n      margin: 0;\r\n      border-radius: 40rpx;\r\n      background: #dcdfe6;\r\n      color: #ffffff;\r\n      font-size: 30rpx;\r\n      border: none;\r\n      outline: none;\r\n\r\n      &::after {\r\n        border: none;\r\n      }\r\n\r\n      &.comment-submit-active {\r\n        background: #023F98;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_article/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ref", "computed", "onBackPress", "reactive", "config", "getArticleDetail", "formatRichText", "getCommentList", "addComment", "nextTick", "onLoad", "onShareAppMessage"], "mappings": ";;;;;;;;;;;;;;;;;;;AAgHA,MAAA,SAAA,MAAA;AAOA,MAAA,cAAA,MAAA;AAGA,MAAA,yBAAA;;;;AACA,UAAA,wBAAAA,cAAA,MAAA,OAAA,sBAAA;AACA,UAAA,kBAAAC,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,sBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,YAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,SAAAA,cAAA,IAAAD,cAAA,MAAA,eAAA,cAAA,KAAA,CAAA,CAAA;AAEA,UAAA,sBAAAE,cAAA,SAAA,MAAA;AACA,aAAA,OAAA,MAAA,8BAAA;AAAA,IACA,CAAA;AAEA,UAAA,mBAAAA,cAAA,SAAA,MAAA;AACA,YAAA,WAAA,OAAA,MAAA;AACA,UAAA,UAAA;AACA,eAAA;AAAA,UACA,iBAAA,QAAA,QAAA;AAAA,QACA;AAAA,MACA;AACA,aAAA;IACA,CAAA;AAEA,UAAA,gBAAA,MAAA;AACA,UAAA;AACA,cAAA,iBAAAF,oBAAA;AACA,wBAAA,QAAA,eAAA;AACA,qBAAA,QAAA,eAAA;AACA,qBAAA,QAAA,eAAA,SAAA;AAAA,MACA,SAAA,GAAA;AACA,cAAA,aAAAA,oBAAA;AACA,wBAAA,QAAA,WAAA,mBAAA;AACA,qBAAA,QAAA;AACA,qBAAA,QAAA,gBAAA,QAAA,aAAA,QAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,oBAAA,MAAA;AACA,UAAA;AACA,cAAA,QAAA;AACA,cAAA,UAAA,MAAA,MAAA,SAAA,CAAA;AACA,cAAA,QAAA,MAAA,QAAA;AACA,cAAA,UAAA,QAAA,WAAA;AACA,cAAA,QAAA,OAAA,KAAA,OAAA,EACA,IAAA,SAAA,GAAA,mBAAA,GAAA,CAAA,IAAA,mBAAA,QAAA,GAAA,CAAA,CAAA,EAAA,EACA,KAAA,GAAA;AACA,eAAA,QAAA,GAAA,KAAA,IAAA,KAAA,KAAA;AAAA,MACA,SAAA,GAAA;AACA,eAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,0BAAA,MAAA;AACA,UAAA;AACA,cAAA,QAAAA,cAAAA,MAAA,eAAA,OAAA;AACA,YAAA,CAAA,OAAA;AACA,gBAAA,UAAA;AACA,cAAA;AAAAA,0BAAAA,MAAA,eAAA,iBAAA,OAAA;AAAA,UAAA,SAAA,GAAA;AAAA,UAAA;AACAA,wBAAAA,MAAA,WAAA,EAAA,KAAA,+BAAA,CAAA;AACA,iBAAA;AAAA,QACA;AACA,eAAA;AAAA,MACA,SAAA,GAAA;AACAA,sBAAAA,MAAA,WAAA,EAAA,KAAA,+BAAA,CAAA;AACA,eAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,yBAAA,MAAA;AACA,UAAA,CAAA,wBAAA;AAAA;AACA,iBAAA,UAAA;AACA,0BAAA,QAAA;AAAA,IACA;AAEA,UAAA,uBAAA,MAAA;AACA,0BAAA,QAAA;AAAA,IACA;AAEA,UAAA,SAAA,MAAA;AACA,YAAA,QAAA;AACA,UAAA,MAAA,SAAA,GAAA;AACAA,sBAAAA,MAAA,aAAA;AAAA,UACA,OAAA;AAAA,QACA,CAAA;AAAA,MACA,OAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,KAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAEAG,kBAAAA,YAAA,MAAA;AACA,YAAA,QAAA;AACA,UAAA,MAAA,SAAA,GAAA;AACA,eAAA;AAAA,MACA,OAAA;AACAH,sBAAAA,MAAA,UAAA;AAAA,UACA,KAAA;AAAA,QACA,CAAA;AACA,eAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEA,UAAA,UAAAC,cAAAA,IAAA,IAAA;AACA,UAAA,YAAAA,cAAAA,IAAA,IAAA;AACA,UAAA,cAAAA,cAAAA,IAAA,CAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,aAAAG,cAAA,SAAA,EAAA,SAAA,IAAA,cAAA,MAAA,CAAA;AAEA,UAAA,oBAAAH,cAAAA,IAAA,KAAA;AAEA,UAAA,eAAAC,cAAA,SAAA,MAAA;AACA,UAAA,CAAAG,aAAA,OAAA;AAAA,eAAA;AACA,aAAAA,aAAA,OAAA,aAAA,QAAA,OAAA,EAAA;AAAA,IACA,CAAA;AAEA,UAAA,mBAAA,OAAA,OAAA;AACA,gBAAA,QAAA;AACA,UAAA;AACA,cAAA,WAAA,MAAAC,qCAAA,EAAA;AACA,YAAA,SAAA,SAAA,OAAA,SAAA,MAAA;AACA,gBAAA,aAAA,SAAA;AACA,qBAAA,UAAAC,YAAAA,eAAA,WAAA,OAAA;AACA,cAAA,aAAA,CAAA;AACA,cAAA,WAAA,QAAA,OAAA,WAAA,SAAA,UAAA;AACA,gBAAA;AACA,2BAAA,KAAA,MAAA,WAAA,IAAA;AAAA,YACA,SAAA,GAAA;AACAP,4BAAA,MAAA,MAAA,SAAA,6CAAA,WAAA,CAAA;AAAA,YACA;AAAA,UACA,WAAA,MAAA,QAAA,WAAA,IAAA,GAAA;AACA,yBAAA,WAAA;AAAA,UACA;AACA,cAAA,CAAA,MAAA,QAAA,UAAA;AAAA,yBAAA,CAAA;AACA,kBAAA,QAAA,EAAA,GAAA,YAAA,WAAA;AAAA,QACA,OAAA;AACAA,8BAAA,UAAA,EAAA,OAAA,SAAA,OAAA,UAAA,MAAA,OAAA,CAAA;AACA,oBAAA,QAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,6CAAA,WAAA,KAAA;AACAA,sBAAA,MAAA,UAAA,EAAA,OAAA,UAAA,MAAA,QAAA,CAAA;AACA,kBAAA,QAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,gBAAA,YAAA;AACA,UAAA,CAAA,UAAA;AAAA;AACA,wBAAA,QAAA;AACA,UAAA;AACA,cAAA,WAAA,MAAAQ,4CAAA,eAAA,EAAA,WAAA,UAAA,OAAA,aAAA,UAAA,CAAA;AACA,YAAA,SAAA,SAAA,OAAA,SAAA,MAAA;AACA,sBAAA,QAAA,SAAA,KAAA,YAAA,CAAA;AACA,uBAAA,QAAA,SAAA,KAAA,SAAA;AAAA,QACA,OAAA;AACA,sBAAA,QAAA;AACA,uBAAA,QAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACA,oBAAA,QAAA;AACA,qBAAA,QAAA;AAAA,MACA,UAAA;AACA,0BAAA,QAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,oBAAA,OAAA,EAAA,SAAA,kBAAA;AACA,UAAA,CAAA,QAAA,KAAA,KAAA,YAAA;AAAA;AACA,kBAAA,eAAA;AACA,UAAA;AACA,cAAA,WAAA,MAAAC,uDAAA;AAAA,UACA,WAAA,UAAA;AAAA,UACA,aAAA;AAAA,UACA,SAAA,QAAA,KAAA;AAAA,QACA,CAAA;AACA,YAAA,SAAA,SAAA,KAAA;AACAT,wBAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,UAAA,CAAA;AACA,gBAAA,cAAA;AACA,iBAAA;AAAA,QACA,OAAA;AACAA,8BAAA,UAAA,EAAA,OAAA,SAAA,OAAA,QAAA,MAAA,QAAA,CAAA;AACA,iBAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACA,eAAA;AAAA,MACA,UAAA;AACA,oBAAA,eAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,iBAAA,YAAA;AACA,UAAA,CAAA,wBAAA;AAAA;AACA,YAAA,UAAA,MAAA,kBAAA,EAAA,SAAA,WAAA,SAAA,aAAA,WAAA,CAAA;AACA,UAAA,SAAA;AACA,mBAAA,UAAA;AACA;MACA;AAAA,IACA;AAEA,UAAA,eAAA,YAAA;AACA,UAAA,CAAA,UAAA,OAAA;AACA,kBAAA,QAAA;AACA;AAAA,MACA;AACA,YAAA,iBAAA,UAAA,KAAA;AACA,UAAA,QAAA,OAAA;AACA,cAAAU,cAAA,WAAA;AACA;AACA,cAAA,cAAA;AAAA,MACA;AAAA,IACA;AAMAC,kBAAA,OAAA,CAAA,YAAA;AACAX,oBAAAA,MAAA,MAAA,OAAA,6CAAA,uBAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAEA,gBAAA,QAAA,QAAA;AACA;IACA,CAAA;AAEAY,kBAAAA,kBAAA,MAAA;AACA,aAAA;IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7VA,GAAG,WAAW,eAAe;"}