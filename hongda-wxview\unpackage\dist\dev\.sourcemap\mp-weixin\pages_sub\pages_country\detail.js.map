{"version": 3, "file": "detail.js", "sources": ["pages_sub/pages_country/detail.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX2NvdW50cnlcZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view v-if=\"loading\" class=\"loading-container\">\r\n    <uni-load-more status=\"loading\"/>\r\n  </view>\r\n\r\n  <view v-else-if=\"country\" class=\"page-container\">\r\n    <view class=\"fixed-header\" :style=\"{ height: headerHeight + 'px' }\">\r\n      <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n      <view class=\"custom-nav-bar\" :style=\"{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }\">\r\n        <view class=\"nav-back-button\" @click=\"goBack\">\r\n          <uni-icons type=\"left\" color=\"#000000\" size=\"22\"></uni-icons>\r\n        </view>\r\n        <view class=\"nav-title\">{{ country.nameCn || '国别详情' }}</view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y class=\"scrollable-content\" :style=\"{ paddingTop: headerHeight + 'px' }\">\r\n      <view class=\"cover-section\">\r\n        <image class=\"cover-image\" :src=\"country.detailsCoverUrl\" mode=\"aspectFill\"/>\r\n        <view class=\"cover-overlay\">\r\n          <view class=\"country-name-cn\">{{ country.nameCn }}</view>\r\n          <view class=\"country-name-en\">{{ country.nameEn }}</view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"tabs-wrapper\">\r\n        <scroll-view class=\"tabs\" scroll-x=\"true\" :show-scrollbar=\"false\">\r\n          <view\r\n              v-for=\"(tab, index) in tabs\"\r\n              :key=\"tab.id\"\r\n              class=\"tab-item\"\r\n              :class=\"{ active: activeTab === index }\"\r\n              @click=\"onTabClick(index)\"\r\n              :style=\"activeTab === index ? activeTabStyle : {}\"\r\n          >\r\n            <image class=\"tab-icon\" :src=\"activeTab === index ? tab.activeIcon : tab.icon\"></image>\r\n            <text class=\"tab-text\">{{ tab.name }}</text>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n\r\n      <view class=\"content-wrapper\">\r\n        <view v-if=\"!isPolicyTabActive\">\r\n          <view v-show=\"activeTab === 0\" class=\"content-panel\">\r\n            <view class=\"section-card\">\r\n              <view class=\"section-title\">国家简介</view>\r\n              <view class=\"plain-text-content introduction-text\">\r\n                <text selectable user-select>{{ country.introduction }}</text>\r\n              </view>\r\n            </view>\r\n\r\n            <view class=\"section-card\" v-if=\"basicInfoList.length > 0\">\r\n              <view class=\"section-title\">基本信息</view>\r\n              <view class=\"info-grid-container basic-info-card\" :style=\"basicInfoCardStyle\">\r\n                <view class=\"info-pair-row\" v-for=\"(pair, pairIndex) in pairedBasicInfoList\" :key=\"pairIndex\">\r\n                  <view class=\"info-row key-row\">\r\n                    <view class=\"info-column\">{{ pair[0]?.key }}</view>\r\n                    <view class=\"info-column\">{{ pair[1]?.key }}</view>\r\n                  </view>\r\n                  <view class=\"info-row value-row\">\r\n                    <view class=\"info-column\">{{ pair[0]?.value }}</view>\r\n                    <view class=\"info-column\">{{ pair[1]?.value }}</view>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view v-show=\"activeTab === 4\" class=\"content-panel\">\r\n            <view\r\n                class=\"park-card\"\r\n                v-for=\"(park, index) in country.industrialParks\"\r\n                :key=\"park.id\"\r\n                @click=\"goToParkDetail(park.id)\"\r\n            >\r\n              <view class=\"park-number-badge\">{{ index + 1 }}</view>\r\n\r\n              <view class=\"park-card-image-wrapper\">\r\n                <u-image\r\n                    :src=\"park.coverImageUrl\"\r\n                    width=\"100%\"\r\n                    height=\"240rpx\"\r\n                    :fade=\"true\"\r\n                    :lazy-load=\"true\"\r\n                ></u-image>\r\n              </view>\r\n              <view class=\"park-card-content\">\r\n                <view class=\"park-name\">{{ park.name }}</view>\r\n                <view class=\"park-info-item\">\r\n                  <image class=\"park-info-icon\" :src=\"assets.icon_park_location || '/static/icons/位置icon金@2x.png'\" mode=\"aspectFit\"></image>\r\n                  <text>{{ park.location }}</text>\r\n                </view>\r\n                <view class=\"park-info-item\">\r\n                  <image class=\"park-info-icon\" :src=\"assets.icon_park_industries || '/static/icons/企业浅金@2x.png'\" mode=\"aspectFit\"></image>\r\n                  <text>{{ park.industries }}</text>\r\n                </view>\r\n                <view class=\"park-info-item\">\r\n                  <image class=\"park-info-icon\" :src=\"assets.icon_park_features || '/static/icons/亮点浅金@2x.png'\" mode=\"aspectFit\"></image>\r\n                  <text>{{ park.features }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view v-else class=\"policy-page-container\">\r\n          <view class=\"policy-title-header\">\r\n            <h1 class=\"policy-title\">{{ currentPolicyName }}</h1>\r\n          </view>\r\n          <view class=\"policy-main-content\">\r\n            <view class=\"policy-intro-wrapper\">\r\n              <view class=\"plain-text-content policy-text\">\r\n                <text selectable user-select>{{ currentPolicyContent }}</text>\r\n              </view>\r\n            </view>\r\n            <ContentModule\r\n                :key=\"currentPolicyType\"\r\n                :country-id=\"country.id\"\r\n                :policy-type=\"currentPolicyType\"\r\n            />\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n\r\n<script setup>\r\nimport {onLoad, onBackPress, onShareAppMessage} from '@dcloudio/uni-app'\r\nimport {computed, ref} from 'vue';\r\nimport {getCountryDetail} from '@/api/content/country.js';\r\nimport ContentModule from '@/components/home/<USER>';\r\n\r\n// --- 自定义导航栏相关逻辑 ---\r\nconst navBarPaddingBottomRpx = 20;\r\nconst navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);\r\nconst statusBarHeight = ref(0);\r\nconst navBarHeight = ref(0);\r\nconst headerHeight = ref(0);\r\n\r\nconst getNavBarInfo = () => {\r\n  try {\r\n    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n    statusBarHeight.value = menuButtonInfo.top;\r\n    navBarHeight.value = menuButtonInfo.height;\r\n    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;\r\n  } catch(e) {\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    statusBarHeight.value = systemInfo.statusBarHeight || 20;\r\n    navBarHeight.value = 44;\r\n    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;\r\n  }\r\n};\r\n\r\n// [修改] 简化 goBack 函数\r\nconst goBack = () => {\r\n  // 获取当前页面栈\r\n  const pages = getCurrentPages();\r\n\r\n  // 判断页面栈中是否有可以返回的页面\r\n  if (pages.length > 1) {\r\n    // 如果有，则直接返回上一页\r\n    uni.navigateBack({\r\n      delta: 1\r\n    });\r\n  } else {\r\n    // 如果没有（例如分享页直接进入），则跳转到国别列表页作为后备\r\n    uni.switchTab({\r\n      url: '/pages/country/index'\r\n    });\r\n  }\r\n};\r\n\r\nonBackPress(() => {\r\n  // 获取当前页面栈\r\n  const pages = getCurrentPages();\r\n\r\n  // 判断页面栈中是否有可以返回的页面\r\n  if (pages.length > 1) {\r\n    // 如果有，返回 false，让 uni-app 执行默认的、最流畅的返回操作\r\n    return false;\r\n  } else {\r\n    // 如果没有上一页，则手动跳转到国别列表页\r\n    uni.switchTab({\r\n      url: '/pages/country/index'\r\n    });\r\n    // 返回 true，表示我们已经处理了返回事件，阻止 App 退出\r\n    return true;\r\n  }\r\n});\r\n\r\n// --- 页面核心数据和状态 ---\r\nconst country = ref(null);\r\nconst loading = ref(true);\r\nconst activeTab = ref(0);\r\nconst assets = ref({});\r\n\r\nconst basicInfoCardStyle = computed(() => ({\r\n  backgroundImage: assets.value.bg_basic_info_card ? `url('${assets.value.bg_basic_info_card}')` : 'none'\r\n}));\r\n\r\nconst activeTabStyle = computed(() => ({\r\n  backgroundImage: assets.value.bg_tab_active ? `url('${assets.value.bg_tab_active}')` : 'none'\r\n}));\r\n\r\n// 【已修改】这里现在是标签页的静态配置\r\nconst tabsConfig = ref([\r\n  { id: 'basic', name: '基本信息', iconKey: 'icon_tab_basic_normal', activeIconKey: 'icon_tab_basic_active' },\r\n  { id: 'investment', name: '招商政策', iconKey: 'icon_tab_investment_normal', activeIconKey: 'icon_tab_investment_active' },\r\n  { id: 'customs', name: '海关政策', iconKey: 'icon_tab_customs_normal', activeIconKey: 'icon_tab_customs_active' },\r\n  { id: 'tax', name: '税务政策', iconKey: 'icon_tab_tax_normal', activeIconKey: 'icon_tab_tax_active' },\r\n  { id: 'parks', name: '工业园区', iconKey: 'icon_tab_parks_normal', activeIconKey: 'icon_tab_parks_active' },\r\n]);\r\n\r\n// 【新增】一个计算属性，用于动态构建带有正确图标URL的标签页列表\r\nconst tabs = computed(() => {\r\n  return tabsConfig.value.map(tab => ({\r\n    id: tab.id,\r\n    name: tab.name,\r\n    icon: assets.value[tab.iconKey] || tab.fallbackIcon,\r\n    activeIcon: assets.value[tab.activeIconKey] || tab.fallbackActiveIcon\r\n  }));\r\n});\r\n\r\nconst basicInfoList = computed(() => {\r\n  if (country.value && Array.isArray(country.value.basicInfoJson)) {\r\n    return country.value.basicInfoJson;\r\n  }\r\n  return [];\r\n});\r\n\r\nconst pairedBasicInfoList = computed(() => {\r\n  const result = [];\r\n  const list = basicInfoList.value;\r\n  for (let i = 0; i < list.length; i += 2) {\r\n    const pair = [list[i]];\r\n    if (list[i + 1]) pair.push(list[i + 1]);\r\n    result.push(pair);\r\n  }\r\n  return result;\r\n});\r\n\r\nconst onTabClick = (index) => {\r\n  activeTab.value = index;\r\n};\r\n\r\nconst isPolicyTabActive = computed(() => {\r\n  const policyIds = ['investment', 'customs', 'tax'];\r\n  return policyIds.includes(tabs.value[activeTab.value]?.id);\r\n});\r\n\r\nconst currentPolicyType = computed(() => tabs.value[activeTab.value]?.id);\r\nconst currentPolicyName = computed(() => tabs.value[activeTab.value]?.name);\r\n\r\nconst currentPolicyContent = computed(() => {\r\n  if (!country.value) return '';\r\n  switch (currentPolicyType.value) {\r\n    case 'investment': return country.value.investmentPolicy;\r\n    case 'customs': return country.value.customsPolicy;\r\n    case 'tax': return country.value.taxPolicy;\r\n    default: return '';\r\n  }\r\n});\r\n\r\nconst goToParkDetail = (parkId) => {\r\n  uni.navigateTo({\r\n    url: `/pages_sub/pages_other/park_detail?id=${parkId}`\r\n  });\r\n};\r\n\r\nonLoad(async (options) => {\r\n  console.log('国别详情页 onLoad - 页面首次加载');\r\n  uni.showShareMenu({\r\n      menus: ['shareAppMessage', 'shareTimeline']\r\n    });\r\n\r\n  getNavBarInfo();\r\n  assets.value = uni.getStorageSync('staticAssets') || {};\r\n\r\n  if (!options.id) {\r\n    uni.showToast({title: '参数错误', icon: 'none'});\r\n    uni.navigateBack();\r\n    return;\r\n  }\r\n\r\n  if (options.tab) {\r\n    const initialTabIndex = tabs.value.findIndex(t => t.id === options.tab);\r\n    if (initialTabIndex !== -1) activeTab.value = initialTabIndex;\r\n  }\r\n\r\n  try {\r\n    const res = await getCountryDetail(options.id);\r\n    // 【已修改】不再需要为图片URL拼接baseUrl\r\n    // 后端的AOP切面已经处理好了完整的URL\r\n    country.value = res.data;\r\n  } catch (error) {\r\n    console.error('获取详情失败:', error);\r\n    uni.showToast({title: error.message || '加载失败', icon: 'none'});\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n});\r\n\r\nonShareAppMessage(() => {\r\n  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* --- 页面布局及自定义导航栏样式 --- */\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #FFFFFF;\r\n}\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n  background-color: #ffffff;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n.scrollable-content {\r\n  flex: 1;\r\n  height: 0;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n.status-bar {\r\n  width: 100%;\r\n}\r\n.custom-nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  box-sizing: content-box;\r\n}\r\n.nav-back-button {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 30rpx;\r\n}\r\n.nav-title {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #000000;\r\n}\r\n.loading-container {\r\n  padding-top: 200rpx;\r\n}\r\n.cover-section {\r\n  position: relative;\r\n  height: 400rpx;\r\n}\r\n.cover-section .cover-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.cover-section .cover-overlay {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  padding: 30rpx;\r\n  box-sizing: border-box;\r\n  color: #fff;\r\n  /* background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent); */ /* <-- 像这样注释掉即可 */\r\n}\r\n.cover-section .cover-overlay .country-name-cn {\r\n  font-size: 48rpx;\r\n  font-weight: bold;\r\n}\r\n.cover-section .cover-overlay .country-name-en {\r\n  font-size: 32rpx;\r\n  opacity: 0.9;\r\n}\r\n.tabs-wrapper {\r\n  margin: 30rpx 30rpx 0;\r\n  background-color: #F4F4F4;\r\n  border-radius: 16rpx;\r\n  padding: 12rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n}\r\n.tabs {\r\n  display: flex;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* detail.vue - 请用这段代码替换原来的 .tabs::-webkit-scrollbar 规则 */\r\n.tabs :deep(::-webkit-scrollbar) {\r\n  display: none;\r\n  width: 0 !important;\r\n  width: 0 !important;\r\n  height: 0 !important;\r\n  -webkit-appearance: none;\r\n  background: transparent;\r\n  color: transparent;\r\n}\r\n.tab-item {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n  background-color: #fff;\r\n  border-radius: 10rpx;\r\n  padding: 12rpx 10rpx;\r\n  margin-right: 20rpx;\r\n}\r\n.tab-item:last-child {\r\n  margin-right: 0;\r\n}\r\n.tab-item .tab-icon {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n.tab-item .tab-text {\r\n  font-size: 28rpx;\r\n  color: #9B9A9A;\r\n}\r\n.tab-item.active {\r\n  background-size: cover;\r\n  background-position: center;\r\n}\r\n.tab-item.active .tab-text {\r\n  color: #23232A;\r\n}\r\n.content-wrapper {\r\n  padding: 24rpx;\r\n}\r\n.section-card {\r\n  background-color: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 24rpx;\r\n}\r\n.section-title {\r\n  font-size: 32rpx;\r\n  color: #333;\r\n  padding-bottom: 15rpx;\r\n  margin-bottom: 20rpx;\r\n  font-family: \"Alibaba PuHuiTi Medium\", \"Alibaba PuHuiTi\", sans-serif;\r\n}\r\n.plain-text-content {\r\n  font-size: 26rpx;\r\n  line-height: 1.7;\r\n  white-space: pre-wrap;\r\n}\r\n.introduction-text {\r\n  color: #66666E;\r\n}\r\n.policy-text {\r\n  color: #23232A;\r\n}\r\n.basic-info-card {\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  padding: 20rpx;\r\n}\r\n.info-grid-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 24rpx;\r\n}\r\n.info-row {\r\n  display: flex;\r\n  width: 100%;\r\n}\r\n.info-column {\r\n  flex: 1;\r\n  width: 50%;\r\n  padding-right: 20rpx;\r\n  box-sizing: border-box;\r\n}\r\n.key-row .info-column {\r\n  font-size: 24rpx;\r\n  color: #66666E;\r\n}\r\n.value-row {\r\n  margin-top: 8rpx;\r\n}\r\n.value-row .info-column {\r\n  font-size: 28rpx;\r\n  color: #23232A;\r\n  font-weight: 500;\r\n}\r\n.policy-title-header {\r\n  padding: 30rpx;\r\n  border-radius: 16rpx 16rpx 0 0;\r\n  background: linear-gradient(to bottom, #CEDEF5, #F0F2F3) center;\r\n  //border-bottom: 2rpx solid #DCDFE6;\r\n}\r\n.policy-title-header .policy-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #023F98;\r\n  font-family: 'Alibaba PuHuiTi Bold', sans-serif;\r\n}\r\n.policy-main-content {\r\n  background-color: #F2F4FA;\r\n  border-radius: 0 0 16rpx 16rpx;\r\n  padding: 30rpx;\r\n}\r\n.policy-intro-wrapper {\r\n  margin-bottom: 30rpx;\r\n}\r\n.park-card {\r\n  width: 702rpx;\r\n  height: 544rpx;\r\n  background: linear-gradient(180deg, #EDF2FF 0%, #FFFFFF 100%);\r\n  border-radius: 16rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n  transition: transform 0.2s;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n.park-card:active {\r\n  transform: scale(0.98);\r\n}\r\n.park-card .park-card-image-wrapper {\r\n  width: calc(100% - 48rpx);\r\n  height: 240rpx;\r\n  background-color: #f0f2f5;\r\n  margin: 24rpx 24rpx 0 24rpx;\r\n  border-radius: 16rpx 16rpx 16rpx 16rpx;\r\n  overflow: hidden;\r\n}\r\n.park-card .park-card-content {\r\n  padding: 30rpx;\r\n}\r\n.park-card .park-name {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 24rpx;\r\n}\r\n.park-card .park-info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 16rpx;\r\n}\r\n.park-card .park-info-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.park-card .park-info-icon {\r\n  width: 26rpx;\r\n  height: 26rpx;\r\n  margin-right: 12rpx;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* [新增] 园区卡片左上角序号标记的样式 */\r\n.park-number-badge {\r\n  position: absolute;\r\n  top: 36rpx;\r\n  left: 36rpx;\r\n  z-index: 2; /* 确保在图片上方 */\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  background-color: #FFBF51;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #23232A;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2); /* 增加一点阴影使其更突出 */\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_country/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ref", "onBackPress", "computed", "onLoad", "getCountryDetail", "onShareAppMessage"], "mappings": ";;;;;;;;;;;;;;;AAoIA,MAAA,gBAAA,MAAA;AAGA,MAAA,yBAAA;;;;AACA,UAAA,wBAAAA,cAAA,MAAA,OAAA,sBAAA;AACA,UAAA,kBAAAC,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AAEA,UAAA,gBAAA,MAAA;AACA,UAAA;AACA,cAAA,iBAAAD,oBAAA;AACA,wBAAA,QAAA,eAAA;AACA,qBAAA,QAAA,eAAA;AACA,qBAAA,QAAA,eAAA,SAAA;AAAA,MACA,SAAA,GAAA;AACA,cAAA,aAAAA,oBAAA;AACA,wBAAA,QAAA,WAAA,mBAAA;AACA,qBAAA,QAAA;AACA,qBAAA,QAAA,gBAAA,QAAA,aAAA,QAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,SAAA,MAAA;AAEA,YAAA,QAAA;AAGA,UAAA,MAAA,SAAA,GAAA;AAEAA,sBAAAA,MAAA,aAAA;AAAA,UACA,OAAA;AAAA,QACA,CAAA;AAAA,MACA,OAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA,UACA,KAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAEAE,kBAAAA,YAAA,MAAA;AAEA,YAAA,QAAA;AAGA,UAAA,MAAA,SAAA,GAAA;AAEA,eAAA;AAAA,MACA,OAAA;AAEAF,sBAAAA,MAAA,UAAA;AAAA,UACA,KAAA;AAAA,QACA,CAAA;AAEA,eAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,UAAAC,cAAAA,IAAA,IAAA;AACA,UAAA,UAAAA,cAAAA,IAAA,IAAA;AACA,UAAA,YAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,SAAAA,cAAAA,IAAA,CAAA,CAAA;AAEA,UAAA,qBAAAE,cAAA,SAAA,OAAA;AAAA,MACA,iBAAA,OAAA,MAAA,qBAAA,QAAA,OAAA,MAAA,kBAAA,OAAA;AAAA,IACA,EAAA;AAEA,UAAA,iBAAAA,cAAA,SAAA,OAAA;AAAA,MACA,iBAAA,OAAA,MAAA,gBAAA,QAAA,OAAA,MAAA,aAAA,OAAA;AAAA,IACA,EAAA;AAGA,UAAA,aAAAF,cAAAA,IAAA;AAAA,MACA,EAAA,IAAA,SAAA,MAAA,QAAA,SAAA,yBAAA,eAAA,wBAAA;AAAA,MACA,EAAA,IAAA,cAAA,MAAA,QAAA,SAAA,8BAAA,eAAA,6BAAA;AAAA,MACA,EAAA,IAAA,WAAA,MAAA,QAAA,SAAA,2BAAA,eAAA,0BAAA;AAAA,MACA,EAAA,IAAA,OAAA,MAAA,QAAA,SAAA,uBAAA,eAAA,sBAAA;AAAA,MACA,EAAA,IAAA,SAAA,MAAA,QAAA,SAAA,yBAAA,eAAA,wBAAA;AAAA,IACA,CAAA;AAGA,UAAA,OAAAE,cAAA,SAAA,MAAA;AACA,aAAA,WAAA,MAAA,IAAA,UAAA;AAAA,QACA,IAAA,IAAA;AAAA,QACA,MAAA,IAAA;AAAA,QACA,MAAA,OAAA,MAAA,IAAA,OAAA,KAAA,IAAA;AAAA,QACA,YAAA,OAAA,MAAA,IAAA,aAAA,KAAA,IAAA;AAAA,MACA,EAAA;AAAA,IACA,CAAA;AAEA,UAAA,gBAAAA,cAAA,SAAA,MAAA;AACA,UAAA,QAAA,SAAA,MAAA,QAAA,QAAA,MAAA,aAAA,GAAA;AACA,eAAA,QAAA,MAAA;AAAA,MACA;AACA,aAAA;IACA,CAAA;AAEA,UAAA,sBAAAA,cAAA,SAAA,MAAA;AACA,YAAA,SAAA,CAAA;AACA,YAAA,OAAA,cAAA;AACA,eAAA,IAAA,GAAA,IAAA,KAAA,QAAA,KAAA,GAAA;AACA,cAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACA,YAAA,KAAA,IAAA,CAAA;AAAA,eAAA,KAAA,KAAA,IAAA,CAAA,CAAA;AACA,eAAA,KAAA,IAAA;AAAA,MACA;AACA,aAAA;AAAA,IACA,CAAA;AAEA,UAAA,aAAA,CAAA,UAAA;AACA,gBAAA,QAAA;AAAA,IACA;AAEA,UAAA,oBAAAA,cAAA,SAAA,MAAA;;AACA,YAAA,YAAA,CAAA,cAAA,WAAA,KAAA;AACA,aAAA,UAAA,UAAA,UAAA,MAAA,UAAA,KAAA,MAAA,mBAAA,EAAA;AAAA,IACA,CAAA;AAEA,UAAA,oBAAAA,cAAAA,SAAA,MAAA;;AAAA,wBAAA,MAAA,UAAA,KAAA,MAAA,mBAAA;AAAA,KAAA;AACA,UAAA,oBAAAA,cAAAA,SAAA,MAAA;;AAAA,wBAAA,MAAA,UAAA,KAAA,MAAA,mBAAA;AAAA,KAAA;AAEA,UAAA,uBAAAA,cAAA,SAAA,MAAA;AACA,UAAA,CAAA,QAAA;AAAA,eAAA;AACA,cAAA,kBAAA,OAAA;AAAA,QACA,KAAA;AAAA,iBAAA,QAAA,MAAA;AAAA,QACA,KAAA;AAAA,iBAAA,QAAA,MAAA;AAAA,QACA,KAAA;AAAA,iBAAA,QAAA,MAAA;AAAA,QACA;AAAA,iBAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEA,UAAA,iBAAA,CAAA,WAAA;AACAH,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,yCAAA,MAAA;AAAA,MACA,CAAA;AAAA,IACA;AAEAI,kBAAA,OAAA,OAAA,YAAA;AACAJ,oBAAAA,MAAA,MAAA,OAAA,6CAAA,uBAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAEA;AACA,aAAA,QAAAA,cAAA,MAAA,eAAA,cAAA,KAAA,CAAA;AAEA,UAAA,CAAA,QAAA,IAAA;AACAA,sBAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,OAAA,CAAA;AACAA,sBAAA,MAAA,aAAA;AACA;AAAA,MACA;AAEA,UAAA,QAAA,KAAA;AACA,cAAA,kBAAA,KAAA,MAAA,UAAA,OAAA,EAAA,OAAA,QAAA,GAAA;AACA,YAAA,oBAAA;AAAA,oBAAA,QAAA;AAAA,MACA;AAEA,UAAA;AACA,cAAA,MAAA,MAAAK,oBAAAA,iBAAA,QAAA,EAAA;AAGA,gBAAA,QAAA,IAAA;AAAA,MACA,SAAA,OAAA;AACAL,sBAAA,MAAA,MAAA,SAAA,6CAAA,WAAA,KAAA;AACAA,4BAAA,UAAA,EAAA,OAAA,MAAA,WAAA,QAAA,MAAA,OAAA,CAAA;AAAA,MACA,UAAA;AACA,gBAAA,QAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEAM,kBAAAA,kBAAA,MAAA;AACA,aAAA;IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjTA,GAAG,WAAW,eAAe;"}