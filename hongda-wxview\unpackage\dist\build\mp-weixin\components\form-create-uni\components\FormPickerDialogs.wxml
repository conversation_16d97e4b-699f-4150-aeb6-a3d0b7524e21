<view class="form-picker-dialogs data-v-98280c54"><up-picker wx:if="{{c}}" class="data-v-98280c54" bindconfirm="{{a}}" bindcancel="{{b}}" u-i="98280c54-0" bind:__l="__l" u-p="{{c}}"/><up-picker wx:if="{{f}}" class="data-v-98280c54" bindconfirm="{{d}}" bindcancel="{{e}}" u-i="98280c54-1" bind:__l="__l" u-p="{{f}}"/><up-popup wx:if="{{n}}" class="data-v-98280c54" u-s="{{['d']}}" bindclose="{{m}}" u-i="98280c54-2" bind:__l="__l" u-p="{{n}}"><view class="color-picker-popup data-v-98280c54"><view class="color-picker-title data-v-98280c54">选择颜色</view><view class="color-list data-v-98280c54"><view wx:for="{{g}}" wx:for-item="color" wx:key="b" class="color-item data-v-98280c54" style="{{'background-color:' + color.c}}" bindtap="{{color.d}}"><text wx:if="{{color.a}}" class="color-check data-v-98280c54">✓</text></view></view><view class="custom-color-input data-v-98280c54"><text class="input-label data-v-98280c54">自定义颜色:</text><up-input wx:if="{{j}}" class="data-v-98280c54" bindblur="{{h}}" u-i="98280c54-3,98280c54-2" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"/><up-button wx:if="{{l}}" class="data-v-98280c54" u-s="{{['d']}}" bindclick="{{k}}" u-i="98280c54-4,98280c54-2" bind:__l="__l" u-p="{{l}}"> 确定 </up-button></view></view></up-popup></view>