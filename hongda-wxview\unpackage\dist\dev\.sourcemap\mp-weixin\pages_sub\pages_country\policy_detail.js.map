{"version": 3, "file": "policy_detail.js", "sources": ["pages_sub/pages_country/policy_detail.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX2NvdW50cnlccG9saWN5X2RldGFpbC52dWU"], "sourcesContent": ["<template>\r\n  <DetailSkeleton v-if=\"loading\" />\r\n\r\n  <view v-else-if=\"article\" class=\"page-container\">\r\n    <view class=\"fixed-header\" :style=\"{ height: headerHeight + 'px' }\">\r\n      <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n      <view class=\"custom-nav-bar\" :style=\"{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }\">\r\n        <view class=\"nav-back-button\" @click=\"goBack\">\r\n          <uni-icons type=\"left\" color=\"#000000\" size=\"22\"></uni-icons>\r\n        </view>\r\n        <view class=\"nav-title\">政策详情</view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view scroll-y class=\"scrollable-content\" :style=\"{ paddingTop: headerHeight + 'px' }\">\r\n      <view class=\"main-content\">\r\n        <view class=\"article-header\">\r\n          <text class=\"article-title\">{{ article.title }}</text>\r\n          <view class=\"article-meta\">\r\n            <view class=\"meta-item\">\r\n              <text class=\"meta-label\">发布于 {{ article.createTime }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"content-card\">\r\n          <view class=\"content-body\">\r\n            <mp-html\r\n                :content=\"article.content\"\r\n                :domain=\"baseUrl\"\r\n                :tag-style=\"tagStyle\"\r\n                :container-style=\"containerStyle\"\r\n                :preview-img=\"true\"\r\n                lazy-load\r\n            />\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n\r\n  <view v-else class=\"error-state\">\r\n    <u-empty mode=\"list\" text=\"文章不存在或已被删除\"></u-empty>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport {onLoad, onShareAppMessage} from '@dcloudio/uni-app'\r\nimport {ref} from 'vue';\r\nimport mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue';\r\nimport {getCountryPolicyArticle} from '@/api/content/countryPolicy.js';\r\nimport {IMAGE_BASE_URL} from '@/utils/config.js';\r\nimport {containerStyle, tagStyle} from '@/utils/mpHtmlStyles.js';\r\n// [关键修改 1] 导入公共的富文本格式化函数\r\nimport { formatRichText } from '@/utils/tools.js';\r\n\r\n// --- 自定义导航栏相关逻辑 (无变化) ---\r\nconst navBarPaddingBottomRpx = 10;\r\nconst navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);\r\n\r\nconst statusBarHeight = ref(0);\r\nconst navBarHeight = ref(0);\r\nconst headerHeight = ref(0);\r\n\r\nconst getNavBarInfo = () => {\r\n  try {\r\n    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n    statusBarHeight.value = menuButtonInfo.top;\r\n    navBarHeight.value = menuButtonInfo.height;\r\n    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;\r\n  } catch (e) {\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    statusBarHeight.value = systemInfo.statusBarHeight || 20;\r\n    navBarHeight.value = 44;\r\n    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;\r\n  }\r\n};\r\n\r\nconst goBack = () => {\r\n  uni.navigateBack({ delta: 1 });\r\n};\r\n// --- 导航栏逻辑结束 ---\r\n\r\n\r\nconst baseUrl = IMAGE_BASE_URL;\r\n\r\nconst article = ref(null);\r\nconst loading = ref(true);\r\n\r\nonLoad(async (options) => {\r\n  console.log('政策详情页 onLoad - 页面首次加载');\r\n  uni.showShareMenu({\r\n      menus: ['shareAppMessage', 'shareTimeline']\r\n    });\r\n\r\n  getNavBarInfo();\r\n\r\n  if (!options.id) {\r\n    uni.showToast({ title: '参数错误', icon: 'none' });\r\n    loading.value = false;\r\n    return;\r\n  }\r\n\r\n  try {\r\n    const res = await getCountryPolicyArticle(options.id);\r\n    if (res.code === 200 && res.data) {\r\n      // [关键修改 2] 在将数据赋值给 article.value 之前，调用函数处理 content 字段\r\n      res.data.content = formatRichText(res.data.content);\r\n      article.value = res.data;\r\n    } else {\r\n      throw new Error(res.msg || '文章不存在或已被删除');\r\n    }\r\n  } catch (error) {\r\n    console.error('获取国别政策文章失败:', error);\r\n    article.value = null;\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n});\r\n\r\nonShareAppMessage(() => {\r\n  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 样式无需修改 */\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n}\r\n\r\n.fixed-header {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 100;\r\n  background-color: #ffffff;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.scrollable-content {\r\n  flex: 1;\r\n  height: 0;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.status-bar {\r\n  width: 100%;\r\n}\r\n\r\n.custom-nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  box-sizing: content-box;\r\n}\r\n\r\n.nav-back-button {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15rpx;\r\n}\r\n\r\n.nav-title {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #000000;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 60%;\r\n}\r\n\r\n.main-content {\r\n  padding: 30rpx;\r\n}\r\n\r\n.article-header {\r\n  background: #ffffff;\r\n  padding: 40rpx;\r\n  border-radius: 24rpx;\r\n  margin-bottom: 30rpx;\r\n  .article-title {\r\n    font-size: 44rpx;\r\n    font-weight: 700;\r\n    color: #303133;\r\n    line-height: 1.5;\r\n  }\r\n  .article-meta {\r\n    margin-top: 30rpx;\r\n    font-size: 26rpx;\r\n    color: #909399;\r\n  }\r\n}\r\n\r\n.content-card {\r\n  background: #ffffff;\r\n  border-radius: 24rpx;\r\n  padding: 10rpx 40rpx 40rpx 40rpx;\r\n}\r\n\r\n.error-state {\r\n  padding-top: 200rpx;\r\n}\r\n\r\n:deep(.mp-html table) {\r\n  width: 100% !important;\r\n  border-collapse: collapse !important;\r\n  margin: 40rpx 0 !important;\r\n  border: 2rpx solid #e9e9eb !important;\r\n}\r\n\r\n:deep(.mp-html th) {\r\n  background: #f8f9fa !important;\r\n  color: #303133 !important;\r\n  padding: 24rpx !important;\r\n  border: 2rpx solid #e9e9eb !important;\r\n  font-weight: 600 !important;\r\n}\r\n\r\n:deep(.mp-html td) {\r\n  border: 2rpx solid #e9e9eb !important;\r\n  padding: 24rpx !important;\r\n  background: #ffffff !important;\r\n  color: #3D424D !important;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_country/policy_detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ref", "IMAGE_BASE_URL", "onLoad", "getCountryPolicyArticle", "formatRichText", "onShareAppMessage"], "mappings": ";;;;;;;;;;;;;;;;;AAiDA,MAAA,SAAA,MAAA;AAQA,MAAA,yBAAA;;;;AACA,UAAA,wBAAAA,cAAA,MAAA,OAAA,sBAAA;AAEA,UAAA,kBAAAC,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAAA,IAAA,CAAA;AAEA,UAAA,gBAAA,MAAA;AACA,UAAA;AACA,cAAA,iBAAAD,oBAAA;AACA,wBAAA,QAAA,eAAA;AACA,qBAAA,QAAA,eAAA;AACA,qBAAA,QAAA,eAAA,SAAA;AAAA,MACA,SAAA,GAAA;AACA,cAAA,aAAAA,oBAAA;AACA,wBAAA,QAAA,WAAA,mBAAA;AACA,qBAAA,QAAA;AACA,qBAAA,QAAA,gBAAA,QAAA,aAAA,QAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,SAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA,EAAA,OAAA,EAAA,CAAA;AAAA,IACA;AAIA,UAAA,UAAAE,aAAAA;AAEA,UAAA,UAAAD,cAAAA,IAAA,IAAA;AACA,UAAA,UAAAA,cAAAA,IAAA,IAAA;AAEAE,kBAAA,OAAA,OAAA,YAAA;AACAH,oBAAAA,MAAA,MAAA,OAAA,mDAAA,uBAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAEA;AAEA,UAAA,CAAA,QAAA,IAAA;AACAA,sBAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,OAAA,CAAA;AACA,gBAAA,QAAA;AACA;AAAA,MACA;AAEA,UAAA;AACA,cAAA,MAAA,MAAAI,0BAAAA,wBAAA,QAAA,EAAA;AACA,YAAA,IAAA,SAAA,OAAA,IAAA,MAAA;AAEA,cAAA,KAAA,UAAAC,YAAAA,eAAA,IAAA,KAAA,OAAA;AACA,kBAAA,QAAA,IAAA;AAAA,QACA,OAAA;AACA,gBAAA,IAAA,MAAA,IAAA,OAAA,YAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAL,sBAAA,MAAA,MAAA,SAAA,oDAAA,eAAA,KAAA;AACA,gBAAA,QAAA;AAAA,MACA,UAAA;AACA,gBAAA,QAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEAM,kBAAAA,kBAAA,MAAA;AACA,aAAA;IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzHA,GAAG,WAAW,eAAe;"}