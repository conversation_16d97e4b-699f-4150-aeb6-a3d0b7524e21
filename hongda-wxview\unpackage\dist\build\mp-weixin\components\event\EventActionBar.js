"use strict";const e=require("../../common/vendor.js"),t={__name:"EventActionBar",props:{eventDetail:{type:Object,required:!0},isLoading:{type:Boolean,default:!1},registrationStatus:{type:String,required:!0},isButtonDisabled:{type:Boolean,required:!0},buttonText:{type:String,required:!0}},setup(t){const i=e.ref("");return e.onMounted((()=>{const t=e.index.getStorageSync("staticAssets");i.value=(null==t?void 0:t.detail_icon_share)||""})),(n,o)=>e.e({a:!t.isLoading&&t.eventDetail},!t.isLoading&&t.eventDetail?{b:i.value,c:e.o((e=>n.$emit("share"))),d:e.t(t.buttonText),e:t.isButtonDisabled?1:"",f:"registered"===t.registrationStatus?1:"",g:e.o((e=>t.isButtonDisabled?null:n.$emit("register")))}:{})}},i=e._export_sfc(t,[["__scopeId","data-v-9efed42f"]]);wx.createComponent(i);
