{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <HeaderComponent @height-calculated=\"onHeaderHeightChange\" />\r\n\r\n    <scroll-view\r\n        class=\"main-scroll-view\"\r\n        :style=\"{ paddingTop: mainPaddingTop + 'px' }\"\r\n        scroll-y\r\n        @scrolltolower=\"handleScrollToLower\"\r\n    >\r\n      <view>\r\n        <BannerComponent />\r\n      </view>\r\n\r\n      <view>\r\n        <QuickNavigationComponent/>\r\n      </view>\r\n\r\n      <view>\r\n        <CountryHighlightComponent/>\r\n      </view>\r\n\r\n      <view>\r\n        <EventPromotionComponent/>\r\n      </view>\r\n\r\n      <view>\r\n        <NewsListComponent/>\r\n      </view>\r\n\r\n      <view>\r\n        <ActivityGridComponent ref=\"activityRef\" @all-loaded-change=\"onAllLoadedChange\"/>\r\n      </view>\r\n\r\n      <NoMoreDivider v-if=\"showNoMore\" />\r\n\r\n    </scroll-view>\r\n\r\n    <CustomTabBar :current=\"0\"/>\r\n\r\n    <view class=\"fab-customer-service\" @click=\"navigateToService\">\r\n      <image class=\"fab-icon\" :src=\"fabIconUrl\" mode=\"aspectFit\"></image>\r\n    </view>\r\n\r\n    <PopupAdComponent\r\n        v-if=\"showPopupAd && currentAdData\"\r\n        :show=\"showPopupAd\"\r\n        :ad-data=\"currentAdData\"\r\n        @close=\"handlePopupClose\"\r\n    />\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\nimport { onShow, onLoad ,onShareAppMessage } from '@dcloudio/uni-app';\r\nimport { getAdListByPositionApi } from '@/api/platform/ad.js';\r\nimport HeaderComponent from \"@/components/home/<USER>\";\r\nimport BannerComponent from '@/components/home/<USER>';\r\nimport QuickNavigationComponent from '@/components/home/<USER>';\r\nimport CountryHighlightComponent from '@/components/home/<USER>';\r\nimport NewsListComponent from \"@/components/home/<USER>\";\r\nimport ActivityGridComponent from '@/components/home/<USER>';\r\nimport EventPromotionComponent from '@/components/home/<USER>';\r\nimport PopupAdComponent from '@/components/common/PopupAdComponent.vue';\r\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue';\r\nimport NoMoreDivider from '@/components/common/NoMoreDivider.vue';\r\n\r\n// --- 新增：用于接收 Header 高度的状态 ---\r\nconst mainPaddingTop = ref(88); // 设置一个初始默认值 (约176rpx / 2), 防止页面初次渲染时闪烁\r\n\r\n// --- 新增：处理 Header 高度变化的方法 ---\r\nconst onHeaderHeightChange = (height) => {\r\n  // 确保高度值有效\r\n  if (height && height > 0) {\r\n    mainPaddingTop.value = height;\r\n  }\r\n};\r\n\r\n\r\n// --- 与精选活动滚动加载相关的状态 ---\r\nconst activityRef = ref(null)\r\nconst showNoMore = ref(false)\r\n\r\n// --- 状态定义 ---\r\n\r\n// 定义客服图标的URL\r\nconst fabIconUrl = ref('');\r\n\r\n// 弹窗广告相关\r\nconst showPopupAd = ref(false);\r\nconst adList = ref([]);\r\nconst currentAdIndex = ref(0);\r\nconst currentAdData = ref(null);\r\nconst AD_POSITION_CODE = 'SPLASH_SCREEN';\r\n\r\n// 会话标记\r\nlet hasShownInCurrentSession = false;\r\n\r\n// --- 方法定义 ---\r\n\r\nconst handleScrollToLower = () => {\r\n  if (activityRef.value && activityRef.value.loadMore) {\r\n    activityRef.value.loadMore()\r\n  }\r\n}\r\n\r\nconst onAllLoadedChange = (finished) => {\r\n  showNoMore.value = !!finished\r\n}\r\n\r\nconst checkAndShowPopupAd = async () => {\r\n  try {\r\n    if (hasShownInCurrentSession) {\r\n      return;\r\n    }\r\n    const response = await getAdListByPositionApi(AD_POSITION_CODE, { pageSize: 10 });\r\n    if (response && response.data && response.data.length > 0) {\r\n      adList.value = response.data;\r\n      currentAdIndex.value = 0;\r\n      showNextAd();\r\n    } else {\r\n      hasShownInCurrentSession = true;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取弹窗广告失败:', error.message || error);\r\n    hasShownInCurrentSession = true;\r\n  }\r\n};\r\n\r\nconst showNextAd = () => {\r\n  if (currentAdIndex.value < adList.value.length) {\r\n    currentAdData.value = adList.value[currentAdIndex.value];\r\n    showPopupAd.value = true;\r\n  } else {\r\n    showPopupAd.value = false;\r\n    currentAdData.value = null;\r\n  }\r\n};\r\n\r\nconst handlePopupClose = () => {\r\n  showPopupAd.value = false;\r\n  currentAdIndex.value++;\r\n  if (currentAdIndex.value >= adList.value.length) {\r\n    hasShownInCurrentSession = true;\r\n    return;\r\n  }\r\n  setTimeout(() => {\r\n    showNextAd();\r\n  }, 300);\r\n};\r\n\r\nconst simulateAppRestart = () => {\r\n  hasShownInCurrentSession = false;\r\n  uni.showToast({\r\n    title: '已模拟重启',\r\n    icon: 'success'\r\n  });\r\n  checkAndShowPopupAd();\r\n};\r\n\r\nconst navigateToService = () => {\r\n  uni.navigateTo({\r\n    url: '/pages_sub/pages_profile/contact'\r\n  });\r\n};\r\n\r\n// --- 生命周期钩子 ---\r\nonLoad(() => {\r\n  console.log('首页 onLoad - 页面首次加载');\r\n  uni.showShareMenu({\r\n      menus: ['shareAppMessage', 'shareTimeline']\r\n    });\r\n});\r\n\r\nonShow(() => {\r\n  console.log('首页 onShow - 页面显示');\r\n  uni.hideTabBar();\r\n  checkAndShowPopupAd();\r\n  const assets = uni.getStorageSync('staticAssets');\r\n  fabIconUrl.value = assets?.fab_customer_service_icon || '';\r\n});\r\n\r\nonShareAppMessage(() => {\n  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start; /* <--- 添加这一行代码 */\r\n  height: 100vh;\r\n  background-color: #FFFFFF;\r\n  overflow: hidden;\r\n}\r\n\r\n.main-scroll-view {\r\n  flex: 1;\r\n  height: 0;\r\n  position: relative;\r\n  z-index: 0;\r\n  transform: translateZ(0);\r\n  /* 移除了固定的 padding-top，改为JS动态绑定 */\r\n  /* padding-top: 176rpx; */\r\n  padding-bottom: calc(144rpx + env(safe-area-inset-bottom));\r\n  box-sizing: border-box;\r\n\r\n  :deep(.u-loadmore) {\r\n    display: none !important;\r\n  }\r\n}\r\n\r\n.fab-customer-service {\r\n  position: fixed;\r\n  right: 20rpx;\r\n  bottom: calc(200rpx + env(safe-area-inset-bottom));\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 50%;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 999;\r\n  transition: opacity 0.3s;\r\n}\r\n\r\n.fab-customer-service:active {\r\n  opacity: 0.7;\r\n}\r\n\r\n.fab-icon {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n}\r\n\r\n\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "getAdListByPositionApi", "uni", "onLoad", "onShow", "onShareAppMessage"], "mappings": ";;;;;;AAyDA,MAAA,kBAAA,MAAA;AACA,MAAA,kBAAA,MAAA;AACA,MAAA,2BAAA,MAAA;AACA,MAAA,4BAAA,MAAA;AACA,MAAA,oBAAA,MAAA;AACA,MAAA,wBAAA,MAAA;AACA,MAAA,0BAAA,MAAA;AACA,MAAA,mBAAA,MAAA;AACA,MAAA,eAAA,MAAA;AACA,MAAA,gBAAA,MAAA;AA4BA,MAAA,mBAAA;;;;AAzBA,UAAA,iBAAAA,cAAAA,IAAA,EAAA;AAGA,UAAA,uBAAA,CAAA,WAAA;AAEA,UAAA,UAAA,SAAA,GAAA;AACA,uBAAA,QAAA;AAAA,MACA;AAAA,IACA;AAIA,UAAA,cAAAA,cAAA,IAAA,IAAA;AACA,UAAA,aAAAA,cAAA,IAAA,KAAA;AAKA,UAAA,aAAAA,cAAAA,IAAA,EAAA;AAGA,UAAA,cAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,SAAAA,cAAAA,IAAA,CAAA,CAAA;AACA,UAAA,iBAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,gBAAAA,cAAAA,IAAA,IAAA;AAIA,QAAA,2BAAA;AAIA,UAAA,sBAAA,MAAA;AACA,UAAA,YAAA,SAAA,YAAA,MAAA,UAAA;AACA,oBAAA,MAAA,SAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,oBAAA,CAAA,aAAA;AACA,iBAAA,QAAA,CAAA,CAAA;AAAA,IACA;AAEA,UAAA,sBAAA,YAAA;AACA,UAAA;AACA,YAAA,0BAAA;AACA;AAAA,QACA;AACA,cAAA,WAAA,MAAAC,uCAAA,kBAAA,EAAA,UAAA,GAAA,CAAA;AACA,YAAA,YAAA,SAAA,QAAA,SAAA,KAAA,SAAA,GAAA;AACA,iBAAA,QAAA,SAAA;AACA,yBAAA,QAAA;AACA;QACA,OAAA;AACA,qCAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAC,4BAAA,MAAA,SAAA,gCAAA,aAAA,MAAA,WAAA,KAAA;AACA,mCAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,aAAA,MAAA;AACA,UAAA,eAAA,QAAA,OAAA,MAAA,QAAA;AACA,sBAAA,QAAA,OAAA,MAAA,eAAA,KAAA;AACA,oBAAA,QAAA;AAAA,MACA,OAAA;AACA,oBAAA,QAAA;AACA,sBAAA,QAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,mBAAA,MAAA;AACA,kBAAA,QAAA;AACA,qBAAA;AACA,UAAA,eAAA,SAAA,OAAA,MAAA,QAAA;AACA,mCAAA;AACA;AAAA,MACA;AACA,iBAAA,MAAA;AACA;MACA,GAAA,GAAA;AAAA,IACA;AAWA,UAAA,oBAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGAC,kBAAAA,OAAA,MAAA;AACAD,oBAAAA,MAAA,MAAA,OAAA,gCAAA,oBAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAAA,IACA,CAAA;AAEAE,kBAAAA,OAAA,MAAA;AACAF,oBAAAA,MAAA,MAAA,OAAA,gCAAA,kBAAA;AACAA,oBAAA,MAAA,WAAA;AACA;AACA,YAAA,SAAAA,cAAAA,MAAA,eAAA,cAAA;AACA,iBAAA,SAAA,iCAAA,8BAAA;AAAA,IACA,CAAA;AAEAG,kBAAAA,kBAAA,MAAA;AACA,aAAA;IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxLA,GAAG,WAAW,eAAe;"}