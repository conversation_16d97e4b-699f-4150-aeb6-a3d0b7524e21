<template>
  <view class="page-container">
    <HeaderComponent @height-calculated="onHeaderHeightChange" />

    <scroll-view
        class="main-scroll-view"
        :style="{ paddingTop: mainPaddingTop + 'px' }"
        scroll-y
        @scrolltolower="handleScrollToLower"
    >
      <view>
        <BannerComponent />
      </view>

      <view>
        <QuickNavigationComponent/>
      </view>

      <view>
        <CountryHighlightComponent/>
      </view>

      <view>
        <EventPromotionComponent/>
      </view>

      <view>
        <NewsListComponent/>
      </view>

      <view>
        <ActivityGridComponent ref="activityRef" @all-loaded-change="onAllLoadedChange"/>
      </view>

      <NoMoreDivider v-if="showNoMore" />

    </scroll-view>

    <CustomTabBar :current="0"/>

    <view class="fab-customer-service" @click="navigateToService">
      <image class="fab-icon" :src="fabIconUrl" mode="aspectFit"></image>
    </view>

    <PopupAdComponent
        v-if="showPopupAd && currentAdData"
        :show="showPopupAd"
        :ad-data="currentAdData"
        @close="handlePopupClose"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onShow, onLoad ,onShareAppMessage } from '@dcloudio/uni-app';
import { getAdListByPositionApi } from '@/api/platform/ad.js';
import HeaderComponent from "@/components/home/<USER>";
import BannerComponent from '@/components/home/<USER>';
import QuickNavigationComponent from '@/components/home/<USER>';
import CountryHighlightComponent from '@/components/home/<USER>';
import NewsListComponent from "@/components/home/<USER>";
import ActivityGridComponent from '@/components/home/<USER>';
import EventPromotionComponent from '@/components/home/<USER>';
import PopupAdComponent from '@/components/common/PopupAdComponent.vue';
import CustomTabBar from '@/components/layout/CustomTabBar.vue';
import NoMoreDivider from '@/components/common/NoMoreDivider.vue';

// --- 新增：用于接收 Header 高度的状态 ---
const mainPaddingTop = ref(88); // 设置一个初始默认值 (约176rpx / 2), 防止页面初次渲染时闪烁

// --- 新增：处理 Header 高度变化的方法 ---
const onHeaderHeightChange = (height) => {
  // 确保高度值有效
  if (height && height > 0) {
    mainPaddingTop.value = height;
  }
};


// --- 与精选活动滚动加载相关的状态 ---
const activityRef = ref(null)
const showNoMore = ref(false)

// --- 状态定义 ---

// 定义客服图标的URL
const fabIconUrl = ref('');

// 弹窗广告相关
const showPopupAd = ref(false);
const adList = ref([]);
const currentAdIndex = ref(0);
const currentAdData = ref(null);
const AD_POSITION_CODE = 'SPLASH_SCREEN';

// 会话标记
let hasShownInCurrentSession = false;

// --- 方法定义 ---

const handleScrollToLower = () => {
  if (activityRef.value && activityRef.value.loadMore) {
    activityRef.value.loadMore()
  }
}

const onAllLoadedChange = (finished) => {
  showNoMore.value = !!finished
}

const checkAndShowPopupAd = async () => {
  try {
    if (hasShownInCurrentSession) {
      return;
    }
    const response = await getAdListByPositionApi(AD_POSITION_CODE, { pageSize: 10 });
    if (response && response.data && response.data.length > 0) {
      adList.value = response.data;
      currentAdIndex.value = 0;
      showNextAd();
    } else {
      hasShownInCurrentSession = true;
    }
  } catch (error) {
    console.error('获取弹窗广告失败:', error.message || error);
    hasShownInCurrentSession = true;
  }
};

const showNextAd = () => {
  if (currentAdIndex.value < adList.value.length) {
    currentAdData.value = adList.value[currentAdIndex.value];
    showPopupAd.value = true;
  } else {
    showPopupAd.value = false;
    currentAdData.value = null;
  }
};

const handlePopupClose = () => {
  showPopupAd.value = false;
  currentAdIndex.value++;
  if (currentAdIndex.value >= adList.value.length) {
    hasShownInCurrentSession = true;
    return;
  }
  setTimeout(() => {
    showNextAd();
  }, 300);
};

const simulateAppRestart = () => {
  hasShownInCurrentSession = false;
  uni.showToast({
    title: '已模拟重启',
    icon: 'success'
  });
  checkAndShowPopupAd();
};

const navigateToService = () => {
  uni.navigateTo({
    url: '/pages_sub/pages_profile/contact'
  });
};

// --- 生命周期钩子 ---
onLoad(() => {
  console.log('首页 onLoad - 页面首次加载');
  uni.showShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    });
});

onShow(() => {
  console.log('首页 onShow - 页面显示');
  uni.hideTabBar();
  checkAndShowPopupAd();
  const assets = uni.getStorageSync('staticAssets');
  fabIconUrl.value = assets?.fab_customer_service_icon || '';
});

onShareAppMessage(() => {
  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为
});
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* <--- 添加这一行代码 */
  height: 100vh;
  background-color: #FFFFFF;
  overflow: hidden;
}

.main-scroll-view {
  flex: 1;
  height: 0;
  position: relative;
  z-index: 0;
  transform: translateZ(0);
  /* 移除了固定的 padding-top，改为JS动态绑定 */
  /* padding-top: 176rpx; */
  padding-bottom: calc(144rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;

  :deep(.u-loadmore) {
    display: none !important;
  }
}

.fab-customer-service {
  position: fixed;
  right: 20rpx;
  bottom: calc(200rpx + env(safe-area-inset-bottom));
  width: 100rpx;
  height: 100rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  transition: opacity 0.3s;
}

.fab-customer-service:active {
  opacity: 0.7;
}

.fab-icon {
  width: 60rpx;
  height: 60rpx;
}


</style>