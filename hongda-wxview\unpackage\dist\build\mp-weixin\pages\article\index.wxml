<view class="page-container data-v-ff362ccc"><view class="header-section data-v-ff362ccc" style="{{p}}"><view class="custom-nav-bar data-v-ff362ccc"><view class="status-bar data-v-ff362ccc"></view><view class="nav-title data-v-ff362ccc">资讯列表</view><view class="filter-bar data-v-ff362ccc"><view class="{{['sort-button', 'data-v-ff362ccc', c && 'is-active']}}" bindtap="{{d}}">{{a}} <view class="{{['custom-arrow', 'data-v-ff362ccc', b && 'rotate-180']}}"></view></view><view class="{{['filter-button', 'data-v-ff362ccc', h && 'is-active']}}" bindtap="{{i}}">{{e}} <view class="{{['custom-arrow', 'data-v-ff362ccc', f && 'rotate-180']}}"></view><view wx:if="{{g}}" class="active-dot data-v-ff362ccc"></view></view><view class="search-box data-v-ff362ccc"><uni-easyinput wx:if="{{o}}" class="search-input data-v-ff362ccc" bindconfirm="{{j}}" bindclear="{{k}}" bindinput="{{l}}" bindiconClick="{{m}}" u-i="ff362ccc-0" bind:__l="__l" bindupdateModelValue="{{n}}" u-p="{{o}}"></uni-easyinput></view></view></view></view><view class="content-section data-v-ff362ccc"><view class="tabs-container data-v-ff362ccc"><u-tabs wx:if="{{q}}" class="data-v-ff362ccc" bindchange="{{r}}" u-i="ff362ccc-1" bind:__l="__l" u-p="{{s}}"></u-tabs></view><scroll-view scroll-y class="article-list-scroll data-v-ff362ccc" bindscrolltolower="{{B}}" enable-flex><view class="article-list data-v-ff362ccc"><view wx:for="{{t}}" wx:for-item="item" wx:key="l" class="article-card data-v-ff362ccc" bindtap="{{item.m}}"><view class="card-cover data-v-ff362ccc"><u-image wx:if="{{item.f}}" class="data-v-ff362ccc" u-s="{{['loading','error']}}" binderror="{{item.c}}" bindload="{{item.d}}" u-i="{{item.e}}" bind:__l="__l" u-p="{{item.f}}"><view class="image-loading data-v-ff362ccc" slot="loading"><u-loading-icon wx:if="{{v}}" class="data-v-ff362ccc" u-i="{{item.a}}" bind:__l="__l" u-p="{{v}}"></u-loading-icon><text class="loading-text data-v-ff362ccc">加载中...</text></view><view class="image-error data-v-ff362ccc" slot="error"><u-icon wx:if="{{w}}" class="data-v-ff362ccc" u-i="{{item.b}}" bind:__l="__l" u-p="{{w}}"></u-icon><text class="error-text data-v-ff362ccc">图片加载失败</text></view></u-image></view><view class="card-content data-v-ff362ccc"><text class="card-title data-v-ff362ccc">{{item.g}}</text><view wx:if="{{item.h}}" class="card-tags data-v-ff362ccc"><text wx:for="{{item.i}}" wx:for-item="tag" wx:key="b" class="{{['data-v-ff362ccc', 'tag-item', tag.c]}}">{{tag.a}}</text></view><view class="card-meta data-v-ff362ccc"><text class="meta-source data-v-ff362ccc">{{item.j}}</text><text class="meta-date data-v-ff362ccc">{{item.k}}</text></view></view></view></view><u-empty wx:if="{{x}}" class="data-v-ff362ccc" u-i="ff362ccc-5" bind:__l="__l" u-p="{{y}}"></u-empty><no-more-divider wx:elif="{{z}}" class="data-v-ff362ccc" u-i="ff362ccc-6" bind:__l="__l"/><u-loadmore wx:else class="data-v-ff362ccc" u-i="ff362ccc-7" bind:__l="__l" u-p="{{A||''}}"/></scroll-view></view><view wx:if="{{C}}" class="dropdown-wrapper data-v-ff362ccc"><view class="dropdown-mask data-v-ff362ccc" bindtap="{{D}}"></view><view class="{{['dropdown-panel', 'data-v-ff362ccc', M && 'show']}}"><view wx:if="{{E}}" class="filter-panel data-v-ff362ccc"><scroll-view scroll-y class="filter-scroll data-v-ff362ccc"><view class="panel-section data-v-ff362ccc"><text class="section-title data-v-ff362ccc">排序方式</text><view class="panel-options data-v-ff362ccc"><view wx:for="{{F}}" wx:for-item="sort" wx:key="b" class="{{['option-btn', 'data-v-ff362ccc', sort.c && 'active']}}" bindtap="{{sort.d}}">{{sort.a}}</view></view></view></scroll-view><view class="panel-footer data-v-ff362ccc"><button class="footer-btn reset data-v-ff362ccc" bindtap="{{G}}">重置</button><button class="footer-btn confirm data-v-ff362ccc" bindtap="{{H}}">确定</button></view></view><view wx:if="{{I}}" class="filter-panel data-v-ff362ccc"><scroll-view scroll-y class="filter-scroll data-v-ff362ccc"><view class="panel-section data-v-ff362ccc"><text class="section-title data-v-ff362ccc">发布时间</text><view class="panel-options data-v-ff362ccc"><view wx:for="{{J}}" wx:for-item="time" wx:key="b" class="{{['option-btn', 'data-v-ff362ccc', time.c && 'active']}}" bindtap="{{time.d}}">{{time.a}}</view></view></view></scroll-view><view class="panel-footer data-v-ff362ccc"><button class="footer-btn reset data-v-ff362ccc" bindtap="{{K}}">重置</button><button class="footer-btn confirm data-v-ff362ccc" bindtap="{{L}}">确定</button></view></view></view></view><custom-tab-bar wx:if="{{N}}" class="data-v-ff362ccc" u-i="ff362ccc-8" bind:__l="__l" u-p="{{N}}"/></view>