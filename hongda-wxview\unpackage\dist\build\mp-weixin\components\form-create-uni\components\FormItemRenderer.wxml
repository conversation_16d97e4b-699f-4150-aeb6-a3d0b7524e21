<up-form-item wx:if="{{aC}}" u-s="{{['d']}}" class="form-item data-v-8194d7d4" style="{{'margin-bottom:' + '92rpx'}}" u-i="8194d7d4-0" bind:__l="__l" u-p="{{aC}}"><block wx:if="{{a}}"><up-input wx:if="{{g}}" class="data-v-8194d7d4" bindinput="{{b}}" bindchange="{{c}}" bindblur="{{d}}" bindconfirm="{{e}}" u-i="8194d7d4-1,8194d7d4-0" bind:__l="__l" bindupdateModelValue="{{f}}" u-p="{{g}}"/></block><block wx:elif="{{h}}"><up-textarea wx:if="{{n}}" class="data-v-8194d7d4" bindinput="{{i}}" bindchange="{{j}}" bindblur="{{k}}" bindconfirm="{{l}}" u-i="8194d7d4-2,8194d7d4-0" bind:__l="__l" bindupdateModelValue="{{m}}" u-p="{{n}}"/></block><block wx:elif="{{o}}"><view bindtap="{{r}}" class="custom-select-input data-v-8194d7d4" style="{{'width:' + '686rpx' + ';' + ('height:' + '76rpx') + ';' + ('background:' + '#FFFFFF') + ';' + ('border-radius:' + '8rpx') + ';' + ('border:' + '2rpx solid #CBCBCB') + ';' + ('padding:' + '20rpx') + ';' + ('box-sizing:' + 'border-box') + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'space-between') + ';' + ('position:' + 'relative')}}"><text class="data-v-8194d7d4" style="{{'font-size:' + '28rpx' + ';' + ('color:' + q) + ';' + ('flex:' + 1)}}">{{p}}</text><text class="select-arrow data-v-8194d7d4" style="{{'font-size:' + '24rpx' + ';' + ('color:' + '#909399') + ';' + ('margin-left:' + '20rpx')}}">▼</text></view></block><block wx:elif="{{s}}"><view bindtap="{{w}}" class="custom-select-input data-v-8194d7d4" style="{{'width:' + '686rpx' + ';' + ('height:' + '76rpx') + ';' + ('background:' + '#FFFFFF') + ';' + ('border-radius:' + '8rpx') + ';' + ('border:' + '2rpx solid #CBCBCB') + ';' + ('padding:' + '20rpx') + ';' + ('box-sizing:' + 'border-box') + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'space-between') + ';' + ('position:' + 'relative')}}"><text class="data-v-8194d7d4" style="{{'font-size:' + '28rpx' + ';' + ('color:' + v) + ';' + ('flex:' + 1)}}">{{t}}</text><text class="select-arrow data-v-8194d7d4" style="{{'font-size:' + '24rpx' + ';' + ('color:' + '#909399') + ';' + ('margin-left:' + '20rpx')}}">▼</text></view></block><block wx:elif="{{x}}"><view bindtap="{{A}}" class="custom-select-input data-v-8194d7d4" style="{{'width:' + '686rpx' + ';' + ('min-height:' + '76rpx') + ';' + ('background:' + '#FFFFFF') + ';' + ('border-radius:' + '8rpx') + ';' + ('border:' + '2rpx solid #CBCBCB') + ';' + ('padding:' + '20rpx') + ';' + ('box-sizing:' + 'border-box') + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'space-between') + ';' + ('position:' + 'relative')}}"><text class="data-v-8194d7d4" style="{{'font-size:' + '28rpx' + ';' + ('color:' + z) + ';' + ('flex:' + 1)}}">{{y}}</text><text class="select-arrow data-v-8194d7d4" style="{{'font-size:' + '24rpx' + ';' + ('color:' + '#909399') + ';' + ('margin-left:' + '20rpx')}}">▼</text></view></block><block wx:elif="{{B}}"><view class="transfer-container data-v-8194d7d4"><text class="transfer-title data-v-8194d7d4" style="{{'font-size:' + '24rpx' + ';' + ('color:' + '#666') + ';' + ('margin-bottom:' + '10rpx')}}"> 可选项（{{C}}项） </text><up-checkbox-group wx:if="{{F}}" class="data-v-8194d7d4" u-s="{{['d']}}" bindchange="{{E}}" u-i="8194d7d4-3,8194d7d4-0" bind:__l="__l" u-p="{{F}}"><up-checkbox wx:for="{{D}}" wx:for-item="option" wx:key="a" class="data-v-8194d7d4" u-i="{{option.b}}" bind:__l="__l" u-p="{{option.c}}"/></up-checkbox-group><text class="transfer-count data-v-8194d7d4" style="{{'font-size:' + '24rpx' + ';' + ('color:' + '#999') + ';' + ('margin-top:' + '10rpx')}}"> 已选择 {{G}} 项 </text></view></block><block wx:elif="{{H}}"><up-radio-group wx:if="{{K}}" class="data-v-8194d7d4" u-s="{{['d']}}" bindchange="{{J}}" u-i="8194d7d4-5,8194d7d4-0" bind:__l="__l" u-p="{{K}}"><up-radio wx:for="{{I}}" wx:for-item="option" wx:key="a" class="data-v-8194d7d4" u-i="{{option.b}}" bind:__l="__l" u-p="{{option.c}}"/></up-radio-group></block><block wx:elif="{{L}}"><up-checkbox-group wx:if="{{O}}" class="data-v-8194d7d4" u-s="{{['d']}}" bindchange="{{N}}" u-i="8194d7d4-7,8194d7d4-0" bind:__l="__l" u-p="{{O}}"><up-checkbox wx:for="{{M}}" wx:for-item="option" wx:key="a" class="data-v-8194d7d4" u-i="{{option.b}}" bind:__l="__l" u-p="{{option.c}}"/></up-checkbox-group></block><block wx:elif="{{P}}"><view class="switch-container data-v-8194d7d4"><up-switch wx:if="{{S}}" class="data-v-8194d7d4" bindchange="{{Q}}" u-i="8194d7d4-9,8194d7d4-0" bind:__l="__l" bindupdateModelValue="{{R}}" u-p="{{S}}"/><text wx:if="{{T}}" class="switch-desc data-v-8194d7d4">{{U}}</text></view></block><block wx:elif="{{V}}"><up-rate wx:if="{{Y}}" class="data-v-8194d7d4" bindchange="{{W}}" u-i="8194d7d4-10,8194d7d4-0" bind:__l="__l" bindupdateModelValue="{{X}}" u-p="{{Y}}"/></block><block wx:elif="{{Z}}"><view class="slider-container data-v-8194d7d4"><up-slider wx:if="{{ac}}" class="data-v-8194d7d4" bindchange="{{aa}}" u-i="8194d7d4-11,8194d7d4-0" bind:__l="__l" bindupdateModelValue="{{ab}}" u-p="{{ac}}"/></view></block><block wx:elif="{{ad}}"><view wx:if="{{ae}}" class="datetime-range-container data-v-8194d7d4"><picker mode="{{ag}}" value="{{ah}}" bindchange="{{ai}}" class="datetime-picker-item data-v-8194d7d4"><view class="datetime-input-display data-v-8194d7d4"><text class="datetime-value data-v-8194d7d4">{{af}}</text><text class="datetime-icon data-v-8194d7d4">📅</text></view></picker><text class="range-separator data-v-8194d7d4">至</text><picker mode="{{ak}}" value="{{al}}" bindchange="{{am}}" class="datetime-picker-item data-v-8194d7d4"><view class="datetime-input-display data-v-8194d7d4"><text class="datetime-value data-v-8194d7d4">{{aj}}</text><text class="datetime-icon data-v-8194d7d4">📅</text></view></picker></view><picker wx:else mode="{{ap}}" value="{{aq}}" bindchange="{{ar}}" class="datetime-picker-single data-v-8194d7d4"><view class="datetime-input-display data-v-8194d7d4"><text class="datetime-value data-v-8194d7d4">{{an}}</text><text class="datetime-icon data-v-8194d7d4">{{ao}}</text></view></picker></block><block wx:elif="{{as}}"><view class="color-picker-container data-v-8194d7d4"><view class="color-preview data-v-8194d7d4" style="{{'background-color:' + at}}" bindtap="{{av}}"/><up-input wx:if="{{ax}}" class="data-v-8194d7d4" bindclick="{{aw}}" u-i="8194d7d4-12,8194d7d4-0" bind:__l="__l" u-p="{{ax}}"/></view></block><block wx:elif="{{ay}}"><view class="upload-container data-v-8194d7d4"><up-upload wx:if="{{aB}}" class="data-v-8194d7d4" bindafterRead="{{az}}" binddelete="{{aA}}" u-i="8194d7d4-13,8194d7d4-0" bind:__l="__l" u-p="{{aB}}"/></view></block></up-form-item>