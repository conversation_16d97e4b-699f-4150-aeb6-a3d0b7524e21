"use strict";const e=require("../../common/vendor.js"),l={__name:"EventFilter",props:{initialFilters:{type:Object,default:()=>({sortBy:1,location:1,timeRange:1,status:1})},cities:{type:Array,default:()=>[]},viewType:{type:String,default:"list",validator:e=>["list","calendar"].includes(e)},notchLeft:{type:String,default:"60rpx"}},emits:["apply-filters","update:notchLeft"],setup(l,{emit:a}){const u=l,v=a,t=e.ref({...u.initialFilters}),o=e.ref({...u.initialFilters});e.watch((()=>u.initialFilters),(e=>{t.value={...e},o.value={...e}}),{deep:!0});const i=e.computed((()=>u.cities)),n=e.ref(!1),s=e.ref(!1),r=e.ref(!1),c=e.ref(!1),p=e.computed((()=>n.value||s.value||r.value||c.value)),f=e.ref([{label:"综合排序",value:1},{label:"最新发布",value:2},{label:"最近开始",value:3}]),d=e.ref({label:"全部地区",value:1}),y=e.ref([{label:"北京",value:2},{label:"上海",value:3},{label:"广州",value:4},{label:"深圳",value:5}]),b=e.computed((()=>[d.value,...y.value,...i.value.map(((e,l)=>({label:e,value:100+l})))])),g=e.ref([{label:"全部时间",value:1},{label:"1周内",value:2},{label:"1月内",value:3},{label:"1年内",value:4}]),m=e.ref([{label:"全部状态",value:1},{label:"即将开始",value:2},{label:"报名中",value:3},{label:"报名截止",value:4}]),w=e.computed((()=>{const e=f.value.find((e=>e.value===t.value.sortBy));return e?e.label:"综合排序"})),T=e.computed((()=>{const e=b.value.find((e=>e.value===t.value.location));return e?e.label:"热门地区"})),B=e.computed((()=>{const e=g.value.find((e=>e.value===t.value.timeRange));return e?e.label:"全部时间"})),R=e.computed((()=>{const e=m.value.find((e=>e.value===t.value.status));return e?e.label:"全部状态"})),h=()=>{n.value=!n.value,s.value=!1,r.value=!1,c.value=!1},x=()=>{s.value=!s.value,n.value=!1,r.value=!1,c.value=!1,"calendar"===u.viewType&&s.value&&v("update:notchLeft","60rpx")},F=()=>{r.value=!r.value,n.value=!1,s.value=!1,c.value=!1,"calendar"===u.viewType&&r.value&&v("update:notchLeft","240rpx")},_=()=>{c.value=!c.value,n.value=!1,s.value=!1,r.value=!1},L=e=>{o.value.location=e,console.log("临时选择地区:",e)},j=()=>{o.value.sortBy=1,t.value.sortBy=1,n.value=!1,console.log("重置排序筛选为初始状态"),v("apply-filters",{...t.value})},S=()=>{t.value.sortBy=o.value.sortBy,n.value=!1,console.log("应用排序筛选:",o.value.sortBy),v("apply-filters",{...t.value})},q=()=>{o.value.location=1,t.value.location=1,s.value=!1,console.log("重置地区筛选为初始状态"),v("apply-filters",{...t.value})},A=()=>{t.value.location=o.value.location,s.value=!1,console.log("应用地区筛选:",o.value.location),v("apply-filters",{...t.value})},C=()=>{o.value.timeRange=1,t.value.timeRange=1,r.value=!1,console.log("重置时间筛选为初始状态"),v("apply-filters",{...t.value})},E=()=>{t.value.timeRange=o.value.timeRange,r.value=!1,console.log("应用时间筛选:",o.value.timeRange),v("apply-filters",{...t.value})},I=()=>{o.value.status=1,t.value.status=1,c.value=!1,console.log("重置状态筛选为初始状态"),v("apply-filters",{...t.value})},O=()=>{t.value.status=o.value.status,c.value=!1,console.log("应用状态筛选:",o.value.status),v("apply-filters",{...t.value})},k=()=>{n.value=!1,s.value=!1,r.value=!1,c.value=!1};return(a,u)=>e.e({a:"list"===l.viewType},"list"===l.viewType?{b:e.t(w.value),c:n.value?1:"",d:n.value?1:"",e:e.o(h),f:e.t(T.value),g:s.value?1:"",h:s.value?1:"",i:e.o(x),j:e.t(B.value),k:r.value?1:"",l:r.value?1:"",m:e.o(F),n:e.t(R.value),o:c.value?1:"",p:c.value?1:"",q:e.o(_)}:"calendar"===l.viewType?{s:e.t(T.value),t:s.value?1:"",v:s.value?1:"",w:e.o(x),x:e.t(B.value),y:r.value?1:"",z:r.value?1:"",A:e.o(F)}:{},{r:"calendar"===l.viewType,B:p.value},p.value?e.e({C:"list"===l.viewType&&n.value},"list"===l.viewType&&n.value?{D:e.f(f.value,((l,a,u)=>({a:e.t(l.label),b:l.value,c:e.n({active:o.value.sortBy===l.value}),d:e.o((e=>{return a=l.value,o.value.sortBy=a,void console.log("临时选择排序:",a);var a}),l.value)}))),E:e.o(j),F:e.o(S)}:{},{G:s.value},s.value?e.e({H:e.t(d.value.label),I:e.n({active:o.value.location===d.value.value}),J:e.o((e=>L(d.value.value))),K:e.f(y.value,((l,a,u)=>({a:e.t(l.label),b:l.value,c:e.n({active:o.value.location===l.value}),d:e.o((e=>L(l.value)),l.value)}))),L:i.value.length>0},i.value.length>0?{M:e.f(i.value,((l,a,u)=>({a:e.t(l),b:100+a,c:e.n({active:o.value.location===100+a}),d:e.o((e=>L(100+a)),100+a)})))}:{},{N:e.o(q),O:e.o(A)}):{},{P:r.value},r.value?{Q:e.f(g.value,((l,a,u)=>({a:e.t(l.label),b:l.value,c:e.n({active:o.value.timeRange===l.value}),d:e.o((e=>{return a=l.value,o.value.timeRange=a,void console.log("临时选择时间:",a);var a}),l.value)}))),R:e.o(C),S:e.o(E)}:{},{T:"list"===l.viewType&&c.value},"list"===l.viewType&&c.value?{U:e.f(m.value,((l,a,u)=>({a:e.t(l.label),b:l.value,c:e.n({active:o.value.status===l.value}),d:e.o((e=>{return a=l.value,o.value.status=a,void console.log("临时选择状态:",a);var a}),l.value)}))),V:e.o(I),W:e.o(O)}:{}):{},{X:p.value},p.value?{Y:e.o(k)}:{},{Z:p.value?1:""})}},a=e._export_sfc(l,[["__scopeId","data-v-4b722c8f"]]);wx.createComponent(a);
