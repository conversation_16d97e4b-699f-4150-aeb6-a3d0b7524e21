"use strict";const e=require("../../common/vendor.js"),a=require("../../api/content/country.js"),l=require("../../utils/config.js"),t=require("../../utils/tools.js");if(!Array){e.resolveComponent("uni-load-more")()}Math||((()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+n)();const n=()=>"../../components/layout/CustomTabBar.js",u={__name:"index",setup(n){const u=e.computed((()=>f.value.bg_country_list_header||"")),o=e.computed((()=>f.value.bg_country_list_active_tab||"")),r=e.computed((()=>{const e=f.value.icon_common_search;return e?{"--search-icon-url":`url(${e})`}:{}})),i=l.IMAGE_BASE_URL,s=e.ref(""),v=e.ref("ALL"),c=e.ref([]),d=e.ref(!1),m=e.getCurrentInstance(),f=e.ref(e.index.getStorageSync("staticAssets")||{}),h=[{label:"全部",value:"ALL"},{label:"亚洲",value:"ASIA"},{label:"欧洲",value:"EUROPE"},{label:"北美洲",value:"NORTH_AMERICA"},{label:"南美洲",value:"SOUTH_AMERICA"},{label:"非洲",value:"AFRICA"},{label:"大洋洲",value:"OCEANIA"}],g=e.ref(0),p=e.ref([]),_=e.ref(0),b=e.ref(!1),x=e.ref(0),A=e.ref(0),y=e.computed((()=>{if(!p.value.length||g.value>=p.value.length)return-999;const a=p.value[g.value];if(!a)return-999;const l=e.index.upx2px(20);return a.left+a.width/2-_.value-l})),w=e=>{_.value=e.detail.scrollLeft},C=()=>new Promise((a=>{const l=e.index.createSelectorQuery().in(m);l.select(".continent-tabs").boundingClientRect(),l.selectAll(".tab-item").boundingClientRect(),l.exec((e=>{if(e&&e[1]&&e[1].length){const l=e[0],t=e[1];A.value=l?l.width:0,p.value=t,b.value=!0,a(t)}else a([])}))})),T=()=>{var e;if(!p.value.length||g.value>=p.value.length)return;const a=p.value[g.value],l=A.value;if(!a||!l)return;let t=a.left+a.width/2-l/2;const n=Math.max(0,(null==(e=a.dataset)?void 0:e.scrollWidth)-l||0);t=Math.max(0,Math.min(t,n)),x.value=t},M=async()=>{await e.nextTick$1();let a=0;const l=async()=>{const e=await C();0===e.length&&a<5?(a++,setTimeout(l,100)):e.length>0&&T()};l()},R=async(l=!1)=>{if(!d.value){d.value=!0;try{const t=await a.getCountryList({continent:v.value,keyword:s.value});c.value=l?t.data:[...c.value,...t.data]}catch(t){console.error("获取国别列表失败:",t),e.index.showToast({title:"数据加载失败",icon:"none"})}finally{d.value=!1,l&&e.index.stopPullDownRefresh()}}},L=t.debounce((()=>{R(!0)}),500),S=()=>{s.value.trim()?L():R(!0)},E=()=>{R(!0)},I=()=>{};return e.onLoad((()=>{console.log("国别列表页 onLoad - 页面首次加载"),e.index.showShareMenu({menus:["shareAppMessage","shareTimeline"]}),R(!0),setTimeout(M,200)})),e.onShow((()=>{e.index.hideTabBar(),setTimeout(M,100)})),e.onPullDownRefresh((()=>{R(!0)})),e.onShareAppMessage((()=>({}))),(a,l)=>e.e({a:e.o(E),b:e.o([e=>s.value=e.detail.value,S]),c:s.value,d:e.o(E),e:e.s(r.value),f:e.f(h,((a,l,t)=>({a:e.t(a.label),b:a.value,c:e.n({active:v.value===a.value}),d:e.o((t=>(async(a,l)=>{v.value!==a&&(g.value,v.value=a,g.value=l,p.value.length||await C(),e.nextTick$1((()=>{T()})),R(!0))})(a.value,l)),a.value),e:"tab-"+l,f:v.value===a.value?`url(${o.value})`:"none"}))),g:e.o(w),h:x.value,i:y.value+"px",j:b.value?1:0,k:`url(${u.value})`,l:e.f(c.value,((a,l,t)=>({a:e.unref(i)+a.listCoverUrl,b:e.t(a.nameCn),c:e.t(a.nameEn),d:e.t(a.summary),e:e.unref(i)+a.flagUrl,f:a.id,g:e.o((l=>{return t=a.id,void e.index.navigateTo({url:`/pages_sub/pages_country/detail?id=${t}`});var t}),a.id)}))),m:d.value},d.value?{n:e.p({status:"loading"})}:(c.value.length,{}),{o:0===c.value.length,p:e.o(I),q:e.p({current:3})})}},o=e._export_sfc(u,[["__scopeId","data-v-49955ee9"]]);u.__runtimeHooks=2,wx.createPage(o);
