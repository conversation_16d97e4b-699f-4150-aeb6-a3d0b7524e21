<view class="form-create-uni data-v-fba76324"><up-form wx:if="{{c}}" class="r data-v-fba76324" u-s="{{['d']}}" u-r="formRef" u-i="fba76324-0" bind:__l="__l" u-p="{{c}}"><form-item-renderer wx:for="{{a}}" wx:for-item="item" wx:key="a" class="data-v-fba76324" bindupdateField="{{item.b}}" bindopenPicker="{{item.c}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"/></up-form><form-picker-dialogs wx:if="{{i}}" class="r data-v-fba76324" u-r="pickerDialogsRef" bindpickerConfirm="{{e}}" bindpickerCancel="{{f}}" bindcascaderConfirm="{{g}}" bindcolorConfirm="{{h}}" u-i="fba76324-2" bind:__l="__l" u-p="{{i}}"/></view>