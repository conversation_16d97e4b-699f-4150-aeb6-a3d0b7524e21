"use strict";const e=require("../../common/vendor.js"),a=require("../../common/assets.js"),o=require("../../utils/image.js"),l=require("./api/platform/consultant.js");if(!Array){(e.resolveComponent("uni-icons")+e.resolveComponent("uni-load-more"))()}Math||((()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js"))();const u={__name:"contact",setup(u){const t=e.index.upx2px(20),n=e.ref(0),r=e.ref(0),s=e.ref(0),i=()=>{e.index.navigateBack({delta:1})},v=e.ref(!0),c=e.ref(null),d=e.computed((()=>c.value?o.getFullImageUrl(c.value.avatarUrl):"")),m=e.computed((()=>c.value?o.getFullImageUrl(c.value.qrCodeUrl):"")),p=()=>{m.value&&e.index.previewImage({urls:[m.value]})},g=e=>{console.error(`${e} image failed to load.`),"avatar"===e?c.value.avatarUrl="/static/images/default-avatar.png":"qrCode"===e&&(c.value.qrCodeUrl="/static/images/default-qrcode.png")};return e.onLoad((()=>{console.log("联系顾问页 onLoad - 页面首次加载"),e.index.showShareMenu({menus:["shareAppMessage","shareTimeline"]}),(()=>{try{const a=e.index.getMenuButtonBoundingClientRect();n.value=a.top,r.value=a.height,s.value=a.bottom+t}catch(a){const o=e.index.getSystemInfoSync();n.value=o.statusBarHeight||20,r.value=44,s.value=n.value+r.value+t}})(),v.value=!0,(async()=>{try{const e=await l.getDisplayConsultantApi();200===e.code&&e.data?c.value=e.data:c.value=null}catch(e){console.error("获取顾问信息失败:",e),c.value=null}finally{v.value=!1}})()})),e.onShareAppMessage((()=>({}))),(o,l)=>e.e({a:n.value+"px",b:e.p({type:"left",color:"#000000",size:"22"}),c:e.o(i),d:r.value+"px",e:e.unref(t)+"px",f:s.value+"px",g:v.value},v.value?{h:e.p({status:"loading","show-icon":!0})}:{},{i:!v.value&&c.value},!v.value&&c.value?{j:d.value,k:e.o((e=>g("avatar"))),l:e.t(c.value.name),m:e.t(c.value.introduction),n:m.value,o:e.o((e=>g("qrCode"))),p:e.o(p)}:{},{q:!v.value&&!c.value},v.value||c.value?{}:{r:a._imports_0},{s:s.value+"px"})}},t=e._export_sfc(u,[["__scopeId","data-v-799adeb2"]]);u.__runtimeHooks=2,wx.createPage(t);
