"use strict";const e=require("../../common/vendor.js");if(!Array){e.resolveComponent("up-form")()}Math||(r+(()=>"../../uni_modules/uview-plus/components/u-form/u-form.js")+o)();const r=()=>"./components/FormItemRenderer.js",o=()=>"./components/FormPickerDialogs.js",a={__name:"form-create-uni",props:{formConfig:{type:Array,default:()=>[]},modelValue:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(r,{expose:o,emit:a}){const t=r,l=a,i=e.reactive({}),c=e.reactive({}),n=e.ref(),s=e.ref(),f=e.ref(!1),u=e.ref([]),d=e.ref(!1),m=e.ref([]),p=e.ref(!1),y=e.ref(""),v=e.ref(["#FF0000","#FF7F00","#FFFF00","#00FF00","#0000FF","#4B0082","#9400D3","#FF1493","#00CED1","#FFD700","#32CD32","#FF6347","#4169E1","#BA55D3"]),g=e.computed((()=>t.formConfig&&Array.isArray(t.formConfig)?t.formConfig.filter((e=>$(e))):[]));let b=!1;const h=()=>{if(!b){const e={};Object.keys(i).forEach((r=>{e[r]=i[r]})),l("update:modelValue",e)}};let k=null,C=null;e.onMounted((()=>{e.nextTick$1((()=>{if(t.modelValue&&"object"==typeof t.modelValue){const e=JSON.parse(JSON.stringify(t.modelValue));Object.keys(e).forEach((r=>{i[r]=e[r]}))}t.formConfig&&Array.isArray(t.formConfig)&&(A(),O())}))})),e.onUpdated((()=>{(()=>{if(JSON.stringify(t.formConfig)!==JSON.stringify(k)&&(k=JSON.parse(JSON.stringify(t.formConfig||[])),t.formConfig&&Array.isArray(t.formConfig)&&(A(),O())),JSON.stringify(t.modelValue)!==JSON.stringify(C)&&(C=JSON.parse(JSON.stringify(t.modelValue||{})),t.modelValue&&"object"==typeof t.modelValue)){b=!0,Object.keys(i).forEach((e=>delete i[e]));const r=JSON.parse(JSON.stringify(t.modelValue));Object.keys(r).forEach((e=>{i[e]=r[e]})),e.nextTick$1((()=>{b=!1}))}})()}));const A=()=>{t.formConfig&&Array.isArray(t.formConfig)?t.formConfig.forEach((e=>{if(!e||!e.field||"string"!=typeof e.field)return;if(!["elTag","elDivider","html","div","elButton","elAlert","fcRow","elTabs","elCard","elCollapse","elCollapseItem","elTabPane","col","fcTable","space"].includes(e.type)&&!i[e.field]){let r=F(e);i[e.field]=r}})):console.warn("表单配置无效或为空")},F=e=>{const{type:r,defaultValue:o}=e;if(void 0!==o)return o;switch(r){case"checkbox":case"upload":case"imageUpload":case"fileUpload":case"cascader":return[];case"switch":return!1;case"rate":case"slider":return 0;case"daterange":case"timerange":return{start:"",end:""};default:return""}},O=()=>{t.formConfig&&Array.isArray(t.formConfig)&&(Object.keys(c).forEach((e=>delete c[e])),t.formConfig.forEach((e=>{if(!e||!e.field||"string"!=typeof e.field)return;if(!["elTag","elDivider","html","div","elButton","elAlert","fcRow","elTabs","elCard","elCollapse","elCollapseItem","elTabPane","col","fcTable","space"].includes(e.type)&&(e.required||e.$required)){const r=["select","cascader","radio","checkbox","datePicker","timePicker","colorPicker"].includes(e.type);c[e.field]=[{required:!0,message:`请${r?"选择":"输入"}${e.label||e.title}`,trigger:["blur","change"]}]}})))},j=(r,o)=>{if(r){try{i[r]=o}catch(a){console.warn("字段更新失败，尝试创建新属性:",a),r in i||(i[r]=o)}e.nextTick$1((()=>{h()}))}else console.warn("收到无效的字段名:",r)},w=e.ref(""),T=(r,o)=>{if(o&&o.field)switch(w.value=o.field,s.value&&"function"==typeof s.value.setCurrentField&&s.value.setCurrentField(o.field),r){case"select":o.options&&Array.isArray(o.options)?(u.value=[o.options],f.value=!0):e.index.$u.toast("选择器配置错误");break;case"cascader":o.options&&Array.isArray(o.options)?(m.value=[o.options],d.value=!0):e.index.$u.toast("级联选择器配置错误");break;case"tree":o.options&&Array.isArray(o.options)?(u.value=[o.options],f.value=!0):e.index.$u.toast("树形选择器配置错误");break;case"color":y.value=o.field,p.value=!0}},x=(r,o)=>{try{const{value:t}=r||{},l=o||w.value;if(!l||!t||!Array.isArray(t)||0===t.length)return;const c=t[0];if(!c||void 0===c.value)return;try{l in i&&delete i[l],i[l]=c.value}catch(a){const e={...i};e[l]=c.value,Object.keys(i).forEach((e=>delete i[e])),Object.keys(e).forEach((r=>{i[r]=e[r]}))}e.nextTick$1((()=>{h()}))}catch(a){}finally{S()}},D=(r,o)=>{try{const{value:a}=r||{},t=o||w.value;if(!t||!a||!Array.isArray(a))return;const l=a.map((e=>e&&e.value)).filter((e=>void 0!==e));i[t]=l,e.nextTick$1((()=>{h()}))}catch(a){}finally{d.value=!1}},E=r=>{y.value&&(i[y.value]=r,e.nextTick$1((()=>{h()}))),p.value=!1},S=()=>{f.value=!1,d.value=!1,p.value=!1},$=e=>{if(!e)return!1;if(!e.field||"string"!=typeof e.field)return!1;return!["elTag","elDivider","html","div","elButton","elAlert","fcRow","elTabs","elCard","elCollapse","elCollapseItem","elTabPane","col","fcTable","space"].includes(e.type)};return o({validate:()=>new Promise(((e,r)=>{if(!n.value)return console.error("表单引用不存在，无法进行验证"),void r(new Error("表单未初始化"));let o=!1;const a=[];if(t.formConfig.forEach((e=>{if(e&&e.field&&(e.required||e.$required)){const r=i[e.field];if(null==r||""===r||Array.isArray(r)&&0===r.length){o=!0;const r=["select","cascader","radio","checkbox","datePicker","timePicker","colorPicker"].includes(e.type);a.push(`请${r?"选择":"输入"}${e.label||e.title}`)}}})),o)return console.log("表单验证失败，缺少必填项:",a),void r(new Error(a[0]||"请填写必填项"));"function"==typeof n.value.validate?n.value.validate().then((()=>{console.log("表单验证通过"),e()})).catch((e=>{console.log("uView表单验证失败:",e),r(e)})):(console.log("手动验证通过，uView验证方法不可用"),e())})),formRef:n,forceReinit:(r,o)=>{e.nextTick$1((()=>{if(r&&Array.isArray(r)&&(Object.defineProperty(t,"formConfig",{value:r,writable:!0}),A(),O()),o&&"object"==typeof o){const e=JSON.parse(JSON.stringify(o));Object.keys(e).forEach((r=>{i[r]=e[r]}))}}))},getValue:e=>i[e],getFormData:()=>{const e={};return Object.keys(i).forEach((r=>{e[r]=i[r]})),e},updateFormData:r=>{if(r&&"object"==typeof r){console.log("updateFormData 接收到数据:",r);try{b=!0,Object.keys(i).forEach((e=>{delete i[e]}));const o=JSON.parse(JSON.stringify(r));Object.keys(o).forEach((e=>{i[e]=o[e]})),console.log("updateFormData 更新后的formData:",i),e.nextTick$1((()=>{b=!1,h()}))}catch(o){console.error("updateFormData 更新失败:",o),b=!1}}else console.warn("updateFormData: 无效的数据")},formData:i}),(r,o)=>({a:e.f(g.value,((r,o,a)=>({a:r.field,b:e.o(j,r.field),c:e.o(T,r.field),d:"fba76324-1-"+a+",fba76324-0",e:e.p({item:r,"form-data":i})}))),b:e.sr(n,"fba76324-0",{k:"formRef"}),c:e.p({model:i,rules:c,labelPosition:"top",labelWidth:"auto",labelStyle:{fontFamily:"Alibaba PuHuiTi 3.0-55 Regular",fontWeight:"normal",fontSize:"28rpx",color:"#23232A",lineHeight:"normal"}}),d:e.sr(s,"fba76324-2",{k:"pickerDialogsRef"}),e:e.o(x),f:e.o(S),g:e.o(D),h:e.o(E),i:e.p({"picker-show":f.value,"picker-columns":u.value,"cascader-show":d.value,"cascader-columns":m.value,"color-picker-show":p.value,"color-list":v.value,"current-color-field":y.value,"form-data":i})})}},t=e._export_sfc(a,[["__scopeId","data-v-fba76324"]]);wx.createComponent(t);
