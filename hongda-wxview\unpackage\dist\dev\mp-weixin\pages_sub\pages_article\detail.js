"use strict";
const common_vendor = require("../../common/vendor.js");
const api_content_article = require("../../api/content/article.js");
const pages_sub_pages_article_api_content_comment = require("./api/content/comment.js");
const pages_sub_pages_article_api_common_mpHtmlStyles = require("./api/common/mpHtmlStyles.js");
const utils_config = require("../../utils/config.js");
const utils_image = require("../../utils/image.js");
const utils_date = require("../../utils/date.js");
const utils_tools = require("../../utils/tools.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  (_easycom_u_icon2 + _easycom_u_popup2)();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_popup = () => "../../uni_modules/uview-plus/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_u_icon + mpHtml + CommentItem + _easycom_u_popup)();
}
const mpHtml = () => "../../uni_modules/mp-html/components/mp-html/mp-html.js";
const CommentItem = () => "../../components/common/CommentItem.js";
const navBarPaddingBottomRpx = 20;
const _sfc_main = {
  __name: "detail",
  setup(__props) {
    const navBarPaddingBottomPx = common_vendor.index.upx2px(navBarPaddingBottomRpx);
    const statusBarHeight = common_vendor.ref(0);
    const navBarHeight = common_vendor.ref(0);
    const headerHeight = common_vendor.ref(0);
    const showNewCommentModal = common_vendor.ref(false);
    const loadError = common_vendor.ref(false);
    const assets = common_vendor.ref(common_vendor.index.getStorageSync("staticAssets") || {});
    const commentInputIconUrl = common_vendor.computed(() => {
      return assets.value.icon_article_comment_input || "";
    });
    const summaryCardStyle = common_vendor.computed(() => {
      const imageUrl = assets.value.bg_article_summary_card;
      if (imageUrl) {
        return {
          backgroundImage: `url('${imageUrl}')`
        };
      }
      return {};
    });
    const getNavBarInfo = () => {
      try {
        const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
        statusBarHeight.value = menuButtonInfo.top;
        navBarHeight.value = menuButtonInfo.height;
        headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;
      } catch (e) {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        statusBarHeight.value = systemInfo.statusBarHeight || 20;
        navBarHeight.value = 44;
        headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;
      }
    };
    const getCurrentPageUrl = () => {
      try {
        const pages = getCurrentPages();
        const current = pages[pages.length - 1];
        const route = "/" + current.route;
        const options = current.options || {};
        const query = Object.keys(options).map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(options[key])}`).join("&");
        return query ? `${route}?${query}` : route;
      } catch (e) {
        return "/pages_sub/pages_article/detail";
      }
    };
    const ensureLoggedInForAction = () => {
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token) {
          const backUrl = getCurrentPageUrl();
          try {
            common_vendor.index.setStorageSync("loginBackPage", backUrl);
          } catch (e) {
          }
          common_vendor.index.navigateTo({ url: "/pages_sub/pages_other/login" });
          return false;
        }
        return true;
      } catch (e) {
        common_vendor.index.navigateTo({ url: "/pages_sub/pages_other/login" });
        return false;
      }
    };
    const handleOpenCommentInput = () => {
      if (!ensureLoggedInForAction())
        return;
      newComment.content = "";
      showNewCommentModal.value = true;
    };
    const closeNewCommentModal = () => {
      showNewCommentModal.value = false;
    };
    const goBack = () => {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        common_vendor.index.navigateBack({
          delta: 1
        });
      } else {
        common_vendor.index.switchTab({
          url: "/pages/article/index"
        });
      }
    };
    common_vendor.onBackPress(() => {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        return false;
      } else {
        common_vendor.index.switchTab({
          url: "/pages/article/index"
        });
        return true;
      }
    });
    const article = common_vendor.ref(null);
    const articleId = common_vendor.ref(null);
    const commentList = common_vendor.ref([]);
    const commentTotal = common_vendor.ref(0);
    const newComment = common_vendor.reactive({ content: "", isSubmitting: false });
    const isCommentsLoading = common_vendor.ref(false);
    const imageBaseUrl = common_vendor.computed(() => {
      if (!utils_config.config.imageBaseUrl)
        return "";
      return utils_config.config.imageBaseUrl.replace(/\/$/, "");
    });
    const fetchArticleData = async (id) => {
      loadError.value = false;
      try {
        const response = await api_content_article.getArticleDetail(id);
        if (response.code === 200 && response.data) {
          const rawArticle = response.data;
          rawArticle.content = utils_tools.formatRichText(rawArticle.content);
          let parsedTags = [];
          if (rawArticle.tags && typeof rawArticle.tags === "string") {
            try {
              parsedTags = JSON.parse(rawArticle.tags);
            } catch (e) {
              common_vendor.index.__f__("error", "at pages_sub/pages_article/detail.vue:251", "标签解析失败:", e);
            }
          } else if (Array.isArray(rawArticle.tags)) {
            parsedTags = rawArticle.tags;
          }
          if (!Array.isArray(parsedTags))
            parsedTags = [];
          article.value = { ...rawArticle, parsedTags };
        } else {
          common_vendor.index.showToast({ title: response.msg || "获取文章失败", icon: "none" });
          loadError.value = true;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_article/detail.vue:263", "获取文章失败:", error);
        common_vendor.index.showToast({ title: "网络请求失败", icon: "error" });
        loadError.value = true;
      }
    };
    const fetchComments = async () => {
      if (!articleId.value)
        return;
      isCommentsLoading.value = true;
      try {
        const response = await pages_sub_pages_article_api_content_comment.getCommentList({ relatedId: articleId.value, relatedType: "article" });
        if (response.code === 200 && response.data) {
          commentList.value = response.data.comments || [];
          commentTotal.value = response.data.total || 0;
        } else {
          commentList.value = [];
          commentTotal.value = 0;
        }
      } catch (error) {
        commentList.value = [];
        commentTotal.value = 0;
      } finally {
        isCommentsLoading.value = false;
      }
    };
    const handlePostComment = async ({ content, stateObject }) => {
      if (!content.trim() || stateObject.isSubmitting)
        return;
      stateObject.isSubmitting = true;
      try {
        const response = await pages_sub_pages_article_api_content_comment.addComment({
          relatedId: articleId.value,
          relatedType: "article",
          content: content.trim()
        });
        if (response.code === 200) {
          common_vendor.index.showToast({ title: "发布成功", icon: "success" });
          await fetchComments();
          return true;
        } else {
          common_vendor.index.showToast({ title: response.msg || "评论失败", icon: "error" });
          return false;
        }
      } catch (error) {
        return false;
      } finally {
        stateObject.isSubmitting = false;
      }
    };
    const postNewComment = async () => {
      if (!ensureLoggedInForAction())
        return;
      const success = await handlePostComment({ content: newComment.content, stateObject: newComment });
      if (success) {
        newComment.content = "";
        closeNewCommentModal();
      }
    };
    const initPageData = async () => {
      if (!articleId.value) {
        loadError.value = true;
        return;
      }
      await fetchArticleData(articleId.value);
      if (article.value) {
        await common_vendor.nextTick$1();
        getNavBarInfo();
        await fetchComments();
      }
    };
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages_sub/pages_article/detail.vue:340", "文章详情页 onLoad - 页面首次加载");
      common_vendor.index.showShareMenu({
        menus: ["shareAppMessage", "shareTimeline"]
      });
      articleId.value = options.id;
      initPageData();
    });
    common_vendor.onShareAppMessage(() => {
      return {};
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: article.value
      }, article.value ? common_vendor.e({
        b: statusBarHeight.value + "px",
        c: common_vendor.p({
          name: "arrow-left",
          color: "#000000",
          size: "22"
        }),
        d: common_vendor.o(goBack),
        e: navBarHeight.value + "px",
        f: common_vendor.unref(navBarPaddingBottomPx) + "px",
        g: headerHeight.value + "px",
        h: common_vendor.unref(utils_image.getFullImageUrl)(article.value.coverImageUrl),
        i: common_vendor.t(article.value.title),
        j: common_vendor.t(article.value.source),
        k: common_vendor.t(common_vendor.unref(utils_date.formatDate)(article.value.publishTime, "YYYY-MM-DD")),
        l: common_vendor.t(article.value.viewCount),
        m: article.value.summary
      }, article.value.summary ? {
        n: common_vendor.t(article.value.summary),
        o: common_vendor.s(summaryCardStyle.value)
      } : {}, {
        p: common_vendor.p({
          content: article.value.content,
          domain: imageBaseUrl.value,
          ["tag-style"]: common_vendor.unref(pages_sub_pages_article_api_common_mpHtmlStyles.tagStyle),
          ["preview-img"]: true,
          ["lazy-load"]: true
        }),
        q: article.value.parsedTags && article.value.parsedTags.length > 0
      }, article.value.parsedTags && article.value.parsedTags.length > 0 ? {
        r: common_vendor.f(article.value.parsedTags, (tag, index, i0) => {
          return {
            a: common_vendor.t(tag.name || tag),
            b: common_vendor.t(index < article.value.parsedTags.length - 1 ? " / " : ""),
            c: tag.id || tag.name
          };
        })
      } : {}, {
        s: common_vendor.t(commentTotal.value),
        t: commentInputIconUrl.value,
        v: common_vendor.o(handleOpenCommentInput),
        w: commentList.value.length > 0
      }, commentList.value.length > 0 ? {
        x: common_vendor.f(commentList.value, (comment, k0, i0) => {
          return {
            a: comment.id,
            b: "ebaf798e-2-" + i0,
            c: common_vendor.p({
              comment
            })
          };
        })
      } : {}, {
        y: headerHeight.value + "px",
        z: common_vendor.o(closeNewCommentModal),
        A: newComment.content,
        B: common_vendor.o(($event) => newComment.content = $event.detail.value),
        C: common_vendor.t(newComment.content.length),
        D: common_vendor.t(newComment.isSubmitting ? "发布中..." : "发布"),
        E: newComment.content.trim() ? 1 : "",
        F: !newComment.content.trim() || newComment.isSubmitting,
        G: common_vendor.o(postNewComment),
        H: common_vendor.o(closeNewCommentModal),
        I: common_vendor.p({
          show: showNewCommentModal.value,
          mode: "bottom",
          round: "20",
          ["safe-area-inset-bottom"]: true
        })
      }) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ebaf798e"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_article/detail.js.map
