"use strict";const e=require("../../common/vendor.js"),t=require("../../utils/tools.js"),n=require("../../utils/date.js"),r=require("../../utils/image.js"),s=require("../../utils/location.js"),a={__name:"EventCard",props:{event:{type:Object,required:!0}},setup(a){const i=e.ref(""),u=e.ref("");e.onMounted((()=>{const t=e.index.getStorageSync("staticAssets");i.value=(null==t?void 0:t.list_time)||"",u.value=(null==t?void 0:t.list_location)||""}));const o=e=>{switch(e){case 0:return"即将开始";case 1:return"报名中";case 2:return"报名截止";default:return"未知"}},c=e=>{switch(e){case 0:return"not-started";case 1:return"open";case 2:return"ended";default:return"unknown"}};return(l,v)=>e.e({a:e.unref(r.getFullImageUrl)(a.event.coverImageUrl),b:void 0!==a.event.registrationStatus},void 0!==a.event.registrationStatus?{c:e.t(o(a.event.registrationStatus)),d:e.n(c(a.event.registrationStatus))}:{},{e:e.t(a.event.title),f:i.value,g:e.t(e.unref(n.formatEventDate)(a.event.startTime)),h:u.value,i:e.t(e.unref(s.formatEventLocation)(a.event)),j:e.t(e.unref(t.calculateRemainingSpots)(a.event.maxParticipants,a.event.registeredCount)),k:e.o((e=>l.$emit("click",a.event)))})}},i=e._export_sfc(a,[["__scopeId","data-v-0668f973"]]);wx.createComponent(i);
