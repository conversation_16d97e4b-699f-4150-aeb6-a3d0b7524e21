"use strict";const e=require("../../common/vendor.js"),t=require("../../api/content/article.js"),n=require("../../utils/image.js");if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-tabs")+e.resolveComponent("u-image")+e.resolveComponent("u-empty"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-icon/u-icon.js")+(()=>"../../uni_modules/uview-plus/components/u-tabs/u-tabs.js")+(()=>"../../uni_modules/uview-plus/components/u-image/u-image.js")+(()=>"../../uni_modules/uview-plus/components/u-empty/u-empty.js"))();const a={__name:"NewsListComponent",props:{title:{type:String,default:"为你推荐"},params:{type:Object,default:()=>({pageNum:1})}},setup(a){const o=a,r=e.ref([]),i=e.ref([]),s=e.ref(0),l=e.ref({...o.params,isRecommended:1}),u=e.computed((()=>{const e=o.title;return e.length>2?{main:e.slice(0,2),gradient:e.slice(2)}:{main:e,gradient:""}})),c=async(e=!1)=>{e&&(l.value.pageNum=1);try{const e=await t.getArticleList(l.value);r.value=e.rows}catch(n){console.error("加载资讯列表失败:",n)}},p=e=>{s.value=e.index,l.value.tagIds=i.value[e.index].id,c(!0)},m=()=>{e.index.switchTab({url:"/pages/article/index"})};return e.onMounted((()=>{(async()=>{try{const e=await t.getRecommendedTags(),n=Array.isArray(e.data)?e.data:[];i.value=[{id:null,name:"最新"},...n]}catch(e){console.error("加载标签列表失败:",e)}})(),c(!0)})),(t,a)=>e.e({a:e.t(u.value.main),b:e.t(u.value.gradient),c:e.p({name:"arrow-right",size:"14",color:"#9B9A9A"}),d:e.o(m),e:e.o(p),f:e.p({list:i.value,current:s.value,keyName:"name",lineColor:"#023F98",lineHeight:2,lineWidth:20,"show-scrollbar":!1,lineOffsetBottom:16,activeStyle:{color:"#23232A",fontSize:"32rpx",fontWeight:"700",fontStyle:"none",textTransform:"none",padding:"0 20rpx"},inactiveStyle:{color:"#9B9A9A",fontSize:"32rpx",fontStyle:"none",textTransform:"none",padding:"0 20rpx"}}),g:r.value.length>0},r.value.length>0?{h:e.f(r.value,((t,a,o)=>{return{a:"13599123-2-"+o,b:e.p({src:e.unref(n.getFullImageUrl)(t.coverImageUrl),width:"336rpx",height:"192rpx",radius:"12rpx","lazy-load":!0}),c:e.t(t.title),d:e.t(t.source),e:e.t((r=t.publishTime,r?r.split(" ")[0]:"")),f:t.id,g:e.o((n=>{return a=t.id,void e.index.navigateTo({url:`/pages_sub/pages_article/detail?id=${a}`});var a}),t.id)};var r}))}:{i:e.p({mode:"list",text:"暂无推荐内容"})})}},o=e._export_sfc(a,[["__scopeId","data-v-13599123"]]);wx.createComponent(o);
