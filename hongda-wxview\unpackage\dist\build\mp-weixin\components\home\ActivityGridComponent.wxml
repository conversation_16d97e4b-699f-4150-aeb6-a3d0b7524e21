<view class="activity-section section-wrapper data-v-36858015"><view class="section-header data-v-36858015"><view class="title-wrapper data-v-36858015"><text class="title-main data-v-36858015">{{a}}</text><text class="title-gradient data-v-36858015">{{b}}</text></view></view><view wx:if="{{c}}" class="loading-state data-v-36858015"><up-loading-icon wx:if="{{d}}" class="data-v-36858015" u-i="36858015-0" bind:__l="__l" u-p="{{d}}"></up-loading-icon><text class="loading-text data-v-36858015">加载中...</text></view><view wx:else class="activity-grid data-v-36858015"><view wx:for="{{e}}" wx:for-item="item" wx:key="i" class="activity-card data-v-36858015" bindtap="{{item.j}}"><view class="image-wrapper data-v-36858015"><image class="activity-image data-v-36858015" src="{{item.a}}" mode="aspectFill" lazy-load="{{true}}"></image><view class="{{['status-tag', 'data-v-36858015', item.c]}}">{{item.b}}</view></view><view class="activity-info data-v-36858015"><text class="activity-title data-v-36858015">{{item.d}}</text><view class="activity-meta-row data-v-36858015"><view class="time-info data-v-36858015"><image class="meta-icon data-v-36858015" src="{{f}}" mode="widthFix"/><text class="time-text data-v-36858015">{{item.e}}</text><text class="weekday-text data-v-36858015">{{item.f}}</text></view><view class="location-info data-v-36858015"><image class="meta-icon data-v-36858015" src="{{g}}" mode="widthFix"/><text class="location-text data-v-36858015">{{item.g}}</text></view></view><view class="remaining-spots-tag data-v-36858015"><text class="spots-label data-v-36858015">剩余名额:</text><text class="spots-count data-v-36858015">{{item.h}}</text></view></view></view></view><view wx:if="{{h}}" class="empty-state data-v-36858015"><up-empty wx:if="{{i}}" class="data-v-36858015" u-i="36858015-1" bind:__l="__l" u-p="{{i}}"></up-empty></view></view>