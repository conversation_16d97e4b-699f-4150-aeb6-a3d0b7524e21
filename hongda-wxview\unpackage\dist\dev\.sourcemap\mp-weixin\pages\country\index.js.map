{"version": 3, "file": "index.js", "sources": ["pages/country/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY291bnRyeS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <view class=\"header-background\" :style=\"{ backgroundImage: `url(${headerBgUrl})` }\">\r\n      <view class=\"page-title-wrapper\">\r\n        <text class=\"page-title\">国别列表</text>\r\n      </view>\r\n\r\n      <view class=\"search-bar-wrapper\" :style=\"dynamicIconStyle\">\r\n        <view class=\"custom-search-bar\">\r\n          <input\r\n              class=\"search-input\"\r\n              type=\"text\"\r\n              placeholder=\"搜索国别名称\"\r\n              v-model=\"searchKeyword\"\r\n              @confirm=\"onSearch\"\r\n              @input=\"onSearchInput\"\r\n          />\r\n          <view class=\"search-icon-wrapper\" @click=\"onSearch\"></view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"continent-tabs-container\">\r\n        <scroll-view\r\n            class=\"continent-tabs\"\r\n            scroll-x=\"true\"\r\n            :show-scrollbar=\"false\"\r\n            @scroll=\"onTabsScroll\"\r\n            :scroll-left=\"tabScrollLeft\"\r\n            scroll-with-animation\r\n        >\r\n          <view\r\n              v-for=\"(tab, index) in continents\"\r\n              :key=\"tab.value\"\r\n              :class=\"['tab-item', { active: activeContinent === tab.value }]\"\r\n              @click=\"onFilterTap(tab.value, index)\"\r\n              :id=\"'tab-' + index\"\r\n              :style=\"{ backgroundImage: activeContinent === tab.value ? `url(${activeTabBgUrl})` : 'none' }\"\r\n          >\r\n            {{ tab.label }}\r\n          </view>\r\n        </scroll-view>\r\n        <view class=\"active-indicator\" :style=\"{ left: indicatorLeft + 'px', opacity: showIndicator ? 1 : 0 }\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view class=\"country-list-wrapper\" scroll-y @scrolltolower=\"loadMore\">\r\n      <view class=\"country-card\" v-for=\"item in countryList\" :key=\"item.id\" @click=\"goToDetail(item.id)\">\r\n        <image class=\"country-image\" :src=\"baseUrl + item.listCoverUrl\" mode=\"aspectFill\" />\r\n\r\n        <view class=\"country-info\">\r\n          <view class=\"info-top\">\r\n            <view class=\"name-line\">\r\n              <text class=\"name-cn\">{{ item.nameCn }}</text>\r\n              <text class=\"name-en\">{{ item.nameEn }}</text>\r\n            </view>\r\n            <text class=\"summary\">{{ item.summary }}</text>\r\n          </view>\r\n          <image class=\"country-flag\" :src=\"baseUrl + item.flagUrl\" mode=\"aspectFit\" />\r\n        </view>\r\n      </view>\r\n\r\n      <view v-if=\"loading\" class=\"status-tip\">\r\n        <uni-load-more status=\"loading\" />\r\n      </view>\r\n      <view v-else-if=\"countryList.length === 0\" class=\"empty-message-container\">\r\n        <text class=\"empty-text\">暂无相关国家信息</text>\r\n      </view>\r\n\r\n      <view class=\"scroll-view-bottom-spacer\"></view>\r\n    </scroll-view>\r\n\r\n    <CustomTabBar :current=\"3\" />\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, nextTick, getCurrentInstance } from 'vue';\r\nimport { onPullDownRefresh, onLoad, onShow, onShareAppMessage } from '@dcloudio/uni-app';\r\nimport { getCountryList } from '@/api/content/country.js';\r\nimport { IMAGE_BASE_URL } from '@/utils/config.js';\r\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue';\r\nimport { debounce } from '@/utils/tools.js';\r\n\r\n// 资源配置\r\nconst headerBgUrl = computed(() => assets.value.bg_country_list_header || '');\r\nconst activeTabBgUrl = computed(() => assets.value.bg_country_list_active_tab || '');\r\n// --- [新增代码] ---\r\n// 创建一个计算属性来生成CSS变量样式\r\nconst dynamicIconStyle = computed(() => {\r\n  // 从缓存中获取搜索图标的URL\r\n  const searchIconUrl = assets.value.icon_common_search;\r\n\r\n  // 如果URL存在，则生成CSS变量\r\n  if (searchIconUrl) {\r\n    return {\r\n      '--search-icon-url': `url(${searchIconUrl})`\r\n    };\r\n  }\r\n  // 如果URL不存在，返回空对象\r\n  return {};\r\n});\r\n\r\n// 基础配置和状态\r\nconst baseUrl = IMAGE_BASE_URL;\r\nconst searchKeyword = ref('');\r\nconst activeContinent = ref('ALL');\r\nconst countryList = ref([]);\r\nconst loading = ref(false);\r\nconst instance = getCurrentInstance();\r\nconst assets = ref(uni.getStorageSync('staticAssets') || {});\r\n\r\n// 大洲Tab数据\r\nconst continents = [\r\n  { label: '全部', value: 'ALL' },\r\n  { label: '亚洲', value: 'ASIA' },\r\n  { label: '欧洲', value: 'EUROPE' },\r\n  { label: '北美洲', value: 'NORTH_AMERICA' },\r\n  { label: '南美洲', value: 'SOUTH_AMERICA' },\r\n  { label: '非洲', value: 'AFRICA' },\r\n  { label: '大洋洲', value: 'OCEANIA' }\r\n];\r\n\r\n// --- 指示器相关状态 ---\r\nconst activeIndex = ref(0);\r\nconst tabsInfo = ref([]); // 存储Tab信息\r\nconst currentScrollLeft = ref(0); // 当前滚动位置\r\nconst showIndicator = ref(false); // 控制指示器显示\r\nconst tabScrollLeft = ref(0); // 控制滚动位置\r\nconst tabsContainerWidth = ref(0); // tabs容器宽度\r\n\r\n/**\r\n * 计算指示器位置\r\n */\r\nconst indicatorLeft = computed(() => {\r\n  if (!tabsInfo.value.length || activeIndex.value >= tabsInfo.value.length) {\r\n    return -999;\r\n  }\r\n\r\n  const activeTab = tabsInfo.value[activeIndex.value];\r\n  if (!activeTab) return -999;\r\n\r\n  // 指示器宽度的一半 (20rpx转px)\r\n  const indicatorHalfWidth = uni.upx2px(20);\r\n\r\n  // Tab中心位置 = Tab左边距 + Tab宽度的一半\r\n  const tabCenterX = activeTab.left + activeTab.width / 2;\r\n\r\n  // 指示器left = Tab中心位置 - 滚动距离 - 指示器宽度的一半\r\n  const left = tabCenterX - currentScrollLeft.value - indicatorHalfWidth;\r\n\r\n  return left;\r\n});\r\n\r\n/**\r\n * Tab滚动事件处理\r\n */\r\nconst onTabsScroll = (event) => {\r\n  currentScrollLeft.value = event.detail.scrollLeft;\r\n};\r\n\r\n/**\r\n * 获取Tab布局信息\r\n */\r\nconst getTabsRect = () => {\r\n  return new Promise((resolve) => {\r\n    const query = uni.createSelectorQuery().in(instance);\r\n\r\n    // 获取tabs容器信息\r\n    query.select('.continent-tabs').boundingClientRect();\r\n    // 获取所有tab项信息\r\n    query.selectAll('.tab-item').boundingClientRect();\r\n\r\n    query.exec((res) => {\r\n      if (res && res[1] && res[1].length) {\r\n        const containerRect = res[0];\r\n        const tabRects = res[1];\r\n\r\n        tabsContainerWidth.value = containerRect ? containerRect.width : 0;\r\n        tabsInfo.value = tabRects;\r\n        showIndicator.value = true;\r\n        resolve(tabRects);\r\n      } else {\r\n        resolve([]);\r\n      }\r\n    });\r\n  });\r\n};\r\n\r\n/**\r\n * 确保选中的Tab在可视区域内\r\n */\r\nconst scrollToActiveTab = () => {\r\n  if (!tabsInfo.value.length || activeIndex.value >= tabsInfo.value.length) {\r\n    return;\r\n  }\r\n\r\n  const activeTab = tabsInfo.value[activeIndex.value];\r\n  const containerWidth = tabsContainerWidth.value;\r\n\r\n  if (!activeTab || !containerWidth) return;\r\n\r\n  // Tab中心位置\r\n  const tabCenter = activeTab.left + activeTab.width / 2;\r\n  // 容器中心位置\r\n  const containerCenter = containerWidth / 2;\r\n  // 需要滚动到的位置\r\n  let scrollTo = tabCenter - containerCenter;\r\n\r\n  // 边界处理\r\n  const maxScroll = Math.max(0, activeTab.dataset?.scrollWidth - containerWidth || 0);\r\n  scrollTo = Math.max(0, Math.min(scrollTo, maxScroll));\r\n\r\n  tabScrollLeft.value = scrollTo;\r\n};\r\n\r\n/**\r\n * Tab点击事件\r\n */\r\nconst onFilterTap = async (continentValue, index) => {\r\n  if (activeContinent.value === continentValue) return;\r\n\r\n  const oldIndex = activeIndex.value;\r\n  activeContinent.value = continentValue;\r\n  activeIndex.value = index;\r\n\r\n  // 如果Tab信息还没加载，先加载\r\n  if (!tabsInfo.value.length) {\r\n    await getTabsRect();\r\n  }\r\n\r\n  // 滚动到选中的Tab\r\n  nextTick(() => {\r\n    scrollToActiveTab();\r\n  });\r\n\r\n  fetchData(true);\r\n};\r\n\r\n/**\r\n * 初始化Tab布局信息\r\n */\r\nconst initTabsLayout = async () => {\r\n  // 等待DOM渲染\r\n  await nextTick();\r\n\r\n  // 多次尝试获取布局信息，确保准确性\r\n  let retryCount = 0;\r\n  const maxRetries = 5;\r\n\r\n  const tryGetLayout = async () => {\r\n    const rects = await getTabsRect();\r\n\r\n    if (rects.length === 0 && retryCount < maxRetries) {\r\n      retryCount++;\r\n      setTimeout(tryGetLayout, 100);\r\n    } else if (rects.length > 0) {\r\n      // 布局信息获取成功，滚动到当前选中的Tab\r\n      scrollToActiveTab();\r\n    }\r\n  };\r\n\r\n  tryGetLayout();\r\n};\r\n\r\n/**\r\n * 获取国别列表数据\r\n */\r\nconst fetchData = async (isRefresh = false) => {\r\n  if (loading.value) return;\r\n  loading.value = true;\r\n  try {\r\n    const res = await getCountryList({\r\n      continent: activeContinent.value,\r\n      keyword: searchKeyword.value\r\n    });\r\n    countryList.value = isRefresh ? res.data : [...countryList.value, ...res.data];\r\n  } catch (error) {\r\n    console.error('获取国别列表失败:', error);\r\n    uni.showToast({ title: '数据加载失败', icon: 'none' });\r\n  } finally {\r\n    loading.value = false;\r\n    if (isRefresh) {\r\n      uni.stopPullDownRefresh();\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * 搜索相关方法\r\n */\r\n// 防抖搜索函数\r\nconst debouncedSearch = debounce(() => {\r\n  fetchData(true);\r\n}, 500);\r\n\r\n// 搜索输入事件处理器\r\nconst onSearchInput = () => {\r\n  if (!searchKeyword.value.trim()) {\r\n    // 如果搜索关键词为空，立即搜索全部数据\r\n    fetchData(true);\r\n  } else {\r\n    // 有输入内容时触发防抖搜索\r\n    debouncedSearch();\r\n  }\r\n};\r\n\r\nconst onSearch = () => {\r\n  fetchData(true);\r\n};\r\n\r\nconst onCancelSearch = () => {\r\n  searchKeyword.value = '';\r\n  fetchData(true);\r\n};\r\n\r\n/**\r\n * 跳转到详情页\r\n */\r\nconst goToDetail = (countryId) => {\r\n  uni.navigateTo({ url: `/pages_sub/pages_country/detail?id=${countryId}` });\r\n};\r\n\r\n/**\r\n * 加载更多\r\n */\r\nconst loadMore = () => {};\r\n\r\n// 页面生命周期\r\nonLoad(() => {\r\n  console.log('国别列表页 onLoad - 页面首次加载');\r\n  uni.showShareMenu({\r\n      menus: ['shareAppMessage', 'shareTimeline']\r\n    });\r\n\r\n  fetchData(true);\r\n  // 延迟初始化Tab布局，确保DOM完全渲染\r\n  setTimeout(initTabsLayout, 200);\r\n});\r\n\r\nonShow(() => {\r\n  uni.hideTabBar();\r\n  // 页面显示时重新计算布局（处理屏幕旋转等情况）\r\n  setTimeout(initTabsLayout, 100);\r\n});\r\n\r\nonPullDownRefresh(() => {\r\n  fetchData(true);\r\n});\r\n\r\nonShareAppMessage(() => {\r\n  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 页面总容器 */\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: transparent;\r\n  font-style: normal;\r\n  text-transform: none;\r\n}\r\n\r\n/* 顶部背景区域 */\r\n.header-background {\r\n  height: 420rpx;\r\n  background-size: cover;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n/* 页面标题容器 */\r\n.page-title-wrapper {\r\n  position: absolute;\r\n  top: 94rpx;\r\n  left: 0;\r\n  width: 750rpx;\r\n  height: 88rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.page-title {\r\n  font-size: 32rpx;\r\n  font-family: 'Alibaba PuHuiTi Medium', 'Alibaba PuHuiTi', sans-serif;\r\n  line-height: 44rpx;\r\n  color: #FFFFFF;\r\n}\r\n\r\n/* 搜索栏包裹容器 */\r\n.search-bar-wrapper {\r\n  position: absolute;\r\n  top: 182rpx;\r\n  left: 0;\r\n  width: 750rpx;\r\n  height: 88rpx;\r\n  box-sizing: border-box;\r\n  padding: 14rpx 16rpx 14rpx 32rpx;\r\n}\r\n\r\n/* 自定义搜索框主体 */\r\n.custom-search-bar {\r\n  height: 100%;\r\n  border-radius: 100rpx; /* 圆角 */\r\n  background-color: #FFFFFF; /* 背景色 */\r\n  display: flex; /* 开启 Flexbox 布局 */\r\n  align-items: center; /* 垂直居中对齐 */\r\n  padding: 0 16rpx;\r\n}\r\n\r\n/* 输入框 */\r\n.search-input {\r\n  flex: 1; /* 占据所有可用空间 */\r\n  height: 100%;\r\n  font-size: 28rpx;\r\n  line-height: 44rpx;\r\n  color: #23232a;\r\n  padding-left: 20rpx; /* 左侧内边距，为了视觉效果 */\r\n}\r\n\r\n/* 输入框占位符样式 */\r\n.search-input::placeholder {\r\n  color: #9B9A9A;\r\n  line-height: 44rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n/* 搜索图标容器 */\r\n.search-icon-wrapper {\r\n  flex-shrink: 0; /* 不会被压缩 */\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  background-image: var(--search-icon-url);\r\n  background-size: contain;\r\n  background-repeat: no-repeat;\r\n  background-position: center;\r\n  margin-left: 12rpx; /* 与输入框的间距 */\r\n}\r\n\r\n\r\n/* Tab容器和指示器 */\r\n.continent-tabs-container {\r\n  position: absolute;\r\n  top: 320rpx;\r\n  transform: translateY(-50%);\r\n  width: 100%;\r\n  padding: 0 30rpx;\r\n  box-sizing: border-box;\r\n  margin-top: 10rpx;\r\n}\r\n\r\n.continent-tabs {\r\n  white-space: nowrap;\r\n}\r\n\r\n.continent-tabs :deep(::-webkit-scrollbar) {\r\n  display: none;\r\n  width: 0 !important;\r\n  height: 0 !important;\r\n  -webkit-appearance: none;\r\n  background: transparent;\r\n  color: transparent;\r\n}\r\n\r\n.tab-item {\r\n  display: inline-block;\r\n  padding: 12rpx 30rpx;\r\n  font-size: 28rpx;\r\n  color: #23232A;\r\n  border-radius: 30rpx 30rpx 30rpx 30rpx;\r\n  background-color: #FFFFFF;\r\n  transition: all 0.3s;\r\n  background-size: cover;\r\n  background-position: center;\r\n  margin-right: 24rpx;\r\n\r\n  &:last-child {\r\n    margin-right: 0;\r\n  }\r\n\r\n  &.active {\r\n    color: #23232A;\r\n  }\r\n}\r\n\r\n/* 指示器样式优化 */\r\n.active-indicator {\r\n  position: absolute;\r\n  bottom: -32rpx;\r\n  width: 0;\r\n  height: 0;\r\n  border-left: 20rpx solid transparent;\r\n  border-right: 20rpx solid transparent;\r\n  border-bottom: 25rpx solid #fff;\r\n  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;\r\n  z-index: 10;\r\n  opacity: 0; /* 初始隐藏 */\r\n}\r\n\r\n/* 国别列表包裹容器 */\r\n.country-list-wrapper {\r\n  flex: 1;\r\n  height: 0;\r\n  background-color: #ffffff;\r\n  position: relative;\r\n  z-index: 15;\r\n  border-radius: 30rpx 30rpx 0 0;\r\n  margin-top: -32rpx;\r\n  box-sizing: border-box;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 国别卡片 */\r\n.country-card {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 272rpx;\r\n  padding: 0 30rpx;\r\n  background-color: #fff;\r\n  box-sizing: border-box;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n/* 卡片左侧封面图 */\r\n.country-image {\r\n  flex-shrink: 0;\r\n  width: 336rpx;\r\n  height: 192rpx;\r\n  border-radius: 12rpx;\r\n  margin-right: 24rpx;\r\n}\r\n\r\n/* 卡片右侧信息布局 */\r\n.country-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n  height: 192rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n/* 顶部信息块 */\r\n.info-top {\r\n  flex: 1; /* [核心修改] 让其占据所有剩余垂直空间 */\r\n  min-height: 0; /* [核心修改] 允许其在flex布局中正确收缩 */\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 国名行 */\r\n.name-line {\r\n/*  display: flex;\r\n  align-items: baseline;*/\r\n  margin-bottom: 8rpx;\r\n\r\n  /* [核心修改] 添加以下属性以实现最多2行文本截断 */\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n\r\n  .name-cn {\r\n    font-size: 28rpx;\r\n    font-family: 'Alibaba PuHuiTi Medium', 'Alibaba PuHuiTi', sans-serif;\r\n    color: #23232A;\r\n  }\r\n  .name-en {\r\n    font-size: 22rpx;\r\n    margin-left: 12rpx;\r\n    color: #9B9A9A;\r\n  }\r\n}\r\n\r\n/* 简介 */\r\n.summary {\r\n  font-size: 24rpx;\r\n  color: #23232A;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 1; /* [核心修改] 确保简介最多只占一行 */\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  margin-top: 12rpx; /* [优化] 稍微减少与标题的间距 */\r\n}\r\n\r\n/* 国旗 */\r\n.country-flag {\r\n  width: 60rpx;\r\n  height: 40rpx;\r\n  border-radius: 4rpx;\r\n  border: 1rpx solid #eee;\r\n  align-self: flex-start;\r\n}\r\n\r\n/* 状态提示 */\r\n.status-tip, .empty-message-container {\r\n  padding: 80rpx 0;\r\n  text-align: center;\r\n}\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n.scroll-view-bottom-spacer {\r\n  height: 180rpx;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/country/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["computed", "IMAGE_BASE_URL", "ref", "getCurrentInstance", "uni", "nextTick", "getCountryList", "debounce", "onLoad", "onShow", "onPullDownRefresh", "onShareAppMessage"], "mappings": ";;;;;;;;;;;;;AAgFA,MAAA,eAAA,MAAA;;;;AAIA,UAAA,cAAAA,cAAA,SAAA,MAAA,OAAA,MAAA,0BAAA,EAAA;AACA,UAAA,iBAAAA,cAAA,SAAA,MAAA,OAAA,MAAA,8BAAA,EAAA;AAGA,UAAA,mBAAAA,cAAA,SAAA,MAAA;AAEA,YAAA,gBAAA,OAAA,MAAA;AAGA,UAAA,eAAA;AACA,eAAA;AAAA,UACA,qBAAA,OAAA,aAAA;AAAA,QACA;AAAA,MACA;AAEA,aAAA;IACA,CAAA;AAGA,UAAA,UAAAC,aAAAA;AACA,UAAA,gBAAAC,cAAAA,IAAA,EAAA;AACA,UAAA,kBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,cAAAA,cAAAA,IAAA,CAAA,CAAA;AACA,UAAA,UAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,WAAAC,cAAA,mBAAA;AACA,UAAA,SAAAD,cAAA,IAAAE,cAAA,MAAA,eAAA,cAAA,KAAA,CAAA,CAAA;AAGA,UAAA,aAAA;AAAA,MACA,EAAA,OAAA,MAAA,OAAA,MAAA;AAAA,MACA,EAAA,OAAA,MAAA,OAAA,OAAA;AAAA,MACA,EAAA,OAAA,MAAA,OAAA,SAAA;AAAA,MACA,EAAA,OAAA,OAAA,OAAA,gBAAA;AAAA,MACA,EAAA,OAAA,OAAA,OAAA,gBAAA;AAAA,MACA,EAAA,OAAA,MAAA,OAAA,SAAA;AAAA,MACA,EAAA,OAAA,OAAA,OAAA,UAAA;AAAA,IACA;AAGA,UAAA,cAAAF,cAAAA,IAAA,CAAA;AACA,UAAA,WAAAA,cAAAA,IAAA,CAAA,CAAA;AACA,UAAA,oBAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,gBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,gBAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,qBAAAA,cAAAA,IAAA,CAAA;AAKA,UAAA,gBAAAF,cAAA,SAAA,MAAA;AACA,UAAA,CAAA,SAAA,MAAA,UAAA,YAAA,SAAA,SAAA,MAAA,QAAA;AACA,eAAA;AAAA,MACA;AAEA,YAAA,YAAA,SAAA,MAAA,YAAA,KAAA;AACA,UAAA,CAAA;AAAA,eAAA;AAGA,YAAA,qBAAAI,cAAAA,MAAA,OAAA,EAAA;AAGA,YAAA,aAAA,UAAA,OAAA,UAAA,QAAA;AAGA,YAAA,OAAA,aAAA,kBAAA,QAAA;AAEA,aAAA;AAAA,IACA,CAAA;AAKA,UAAA,eAAA,CAAA,UAAA;AACA,wBAAA,QAAA,MAAA,OAAA;AAAA,IACA;AAKA,UAAA,cAAA,MAAA;AACA,aAAA,IAAA,QAAA,CAAA,YAAA;AACA,cAAA,QAAAA,cAAAA,MAAA,oBAAA,EAAA,GAAA,QAAA;AAGA,cAAA,OAAA,iBAAA,EAAA,mBAAA;AAEA,cAAA,UAAA,WAAA,EAAA,mBAAA;AAEA,cAAA,KAAA,CAAA,QAAA;AACA,cAAA,OAAA,IAAA,CAAA,KAAA,IAAA,CAAA,EAAA,QAAA;AACA,kBAAA,gBAAA,IAAA,CAAA;AACA,kBAAA,WAAA,IAAA,CAAA;AAEA,+BAAA,QAAA,gBAAA,cAAA,QAAA;AACA,qBAAA,QAAA;AACA,0BAAA,QAAA;AACA,oBAAA,QAAA;AAAA,UACA,OAAA;AACA,oBAAA,CAAA,CAAA;AAAA,UACA;AAAA,QACA,CAAA;AAAA,MACA,CAAA;AAAA,IACA;AAKA,UAAA,oBAAA,MAAA;;AACA,UAAA,CAAA,SAAA,MAAA,UAAA,YAAA,SAAA,SAAA,MAAA,QAAA;AACA;AAAA,MACA;AAEA,YAAA,YAAA,SAAA,MAAA,YAAA,KAAA;AACA,YAAA,iBAAA,mBAAA;AAEA,UAAA,CAAA,aAAA,CAAA;AAAA;AAGA,YAAA,YAAA,UAAA,OAAA,UAAA,QAAA;AAEA,YAAA,kBAAA,iBAAA;AAEA,UAAA,WAAA,YAAA;AAGA,YAAA,YAAA,KAAA,IAAA,KAAA,eAAA,YAAA,mBAAA,eAAA,kBAAA,CAAA;AACA,iBAAA,KAAA,IAAA,GAAA,KAAA,IAAA,UAAA,SAAA,CAAA;AAEA,oBAAA,QAAA;AAAA,IACA;AAKA,UAAA,cAAA,OAAA,gBAAA,UAAA;AACA,UAAA,gBAAA,UAAA;AAAA;AAEA,kBAAA;AACA,sBAAA,QAAA;AACA,kBAAA,QAAA;AAGA,UAAA,CAAA,SAAA,MAAA,QAAA;AACA,cAAA,YAAA;AAAA,MACA;AAGAC,oBAAAA,WAAA,MAAA;AACA;MACA,CAAA;AAEA,gBAAA,IAAA;AAAA,IACA;AAKA,UAAA,iBAAA,YAAA;AAEA,YAAAA,cAAA,WAAA;AAGA,UAAA,aAAA;AACA,YAAA,aAAA;AAEA,YAAA,eAAA,YAAA;AACA,cAAA,QAAA,MAAA;AAEA,YAAA,MAAA,WAAA,KAAA,aAAA,YAAA;AACA;AACA,qBAAA,cAAA,GAAA;AAAA,QACA,WAAA,MAAA,SAAA,GAAA;AAEA;QACA;AAAA,MACA;AAEA;IACA;AAKA,UAAA,YAAA,OAAA,YAAA,UAAA;AACA,UAAA,QAAA;AAAA;AACA,cAAA,QAAA;AACA,UAAA;AACA,cAAA,MAAA,MAAAC,mCAAA;AAAA,UACA,WAAA,gBAAA;AAAA,UACA,SAAA,cAAA;AAAA,QACA,CAAA;AACA,oBAAA,QAAA,YAAA,IAAA,OAAA,CAAA,GAAA,YAAA,OAAA,GAAA,IAAA,IAAA;AAAA,MACA,SAAA,OAAA;AACAF,sBAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,KAAA;AACAA,sBAAA,MAAA,UAAA,EAAA,OAAA,UAAA,MAAA,OAAA,CAAA;AAAA,MACA,UAAA;AACA,gBAAA,QAAA;AACA,YAAA,WAAA;AACAA,wBAAA,MAAA,oBAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA;AAMA,UAAA,kBAAAG,YAAA,SAAA,MAAA;AACA,gBAAA,IAAA;AAAA,IACA,GAAA,GAAA;AAGA,UAAA,gBAAA,MAAA;AACA,UAAA,CAAA,cAAA,MAAA,QAAA;AAEA,kBAAA,IAAA;AAAA,MACA,OAAA;AAEA;MACA;AAAA,IACA;AAEA,UAAA,WAAA,MAAA;AACA,gBAAA,IAAA;AAAA,IACA;AAUA,UAAA,aAAA,CAAA,cAAA;AACAH,oBAAA,MAAA,WAAA,EAAA,KAAA,sCAAA,SAAA,GAAA,CAAA;AAAA,IACA;AAKA,UAAA,WAAA,MAAA;AAAA,IAAA;AAGAI,kBAAAA,OAAA,MAAA;AACAJ,oBAAAA,MAAA,MAAA,OAAA,kCAAA,uBAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAEA,gBAAA,IAAA;AAEA,iBAAA,gBAAA,GAAA;AAAA,IACA,CAAA;AAEAK,kBAAAA,OAAA,MAAA;AACAL,oBAAA,MAAA,WAAA;AAEA,iBAAA,gBAAA,GAAA;AAAA,IACA,CAAA;AAEAM,kBAAAA,kBAAA,MAAA;AACA,gBAAA,IAAA;AAAA,IACA,CAAA;AAEAC,kBAAAA,kBAAA,MAAA;AACA,aAAA;IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9VA,GAAG,WAAW,eAAe;"}