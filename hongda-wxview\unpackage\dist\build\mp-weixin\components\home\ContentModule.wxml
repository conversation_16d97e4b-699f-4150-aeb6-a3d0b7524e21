<view class="content-module-container data-v-a9d48ff4"><view class="sub-tabs-container data-v-a9d48ff4"><scroll-view class="sub-tabs-wrapper data-v-a9d48ff4" scroll-x="true" show-scrollbar="{{false}}"><view wx:for="{{a}}" wx:for-item="tag" wx:key="b" class="{{['sub-tab-item', 'data-v-a9d48ff4', tag.c && 'active']}}" bindtap="{{tag.d}}">{{tag.a}}</view></scroll-view><view class="divider data-v-a9d48ff4"></view></view><view class="article-list-container data-v-a9d48ff4"><view wx:if="{{b}}" class="loading-state data-v-a9d48ff4"><u-loading-icon wx:if="{{c}}" class="data-v-a9d48ff4" u-i="a9d48ff4-0" bind:__l="__l" u-p="{{c}}"></u-loading-icon></view><view wx:elif="{{d}}" class="article-list data-v-a9d48ff4"><view wx:for="{{e}}" wx:for-item="article" wx:key="c" class="article-item data-v-a9d48ff4" bindtap="{{article.d}}"><view class="dot data-v-a9d48ff4"></view><text class="article-title data-v-a9d48ff4">{{article.a}}</text><u-icon wx:if="{{f}}" class="data-v-a9d48ff4" u-i="{{article.b}}" bind:__l="__l" u-p="{{f}}"></u-icon></view></view><view wx:else class="empty-state data-v-a9d48ff4"><text class="data-v-a9d48ff4">暂无数据</text></view></view></view>