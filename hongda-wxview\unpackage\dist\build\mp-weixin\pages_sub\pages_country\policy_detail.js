"use strict";const e=require("../../common/vendor.js"),t=require("../../api/content/countryPolicy.js"),n=require("../../utils/config.js"),o=require("../../utils/mpHtmlStyles.js"),a=require("../../utils/tools.js");if(!Array){(e.resolveComponent("DetailSkeleton")+e.resolveComponent("uni-icons")+e.resolveComponent("u-empty"))()}Math||((()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js")+l+(()=>"../../uni_modules/uview-plus/components/u-empty/u-empty.js"))();const l=()=>"../../uni_modules/mp-html/components/mp-html/mp-html.js",u={__name:"policy_detail",setup(l){const u=e.index.upx2px(10),i=e.ref(0),s=e.ref(0),r=e.ref(0),c=()=>{e.index.navigateBack({delta:1})},p=n.IMAGE_BASE_URL,m=e.ref(null),v=e.ref(!0);return e.onLoad((async n=>{if(console.log("政策详情页 onLoad - 页面首次加载"),e.index.showShareMenu({menus:["shareAppMessage","shareTimeline"]}),(()=>{try{const t=e.index.getMenuButtonBoundingClientRect();i.value=t.top,s.value=t.height,r.value=t.bottom+u}catch(t){const n=e.index.getSystemInfoSync();i.value=n.statusBarHeight||20,s.value=44,r.value=i.value+s.value+u}})(),!n.id)return e.index.showToast({title:"参数错误",icon:"none"}),void(v.value=!1);try{const e=await t.getCountryPolicyArticle(n.id);if(200!==e.code||!e.data)throw new Error(e.msg||"文章不存在或已被删除");e.data.content=a.formatRichText(e.data.content),m.value=e.data}catch(o){console.error("获取国别政策文章失败:",o),m.value=null}finally{v.value=!1}})),e.onShareAppMessage((()=>({}))),(t,n)=>e.e({a:v.value},v.value?{}:m.value?{c:i.value+"px",d:e.p({type:"left",color:"#000000",size:"22"}),e:e.o(c),f:s.value+"px",g:e.unref(u)+"px",h:r.value+"px",i:e.t(m.value.title),j:e.t(m.value.createTime),k:e.p({content:m.value.content,domain:e.unref(p),"tag-style":e.unref(o.tagStyle),"container-style":e.unref(o.containerStyle),"preview-img":!0,"lazy-load":!0}),l:r.value+"px"}:{m:e.p({mode:"list",text:"文章不存在或已被删除"})},{b:m.value})}},i=e._export_sfc(u,[["__scopeId","data-v-3c14c464"]]);u.__runtimeHooks=2,wx.createPage(i);
