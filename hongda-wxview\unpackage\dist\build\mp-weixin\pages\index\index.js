"use strict";const e=require("../../common/vendor.js"),o=require("../../api/platform/ad.js");Math||(a+n+t+s+r+l+u+v+c+i)();const a=()=>"../../components/home/<USER>",n=()=>"../../components/home/<USER>",t=()=>"../../components/home/<USER>",s=()=>"../../components/home/<USER>",l=()=>"../../components/home/<USER>",u=()=>"../../components/home/<USER>",r=()=>"../../components/home/<USER>",i=()=>"../../components/common/PopupAdComponent.js",c=()=>"../../components/layout/CustomTabBar.js",v=()=>"../../components/common/NoMoreDivider.js",m={__name:"index",setup(a){const n=e.ref(88),t=e=>{e&&e>0&&(n.value=e)},s=e.ref(null),l=e.ref(!1),u=e.ref(""),r=e.ref(!1),i=e.ref([]),c=e.ref(0),v=e.ref(null);let m=!1;const p=()=>{s.value&&s.value.loadMore&&s.value.loadMore()},d=e=>{l.value=!!e},h=()=>{c.value<i.value.length?(v.value=i.value[c.value],r.value=!0):(r.value=!1,v.value=null)},g=()=>{r.value=!1,c.value++,c.value>=i.value.length?m=!0:setTimeout((()=>{h()}),300)},f=()=>{e.index.navigateTo({url:"/pages_sub/pages_profile/contact"})};return e.onLoad((()=>{console.log("首页 onLoad - 页面首次加载"),e.index.showShareMenu({menus:["shareAppMessage","shareTimeline"]})})),e.onShow((()=>{console.log("首页 onShow - 页面显示"),e.index.hideTabBar(),(async()=>{try{if(m)return;const e=await o.getAdListByPositionApi("SPLASH_SCREEN",{pageSize:10});e&&e.data&&e.data.length>0?(i.value=e.data,c.value=0,h()):m=!0}catch(e){console.error("获取弹窗广告失败:",e.message||e),m=!0}})();const a=e.index.getStorageSync("staticAssets");u.value=(null==a?void 0:a.fab_customer_service_icon)||""})),e.onShareAppMessage((()=>({}))),(o,a)=>e.e({a:e.o(t),b:e.sr(s,"e4a08dff-6",{k:"activityRef"}),c:e.o(d),d:l.value},(l.value,{}),{e:n.value+"px",f:e.o(p),g:e.p({current:0}),h:u.value,i:e.o(f),j:r.value&&v.value},r.value&&v.value?{k:e.o(g),l:e.p({show:r.value,"ad-data":v.value})}:{})}},p=e._export_sfc(m,[["__scopeId","data-v-e4a08dff"]]);m.__runtimeHooks=2,wx.createPage(p);
