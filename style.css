/* style.css */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fdfdfd;
    max-width: 800px;
    margin: 20px auto;
    padding: 0 20px;
}
h1, h2, h3, h4, h5, h6 {
    color: #2c3e50;
    border-bottom: 1px solid #eaecef;
    padding-bottom: .3em;
}
a {
    color: #0366d6;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
code {
    background-color: #f6f8fa;
    padding: .2em .4em;
    margin: 0;
    font-size: 85%;
    border-radius: 3px;
}
pre {
    background-color: #f6f8fa;
    padding: 16px;
    overflow: auto;
    border-radius: 3px;
}
table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
}
th, td {
    border: 1px solid #dfe2e5;
    padding: 8px 12px;
}
th {
    background-color: #f6f8fa;
    font-weight: bold;
}
img {
    max-width: 100%;
    height: auto;
}