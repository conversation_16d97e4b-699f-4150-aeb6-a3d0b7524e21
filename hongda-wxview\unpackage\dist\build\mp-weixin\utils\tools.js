"use strict";require("./config.js"),exports.calculateRemainingSpots=function(e,t){if(!e||e<=0)return"不限";const i=e-(t||0);return i>0?i:0},exports.debounce=function(e,t){let i;return function(...r){clearTimeout(i),i=setTimeout((()=>{clearTimeout(i),e(...r)}),t)}},exports.formatRichText=function(e){return e?e.replace(/<img[^>]*>/gi,(e=>{let t=e.replace(/width="[^"]*"/gi,"").replace(/height="[^"]-]*"/gi,"").replace(/style="[^"]*"/gi,"");const i=t.indexOf(">");if(i>-1){const e=' style="max-width:100%;height:auto;display:block;" ';t=t.slice(0,i)+e+t.slice(i)}return t})):""};
