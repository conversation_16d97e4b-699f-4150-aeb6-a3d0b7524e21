{"version": 3, "file": "index.js", "sources": ["pages/article/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYXJ0aWNsZS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n\r\n    <view class=\"header-section\" :style=\"headerStyle\">\r\n      <view class=\"custom-nav-bar\">\r\n        <view class=\"status-bar\"></view>\r\n        <view class=\"nav-title\">资讯列表</view>\r\n        <view class=\"filter-bar\">\r\n          <view class=\"sort-button\" :class=\"{ 'is-active': activePanel === 'sort' }\" @click=\"togglePanel('sort')\">\r\n            {{ sortButtonText }}\r\n            <view class=\"custom-arrow\" :class=\"{ 'rotate-180': activePanel === 'sort' }\"></view>\r\n          </view>\r\n          <view class=\"filter-button\" :class=\"{ 'is-active': activePanel === 'filter' || isFilterActive }\"\r\n                @click=\"togglePanel('filter')\">\r\n            {{ filterButtonText }}\r\n            <view class=\"custom-arrow\" :class=\"{ 'rotate-180': activePanel === 'filter' }\"></view>\r\n            <view class=\"active-dot\" v-if=\"isFilterActive\"></view>\r\n          </view>\r\n          <view class=\"search-box\">\r\n            <uni-easyinput\r\n                class=\"search-input\"\r\n                suffix-icon=\"search\"\r\n                v-model=\"searchKeyword\"\r\n                placeholder=\"搜索资讯\"\r\n                :clearable=\"true\"\r\n                @confirm=\"handleSearch\"\r\n                @clear=\"handleClear\"\r\n                @input=\"onSearchInput\"\r\n                @iconClick=\"handleSearch\"  ></uni-easyinput>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"content-section\">\r\n      <view class=\"tabs-container\">\r\n        <u-tabs\r\n            v-if=\"tagList.length > 1\"\r\n            :list=\"tagList\"\r\n            :current=\"currentTabIndex\"\r\n            keyName=\"name\"\r\n            @change=\"handleTabChange\"\r\n            lineColor=\"#023F98\"\r\n            :lineHeight=\"3\"\r\n            :lineWidth=\"20\"\r\n            :show-scrollbar=\"false\"\r\n            :activeStyle=\"{\r\n              color: '#023F98',\r\n              fontSize: '32rpx',\r\n            }\"\r\n            :inactiveStyle=\"{\r\n              color: '#9B9A9A',\r\n              fontSize: '32rpx',\r\n            }\"\r\n        ></u-tabs>\r\n      </view>\r\n\r\n      <scroll-view scroll-y class=\"article-list-scroll\" @scrolltolower=\"loadMore\" enable-flex>\r\n        <view class=\"article-list\">\r\n          <view class=\"article-card\" v-for=\"item in articleList\" :key=\"item.id\" @click=\"gotoDetail(item.id)\">\r\n            <view class=\"card-cover\">\r\n              <u-image\r\n                  :src=\"getFullImageUrl(item.coverImageUrl)\"\r\n                  width=\"100%\"\r\n                  height=\"190rpx\"\r\n                  radius=\"16\"\r\n                  :fade=\"true\"\r\n                  :lazy-load=\"true\"\r\n                  @error=\"onImageError\"\r\n                  @load=\"onImageLoad\"\r\n              >\r\n                <template v-slot:loading>\r\n                  <view class=\"image-loading\">\r\n                    <u-loading-icon color=\"#667eea\" size=\"20\"></u-loading-icon>\r\n                    <text class=\"loading-text\">加载中...</text>\r\n                  </view>\r\n                </template>\r\n                <template v-slot:error>\r\n                  <view class=\"image-error\">\r\n                    <u-icon name=\"photo-off\" color=\"#9ca3af\" size=\"24\"></u-icon>\r\n                    <text class=\"error-text\">图片加载失败</text>\r\n                  </view>\r\n                </template>\r\n              </u-image>\r\n            </view>\r\n            <view class=\"card-content\">\r\n              <text class=\"card-title\">{{ item.title }}</text>\r\n              <view class=\"card-tags\" v-if=\"item.parsedTags && item.parsedTags.length > 0\">\r\n                <text\r\n                    v-for=\"tag in item.parsedTags\"\r\n                    :key=\"tag.id\"\r\n                    :class=\"['tag-item', getTagColorClass(tag)]\"\r\n                >\r\n                  {{ tag.name }}\r\n                </text>\r\n              </view>\r\n              <view class=\"card-meta\">\r\n                <text class=\"meta-source\">{{ item.source }}</text>\r\n                <text class=\"meta-date\">{{ formatDate(item.publishTime) }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <u-empty v-if=\"loadStatus === 'nomore' && articleList.length === 0\"\r\n                 mode=\"news\" text=\"暂无资讯\"\r\n                 marginTop=\"100\"></u-empty>\r\n        <NoMoreDivider v-else-if=\"loadStatus === 'nomore' && articleList.length > 0\" />\r\n        <u-loadmore v-else :status=\"loadStatus\" line=\"true\"/>\r\n      </scroll-view>\r\n    </view>\r\n\r\n    <view class=\"dropdown-wrapper\" v-if=\"activePanel\">\r\n      <view class=\"dropdown-mask\" @click=\"closePanel\"></view>\r\n      <view class=\"dropdown-panel\" :class=\"{ show: activePanel }\">\r\n        <view v-if=\"activePanel === 'sort'\" class=\"filter-panel\">\r\n          <scroll-view scroll-y class=\"filter-scroll\">\r\n            <view class=\"panel-section\">\r\n              <text class=\"section-title\">排序方式</text>\r\n              <view class=\"panel-options\">\r\n                <view\r\n                    class=\"option-btn\"\r\n                    v-for=\"sort in sortActions\"\r\n                    :key=\"sort.value\"\r\n                    :class=\"{ 'active': tempFilters.sort === sort.value }\"\r\n                    @click=\"tempFilters.sort = sort.value\"\r\n                >{{ sort.name }}\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </scroll-view>\r\n          <view class=\"panel-footer\">\r\n            <button class=\"footer-btn reset\" @click=\"handleSortReset\">重置</button>\r\n            <button class=\"footer-btn confirm\" @click=\"handleSortConfirm\">确定</button>\r\n          </view>\r\n        </view>\r\n\r\n        <view v-if=\"activePanel === 'filter'\" class=\"filter-panel\">\r\n          <scroll-view scroll-y class=\"filter-scroll\">\r\n            <view class=\"panel-section\">\r\n              <text class=\"section-title\">发布时间</text>\r\n              <view class=\"panel-options\">\r\n                <view\r\n                    class=\"option-btn\"\r\n                    v-for=\"time in timeActions\"\r\n                    :key=\"time.value\"\r\n                    :class=\"{ 'active': tempFilters.time === time.value }\"\r\n                    @click=\"tempFilters.time = time.value\"\r\n                >{{ time.name }}\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </scroll-view>\r\n          <view class=\"panel-footer\">\r\n            <button class=\"footer-btn reset\" @click=\"handleFilterReset\">重置</button>\r\n            <button class=\"footer-btn confirm\" @click=\"handleFilterConfirm\">确定</button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <CustomTabBar :current=\"1\"/>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\n// Script 部分无需任何修改，保持原样即可\r\nimport { ref, reactive, onMounted, computed } from 'vue';\r\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue';\r\nimport NoMoreDivider from '@/components/common/NoMoreDivider.vue';\r\nimport { onPullDownRefresh, onReachBottom, onShow, onLoad, onShareAppMessage } from '@dcloudio/uni-app';\r\nimport { getArticleList } from '@/api/content/article.js';\r\nimport { listAllTag } from '@/api/content/tag.js';\r\nimport { getFullImageUrl } from '@/utils/image.js';\r\nimport { listAllRegion } from \"@/api/content/region\";\r\nimport { debounce } from '@/utils/tools.js';\r\n\r\n// --- 响应式状态 ---\r\nconst articleList = ref([]);\r\nconst tagList = ref([{ id: null, name: '全部' }]);\r\nconst currentTabIndex = ref(0);\r\nconst loadStatus = ref('loadmore');\r\nconst searchKeyword = ref('');\r\nconst activePanel = ref(null);\r\nconst assets = ref({});\r\n\r\n// 在 <script setup> 的任意位置添加这个方法\r\n/**\r\n * 根据标签ID计算出一个固定的颜色类名\r\n * @param {object} tag - 标签对象，必须包含id属性\r\n * @returns {string} - 返回如 'tag-color-0' 的类名\r\n */\r\nconst getTagColorClass = (tag) => {\r\n  if (!tag || typeof tag.id !== 'number') {\r\n    // 如果没有ID或ID不是数字，返回一个默认颜色\r\n    return 'tag-color-1';\r\n  }\r\n  // 使用标签ID对4取模，得到 0, 1, 2, 3\r\n  const colorIndex = tag.id % 4;\r\n  return `tag-color-${colorIndex}`;\r\n};\r\n\r\n// --- 查询参数 ---\r\nconst queryParams = reactive({\r\n  region: null,\r\n  pageNum: 1,\r\n  pageSize: 10,\r\n  title: null,\r\n  tagIds: null,\r\n  orderByColumn: 'sort_order',\r\n  isAsc: 'asc',\r\n  status: '1',\r\n  'params[beginPublishTime]': null,\r\n  'params[endPublishTime]': null,\r\n});\r\n\r\n// --- 筛选状态管理 ---\r\nconst tempFilters = reactive({\r\n  sort: 'default',\r\n  region: 'all',\r\n  time: 'all',\r\n});\r\n\r\nconst appliedFilters = reactive({\r\n  sort: 'default',\r\n  region: 'all',\r\n  time: 'all',\r\n});\r\n\r\n// --- 筛选选项静态配置 ---\r\nconst sortActions = [\r\n  { name: '综合排序', value: 'default' },\r\n  { name: '最新发布', value: 'publish_time' },\r\n  { name: '最多人看', value: 'view_count' },\r\n];\r\n\r\nconst regionActions = ref([]);\r\nconst searchIconUrl = computed(() => assets.value.icon_search_input || '');\r\n\r\nconst timeActions = [\r\n  { name: '全部', value: 'all' }, // ✨ 修改完成\r\n  { name: '1周内', value: 'week' },\r\n  { name: '1月内', value: 'month' },\r\n  { name: '1年内', value: 'year' },\r\n];\r\n\r\n// --- Computed Properties for UI ---\r\nconst headerStyle = computed(() => {\r\n  const imageUrl = assets.value.bg_article_header;\r\n  if (imageUrl) {\r\n    return {\r\n      backgroundImage: `url('${imageUrl}')`\r\n    };\r\n  }\r\n  return {};\r\n});\r\n\r\nconst sortButtonText = computed(() => {\r\n  const sortItem = sortActions.find(item => item.value === appliedFilters.sort);\r\n  return sortItem ? sortItem.name : '综合排序';\r\n});\r\n\r\nconst isFilterActive = computed(() => appliedFilters.time !== 'all');\r\n\r\nconst filterButtonText = computed(() => {\r\n  const timeItem = timeActions.find(item => item.value === appliedFilters.time);\r\n  return timeItem ? timeItem.name : '发布时间';\r\n});\r\n\r\n// --- Methods ---\r\nconst togglePanel = (panelName) => {\r\n  if (activePanel.value === panelName) {\r\n    activePanel.value = null;\r\n  } else {\r\n    tempFilters.sort = appliedFilters.sort;\r\n    tempFilters.region = appliedFilters.region;\r\n    tempFilters.time = appliedFilters.time;\r\n    activePanel.value = panelName;\r\n  }\r\n};\r\n\r\nconst closePanel = () => {\r\n  activePanel.value = null;\r\n};\r\n\r\n// ✨ 新增：处理排序面板的“确定”按钮点击\r\nconst handleSortConfirm = () => {\r\n  // 1. 将临时选中的排序应用到实际的筛选参数中\r\n  appliedFilters.sort = tempFilters.sort;\r\n\r\n  // 2. 根据应用的排序值，更新API查询参数\r\n  const sortValue = appliedFilters.sort;\r\n  queryParams.orderByColumn = sortValue === 'default' ? 'sort_order' : sortValue;\r\n  queryParams.isAsc = sortValue === 'default' ? 'asc' : 'desc';\r\n\r\n  // 3. 关闭面板并重新加载列表\r\n  closePanel();\r\n  loadArticles(true);\r\n};\r\n\r\n// ✨ 新增：处理排序面板的“重置”按钮点击\r\nconst handleSortReset = () => {\r\n  // 1. 将临时和应用的排序都重置为默认值\r\n  tempFilters.sort = 'default';\r\n  appliedFilters.sort = 'default';\r\n\r\n  // 2. 更新API查询参数为默认排序\r\n  queryParams.orderByColumn = 'sort_order';\r\n  queryParams.isAsc = 'asc';\r\n\r\n  // 3. 关闭面板并重新加载列表\r\n  closePanel();\r\n  loadArticles(true);\r\n};\r\n\r\nconst handleFilterReset = () => {\r\n  appliedFilters.region = 'all';\r\n  appliedFilters.time = 'all';\r\n  tempFilters.region = 'all';\r\n  tempFilters.time = 'all';\r\n  updateFilterParams();\r\n  closePanel();\r\n  loadArticles(true);\r\n};\r\n\r\nconst handleFilterConfirm = () => {\r\n  appliedFilters.region = tempFilters.region;\r\n  appliedFilters.time = tempFilters.time;\r\n  updateFilterParams();\r\n  closePanel();\r\n  loadArticles(true);\r\n};\r\n\r\nconst updateFilterParams = () => {\r\n  queryParams.region = appliedFilters.region === 'all' ? null : appliedFilters.region;\r\n  const now = new Date();\r\n  let beginDate = null;\r\n  if (appliedFilters.time !== 'all') {\r\n    const endDate = new Date();\r\n    if (appliedFilters.time === 'week') endDate.setDate(now.getDate() - 7);\r\n    else if (appliedFilters.time === 'month') endDate.setMonth(now.getMonth() - 1);\r\n    else if (appliedFilters.time === 'year') endDate.setFullYear(now.getFullYear() - 1);\r\n    beginDate = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;\r\n  }\r\n  queryParams['params[beginPublishTime]'] = beginDate;\r\n};\r\n\r\nconst parseArticles = (rows) => {\r\n  return rows.map((article) => {\r\n    let parsedTags = [];\r\n    if (article.tags && typeof article.tags === 'string') {\r\n      try {\r\n        parsedTags = JSON.parse(article.tags);\r\n      } catch (e) {\r\n        console.error('解析文章标签JSON失败:', article.id, article.tags, e);\r\n      }\r\n    }\r\n    return { ...article, parsedTags: Array.isArray(parsedTags) ? parsedTags : [] };\r\n  });\r\n};\r\n\r\nconst loadArticles = async (isRefresh = false) => {\r\n  if (!isRefresh && loadStatus.value === 'nomore') return;\r\n  if (isRefresh) {\r\n    queryParams.pageNum = 1;\r\n    articleList.value = [];\r\n  }\r\n  loadStatus.value = 'loading';\r\n  try {\r\n    const response = await getArticleList(queryParams);\r\n    const newArticles = parseArticles(response.rows);\r\n    if (isRefresh) {\r\n      articleList.value = newArticles;\r\n    } else {\r\n      articleList.value.push(...newArticles);\r\n    }\r\n    if (response.rows.length < queryParams.pageSize || articleList.value.length >= response.total) {\r\n      loadStatus.value = 'nomore';\r\n    } else {\r\n      loadStatus.value = 'loadmore';\r\n    }\r\n  } catch (error) {\r\n    loadStatus.value = 'loadmore';\r\n    console.error('加载文章列表失败:', error);\r\n    uni.showToast({ title: '加载失败', icon: 'none' });\r\n  } finally {\r\n    uni.stopPullDownRefresh();\r\n  }\r\n};\r\n\r\nconst loadTags = async () => {\r\n  try {\r\n    const response = await listAllTag();\r\n    const backendTags = Array.isArray(response.data) ? response.data : [];\r\n    tagList.value = [{ id: null, name: '最新' }, ...backendTags];\r\n  } catch (error) {\r\n    console.error('加载标签列表失败:', error);\r\n  }\r\n};\r\n\r\nconst loadRegions = async () => {\r\n  try {\r\n    const response = await listAllRegion();\r\n    const backendRegions = Array.isArray(response.data) ? response.data : [];\r\n    const filteredRegions = backendRegions.filter(item => item.name !== '全球');\r\n    const formattedRegions = filteredRegions.map(item => ({\r\n      name: item.name,\r\n      value: item.code\r\n    }));\r\n    regionActions.value = [{ name: '全部地区', value: 'all' }, ...formattedRegions];\r\n  } catch (error) {\r\n    console.error('加载地区列表失败:', error);\r\n    regionActions.value = [{ name: '全部地区', value: 'all' }];\r\n  }\r\n};\r\n\r\nconst handleTabChange = (tab) => {\r\n  currentTabIndex.value = tab.index;\r\n  queryParams.tagIds = tab.id === null ? null : String(tab.id);\r\n  loadArticles(true);\r\n};\r\n\r\n// 防抖搜索函数\r\nconst debouncedSearch = debounce(() => {\r\n  const keyword = searchKeyword.value.trim();\r\n  queryParams.title = keyword || null;\r\n  loadArticles(true);\r\n}, 500);\r\n\r\nconst onSearchInput = (value) => {\r\n  searchKeyword.value = value;\r\n  if (!value || value.trim() === '') {\r\n    handleClear();\r\n  } else {\r\n    // 实时搜索：当用户输入时触发防抖搜索\r\n    debouncedSearch();\r\n  }\r\n};\r\n\r\nconst handleSearch = () => {\r\n  const keyword = searchKeyword.value.trim();\r\n  queryParams.title = keyword || null;\r\n  loadArticles(true);\r\n};\r\n\r\nconst handleClear = () => {\r\n  searchKeyword.value = '';\r\n  queryParams.title = null;\r\n  loadArticles(true);\r\n  setTimeout(() => uni.hideKeyboard(), 300);\r\n};\r\n\r\nconst onImageLoad = () => {};\r\nconst onImageError = () => {};\r\n\r\nconst gotoDetail = (id) => {\r\n  uni.navigateTo({\r\n    url: `/pages_sub/pages_article/detail?id=${id}`,\r\n  });\r\n};\r\n\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return '';\r\n  return dateString.split(' ')[0];\r\n};\r\n\r\nconst loadMore = () => {\r\n  if (loadStatus.value === 'loadmore') {\r\n    queryParams.pageNum++;\r\n    loadArticles();\r\n  }\r\n};\r\n\r\nconst initPageData = async () => {\r\n  try {\r\n    await Promise.all([\r\n      loadTags(),\r\n      loadRegions(),\r\n      loadArticles(true)\r\n    ]);\r\n  } catch (error) {\r\n    console.error('页面初始化失败:', error);\r\n    uni.showToast({title: '页面加载失败', icon: 'none'});\r\n  }\r\n};\r\n\r\nonLoad(() => {\r\n  console.log('文章列表页 onLoad - 页面首次加载');\r\n  uni.showShareMenu({\r\n      menus: ['shareAppMessage', 'shareTimeline']\r\n    });\r\n});\r\n\r\nonMounted(() => {\r\n  assets.value = uni.getStorageSync('staticAssets') || {};\r\n  initPageData();\r\n});\r\n\r\nonShow(() => {\r\n  uni.hideTabBar();\r\n});\r\n\r\nonPullDownRefresh(() => {\r\n  loadArticles(true);\r\n});\r\n\r\nonReachBottom(() => {\r\n  loadMore();\r\n});\r\n\r\nonShareAppMessage(() => {\r\n  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* --- Style 部分已根据您的最新需求重构 --- */\r\n:root {\r\n  --radius-small: 8rpx;\r\n  --radius-medium: 16rpx;\r\n  --radius-large: 24rpx;\r\n  --radius-xl: 32rpx;\r\n  --separator-color: #E5E7EB;\r\n  --content-padding: 32rpx;\r\n  font-style: normal;\r\n  text-transform: none;\r\n}\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background: #FFFFFF;\r\n}\r\n.header-section {\r\n  position: relative;\r\n  flex-shrink: 0;\r\n  z-index: 10;\r\n  background-color: #FFFFFF;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  padding-bottom: 24rpx;\r\n}\r\n.custom-nav-bar {\r\n  padding: 0 var(--content-padding) 32rpx;\r\n  .status-bar {\r\n    height: var(--status-bar-height);\r\n  }\r\n  .nav-title {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 184rpx;\r\n    box-sizing: border-box;\r\n    font-size: 32rpx;\r\n    color: #FFFFFF;\r\n  }\r\n}\r\n.filter-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 60rpx;\r\n}\r\n.sort-button,\r\n.filter-button {\r\n  font-weight: normal;\r\n  font-size: 26rpx;\r\n  color: #FFFFFF;\r\n  line-height: 44rpx;\r\n  text-align: left;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n  &.is-active {\r\n    color: #FFFFFF;\r\n    .custom-arrow {\r\n      border-top-color: #FFFFFF;\r\n    }\r\n  }\r\n}\r\n.filter-button .active-dot {\r\n  position: absolute;\r\n  top: -4rpx;\r\n  right: -16rpx;\r\n  width: 12rpx;\r\n  height: 12rpx;\r\n  background-color: #ef4444;\r\n  border-radius: 50%;\r\n  border: 2rpx solid #0F4CA7;\r\n}\r\n.search-box {\r\n  flex: 1;\r\n  height: 60rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 32rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 12rpx;\r\n  :deep(.uni-easyinput__content) {\r\n    background: transparent !important;\r\n    border: none !important;\r\n  }\r\n  :deep(.uni-easyinput__placeholder) {\r\n    color: #9B9A9A !important;\r\n    font-weight: normal;\r\n    font-size: 28rpx;\r\n    font-style: normal;\r\n    text-transform: none;\r\n    line-height: 44rpx;\r\n  }\r\n}\r\n.dropdown-wrapper {\r\n  position: fixed;\r\n  z-index: 999;\r\n  top: 326rpx;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  border-top-left-radius: var(--radius-large);\r\n  border-top-right-radius: var(--radius-large);\r\n  overflow: hidden;\r\n}\r\n.dropdown-mask {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  transition: opacity 0.3s ease;\r\n}\r\n.dropdown-panel {\r\n  position: absolute;\r\n  top: 0;\r\n  width: 100%;\r\n  background-color: #FFFFFF;\r\n  border-radius: var(--radius-large);\r\n  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  opacity: 0;\r\n  transform: translateY(-20px);\r\n  pointer-events: none;\r\n  transition: transform 0.25s ease, opacity 0.25s ease;\r\n  &.show {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n    pointer-events: auto;\r\n  }\r\n}\r\n.sort-panel {\r\n  padding: 16rpx 0;\r\n  .sort-option {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 28rpx 32rpx;\r\n    font-size: 28rpx;\r\n    color: #66666E;\r\n    transition: all 0.2s ease;\r\n    &.active {\r\n      color: #023F98;\r\n      font-weight: 500;\r\n      background-color: rgba(42, 97, 241, 0.05);\r\n    }\r\n  }\r\n}\r\n.filter-panel {\r\n  display: flex;\r\n  flex-direction: column;\r\n  max-height: 70vh;\r\n  .filter-scroll {\r\n    flex: 1;\r\n    padding: 32rpx;\r\n  }\r\n  .panel-section {\r\n    margin-bottom: 48rpx;\r\n    &:last-child {\r\n      margin-bottom: 24rpx;\r\n    }\r\n    .section-title {\r\n      font-weight: 500;\r\n      font-size: 30rpx;\r\n      color: #23232A;\r\n      margin-bottom: 28rpx;\r\n      display: block;\r\n    }\r\n    .panel-options {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 24rpx;\r\n    }\r\n    .option-btn {\r\n      width: 200rpx;\r\n      height: 68rpx;\r\n      background: #F2F4FA;\r\n      border-radius: var(--radius-small);\r\n      font-size: 28rpx;\r\n      color: #66666E;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      padding: 0;\r\n      transition: all 0.2s ease;\r\n      &.active {\r\n        background: rgba(42, 97, 241, 0.1);\r\n        color: #023F98;\r\n      }\r\n    }\r\n  }\r\n  .panel-footer {\r\n    display: flex;\r\n    gap: 24rpx;\r\n    padding: 24rpx 32rpx 32rpx;\r\n    border-top: 1rpx solid #F0F2F5;\r\n    .footer-btn {\r\n      flex: 1;\r\n      height: 80rpx;\r\n      margin: 0;\r\n      font-size: 30rpx;\r\n      border-radius: var(--radius-small);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border: none;\r\n      /* --- [核心修改] 开始 --- */\r\n      /* 添加下面这个选择器，即可去掉按钮的默认边框 */\r\n      &::after {\r\n        border: none;\r\n      }\r\n      /* --- [核心修改] 结束 --- */\r\n      &.reset {\r\n        background: rgba(42, 97, 241, 0.1);\r\n        color: #23232A;\r\n      }\r\n      &.confirm {\r\n        background: #023F98;\r\n        color: #FFFFFF;\r\n      }\r\n    }\r\n  }\r\n}\r\n.content-section {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n  background: #FFFFFF;\r\n  margin-top: -24rpx;\r\n  border-top-left-radius: var(--radius-large);\r\n  border-top-right-radius: var(--radius-large);\r\n  position: relative;\r\n  z-index: 20;\r\n}\r\n\r\n/* --- [核心修改] 开始 --- */\r\n/* 1. 为 tabs 容器添加 4rpx 粗的灰色底部边框 */\r\n.tabs-container {\r\n  flex-shrink: 0;\r\n  width: 100%;\r\n  background: #ffffff;\r\n  border-top-left-radius: inherit;\r\n  border-top-right-radius: inherit;\r\n  position: relative;\r\n  border-bottom: 2rpx solid var(--separator-color);\r\n  padding-left: -30rpx;\r\n}\r\n\r\n/* --- [新的解决方案] 开始 --- */\r\n/* 这次我们直接修改包裹标签和下划线的导航容器的高度 */\r\n.tabs-container :deep(.u-tabs__wrapper__nav) {\r\n  height: 75rpx !important;\r\n}\r\n/* --- [新的解决方案] 结束 --- */\r\n\r\n/* 2. 使用 :deep() 精准定位蓝色下划线，并将其下移与灰色边框重叠 */\r\n.tabs-container :deep(.u-tabs__wrapper__nav__line) {\r\n  /* 关键：将下划线向下移动。\r\n     计算方法：(灰色边框厚度 / 2) - (蓝色下划线厚度 / 2) = (4rpx / 2) - (2rpx / 2) = 1rpx。\r\n     我们把它移动到灰色边框的垂直中心线下方1rpx的位置，也就是设置 bottom 为 -3rpx\r\n   */\r\n  bottom: -3rpx !important;\r\n}\r\n/* --- [核心修改] 结束 --- */\r\n\r\n\r\n.tabs-container :deep(::-webkit-scrollbar) {\r\n  display: none;\r\n  width: 0 !important;\r\n  height: 0 !important;\r\n  -webkit-appearance: none;\r\n  background: transparent;\r\n  color: transparent;\r\n}\r\n\r\n/* --- [新增] 修改 u-tabs 标签项的间距 --- */\r\n.tabs-container :deep(.u-tabs__wrapper__nav__item) {\r\n  /* 设置左右内边距，例如各43rpx，总间距就是86rpx */\r\n  padding: 0 43rpx !important;\r\n}\r\n\r\n.article-list-scroll {\r\n  flex: 1;\r\n  height: 0;\r\n  box-sizing: border-box;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  padding-bottom: calc(160rpx + constant(safe-area-inset-bottom));\r\n  padding-bottom: calc(160rpx + env(safe-area-inset-bottom));\r\n}\r\n.article-list {\r\n  background: #FFFFFF;\r\n  padding-top: 40rpx; /* 使用 padding-top 代替 margin-top */\r\n}\r\n.article-card {\r\n  display: flex;\r\n  gap: 30rpx;\r\n  padding: 32rpx var(--content-padding);\r\n  background: #ffffff;\r\n  position: relative;\r\n  &:not(:last-child) {\r\n    border-bottom: 1rpx solid var(--separator-color);\r\n  }\r\n  .card-content {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    min-width: 0;\r\n  }\r\n  .card-title {\r\n    width: 346rpx;\r\n    height: 80rpx;\r\n    font-family: \"Alibaba PuHuiTi 3.0\", sans-serif;\r\n    font-weight: normal;\r\n    font-size: 28rpx;\r\n    color: #23232A;\r\n    text-align: justified;\r\n    line-height: 1.5;\r\n    font-style: normal;\r\n    text-transform: none;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    margin-bottom: auto;\r\n  }\r\n  .card-meta {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20rpx;\r\n    margin-top: 24rpx;\r\n    font-size: 24rpx;\r\n    color: #9B9A9A;\r\n  }\r\n  .card-tags {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 16rpx;\r\n    margin-top: 16rpx;\r\n  }\r\n  .tag-item {\r\n    /* 基础样式：动态宽度、固定高度、圆角等 */\r\n    height: 40rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 0 16rpx; /* 使用内边距代替固定宽度 */\r\n    border-radius: 4rpx;\r\n    font-size: 22rpx;\r\n    white-space: nowrap;\r\n    border-width: 1rpx;\r\n    border-style: solid;\r\n  }\r\n  .card-cover {\r\n    flex-shrink: 0;\r\n    width: 336rpx;\r\n    height: 190rpx;\r\n    border-radius: var(--radius-medium);\r\n    overflow: hidden;\r\n  }\r\n}\r\n\r\n/* ✨ 新增：预设的4种颜色样式 */\r\n.tag-color-0 {\r\n  border-color: #14BA67;\r\n  color: #14BA67;\r\n}\r\n.tag-color-1 {\r\n  border-color: #023F98;\r\n  color: #023F98;\r\n}\r\n.tag-color-2 {\r\n  border-color: #FB8620;\r\n  color: #FB8620;\r\n}\r\n.tag-color-3 {\r\n  border-color: #CFA616;\r\n  color: #CFA616;\r\n}\r\n\r\n\r\n.image-loading,\r\n.image-error {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #f8f8f8;\r\n  border-radius: var(--radius-medium);\r\n}\r\n.loading-text,\r\n.error-text {\r\n  font-size: 24rpx;\r\n  color: #9ca3af;\r\n  margin-top: 8rpx;\r\n}\r\n:deep(.u-loadmore__content__text) {\r\n  font-size: 24rpx !important;\r\n  color: #9B9A9A !important;\r\n  line-height: 34rpx !important;\r\n}\r\n.custom-arrow {\r\n  width: 0;\r\n  height: 0;\r\n  border-left: 8rpx solid transparent;\r\n  border-right: 8rpx solid transparent;\r\n  border-top: 8rpx solid #FFFFFF;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0.4rpx;\r\n}\r\n.rotate-180 {\r\n  transform: rotate(180deg);\r\n  transition: transform 0.3s ease;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/article/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "uni", "getArticleList", "listAllTag", "listAllRegion", "debounce", "onLoad", "onMounted", "onShow", "onPullDownRefresh", "onReachBottom", "onShareAppMessage"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKA,MAAA,eAAA,MAAA;AACA,MAAA,gBAAA,MAAA;;;;AASA,UAAA,cAAAA,cAAAA,IAAA,CAAA,CAAA;AACA,UAAA,UAAAA,cAAAA,IAAA,CAAA,EAAA,IAAA,MAAA,MAAA,KAAA,CAAA,CAAA;AACA,UAAA,kBAAAA,cAAAA,IAAA,CAAA;AACA,UAAA,aAAAA,cAAAA,IAAA,UAAA;AACA,UAAA,gBAAAA,cAAAA,IAAA,EAAA;AACA,UAAA,cAAAA,cAAAA,IAAA,IAAA;AACA,UAAA,SAAAA,cAAAA,IAAA,CAAA,CAAA;AAQA,UAAA,mBAAA,CAAA,QAAA;AACA,UAAA,CAAA,OAAA,OAAA,IAAA,OAAA,UAAA;AAEA,eAAA;AAAA,MACA;AAEA,YAAA,aAAA,IAAA,KAAA;AACA,aAAA,aAAA,UAAA;AAAA,IACA;AAGA,UAAA,cAAAC,cAAAA,SAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,OAAA;AAAA,MACA,QAAA;AAAA,MACA,eAAA;AAAA,MACA,OAAA;AAAA,MACA,QAAA;AAAA,MACA,4BAAA;AAAA,MACA,0BAAA;AAAA,IACA,CAAA;AAGA,UAAA,cAAAA,cAAAA,SAAA;AAAA,MACA,MAAA;AAAA,MACA,QAAA;AAAA,MACA,MAAA;AAAA,IACA,CAAA;AAEA,UAAA,iBAAAA,cAAAA,SAAA;AAAA,MACA,MAAA;AAAA,MACA,QAAA;AAAA,MACA,MAAA;AAAA,IACA,CAAA;AAGA,UAAA,cAAA;AAAA,MACA,EAAA,MAAA,QAAA,OAAA,UAAA;AAAA,MACA,EAAA,MAAA,QAAA,OAAA,eAAA;AAAA,MACA,EAAA,MAAA,QAAA,OAAA,aAAA;AAAA,IACA;AAEA,UAAA,gBAAAD,cAAAA,IAAA,CAAA,CAAA;AACAE,kBAAA,SAAA,MAAA,OAAA,MAAA,qBAAA,EAAA;AAEA,UAAA,cAAA;AAAA,MACA,EAAA,MAAA,MAAA,OAAA,MAAA;AAAA;AAAA,MACA,EAAA,MAAA,OAAA,OAAA,OAAA;AAAA,MACA,EAAA,MAAA,OAAA,OAAA,QAAA;AAAA,MACA,EAAA,MAAA,OAAA,OAAA,OAAA;AAAA,IACA;AAGA,UAAA,cAAAA,cAAA,SAAA,MAAA;AACA,YAAA,WAAA,OAAA,MAAA;AACA,UAAA,UAAA;AACA,eAAA;AAAA,UACA,iBAAA,QAAA,QAAA;AAAA,QACA;AAAA,MACA;AACA,aAAA;IACA,CAAA;AAEA,UAAA,iBAAAA,cAAA,SAAA,MAAA;AACA,YAAA,WAAA,YAAA,KAAA,UAAA,KAAA,UAAA,eAAA,IAAA;AACA,aAAA,WAAA,SAAA,OAAA;AAAA,IACA,CAAA;AAEA,UAAA,iBAAAA,cAAAA,SAAA,MAAA,eAAA,SAAA,KAAA;AAEA,UAAA,mBAAAA,cAAA,SAAA,MAAA;AACA,YAAA,WAAA,YAAA,KAAA,UAAA,KAAA,UAAA,eAAA,IAAA;AACA,aAAA,WAAA,SAAA,OAAA;AAAA,IACA,CAAA;AAGA,UAAA,cAAA,CAAA,cAAA;AACA,UAAA,YAAA,UAAA,WAAA;AACA,oBAAA,QAAA;AAAA,MACA,OAAA;AACA,oBAAA,OAAA,eAAA;AACA,oBAAA,SAAA,eAAA;AACA,oBAAA,OAAA,eAAA;AACA,oBAAA,QAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,aAAA,MAAA;AACA,kBAAA,QAAA;AAAA,IACA;AAGA,UAAA,oBAAA,MAAA;AAEA,qBAAA,OAAA,YAAA;AAGA,YAAA,YAAA,eAAA;AACA,kBAAA,gBAAA,cAAA,YAAA,eAAA;AACA,kBAAA,QAAA,cAAA,YAAA,QAAA;AAGA;AACA,mBAAA,IAAA;AAAA,IACA;AAGA,UAAA,kBAAA,MAAA;AAEA,kBAAA,OAAA;AACA,qBAAA,OAAA;AAGA,kBAAA,gBAAA;AACA,kBAAA,QAAA;AAGA;AACA,mBAAA,IAAA;AAAA,IACA;AAEA,UAAA,oBAAA,MAAA;AACA,qBAAA,SAAA;AACA,qBAAA,OAAA;AACA,kBAAA,SAAA;AACA,kBAAA,OAAA;AACA;AACA;AACA,mBAAA,IAAA;AAAA,IACA;AAEA,UAAA,sBAAA,MAAA;AACA,qBAAA,SAAA,YAAA;AACA,qBAAA,OAAA,YAAA;AACA;AACA;AACA,mBAAA,IAAA;AAAA,IACA;AAEA,UAAA,qBAAA,MAAA;AACA,kBAAA,SAAA,eAAA,WAAA,QAAA,OAAA,eAAA;AACA,YAAA,MAAA,oBAAA;AACA,UAAA,YAAA;AACA,UAAA,eAAA,SAAA,OAAA;AACA,cAAA,UAAA,oBAAA;AACA,YAAA,eAAA,SAAA;AAAA,kBAAA,QAAA,IAAA,YAAA,CAAA;AAAA,iBACA,eAAA,SAAA;AAAA,kBAAA,SAAA,IAAA,aAAA,CAAA;AAAA,iBACA,eAAA,SAAA;AAAA,kBAAA,YAAA,IAAA,gBAAA,CAAA;AACA,oBAAA,GAAA,QAAA,aAAA,IAAA,OAAA,QAAA,aAAA,CAAA,EAAA,SAAA,GAAA,GAAA,CAAA,IAAA,OAAA,QAAA,SAAA,EAAA,SAAA,GAAA,GAAA,CAAA;AAAA,MACA;AACA,kBAAA,0BAAA,IAAA;AAAA,IACA;AAEA,UAAA,gBAAA,CAAA,SAAA;AACA,aAAA,KAAA,IAAA,CAAA,YAAA;AACA,YAAA,aAAA,CAAA;AACA,YAAA,QAAA,QAAA,OAAA,QAAA,SAAA,UAAA;AACA,cAAA;AACA,yBAAA,KAAA,MAAA,QAAA,IAAA;AAAA,UACA,SAAA,GAAA;AACAC,0BAAAA,MAAA,MAAA,SAAA,kCAAA,iBAAA,QAAA,IAAA,QAAA,MAAA,CAAA;AAAA,UACA;AAAA,QACA;AACA,eAAA,EAAA,GAAA,SAAA,YAAA,MAAA,QAAA,UAAA,IAAA,aAAA,CAAA;MACA,CAAA;AAAA,IACA;AAEA,UAAA,eAAA,OAAA,YAAA,UAAA;AACA,UAAA,CAAA,aAAA,WAAA,UAAA;AAAA;AACA,UAAA,WAAA;AACA,oBAAA,UAAA;AACA,oBAAA,QAAA;MACA;AACA,iBAAA,QAAA;AACA,UAAA;AACA,cAAA,WAAA,MAAAC,mCAAA,WAAA;AACA,cAAA,cAAA,cAAA,SAAA,IAAA;AACA,YAAA,WAAA;AACA,sBAAA,QAAA;AAAA,QACA,OAAA;AACA,sBAAA,MAAA,KAAA,GAAA,WAAA;AAAA,QACA;AACA,YAAA,SAAA,KAAA,SAAA,YAAA,YAAA,YAAA,MAAA,UAAA,SAAA,OAAA;AACA,qBAAA,QAAA;AAAA,QACA,OAAA;AACA,qBAAA,QAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACA,mBAAA,QAAA;AACAD,sBAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,KAAA;AACAA,sBAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,OAAA,CAAA;AAAA,MACA,UAAA;AACAA,sBAAA,MAAA,oBAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,WAAA,YAAA;AACA,UAAA;AACA,cAAA,WAAA,MAAAE,gBAAAA;AACA,cAAA,cAAA,MAAA,QAAA,SAAA,IAAA,IAAA,SAAA,OAAA;AACA,gBAAA,QAAA,CAAA,EAAA,IAAA,MAAA,MAAA,KAAA,GAAA,GAAA,WAAA;AAAA,MACA,SAAA,OAAA;AACAF,sBAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,KAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,cAAA,YAAA;AACA,UAAA;AACA,cAAA,WAAA,MAAAG,mBAAAA;AACA,cAAA,iBAAA,MAAA,QAAA,SAAA,IAAA,IAAA,SAAA,OAAA;AACA,cAAA,kBAAA,eAAA,OAAA,UAAA,KAAA,SAAA,IAAA;AACA,cAAA,mBAAA,gBAAA,IAAA,WAAA;AAAA,UACA,MAAA,KAAA;AAAA,UACA,OAAA,KAAA;AAAA,QACA,EAAA;AACA,sBAAA,QAAA,CAAA,EAAA,MAAA,QAAA,OAAA,MAAA,GAAA,GAAA,gBAAA;AAAA,MACA,SAAA,OAAA;AACAH,sBAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,KAAA;AACA,sBAAA,QAAA,CAAA,EAAA,MAAA,QAAA,OAAA,MAAA,CAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,kBAAA,CAAA,QAAA;AACA,sBAAA,QAAA,IAAA;AACA,kBAAA,SAAA,IAAA,OAAA,OAAA,OAAA,OAAA,IAAA,EAAA;AACA,mBAAA,IAAA;AAAA,IACA;AAGA,UAAA,kBAAAI,YAAA,SAAA,MAAA;AACA,YAAA,UAAA,cAAA,MAAA,KAAA;AACA,kBAAA,QAAA,WAAA;AACA,mBAAA,IAAA;AAAA,IACA,GAAA,GAAA;AAEA,UAAA,gBAAA,CAAA,UAAA;AACA,oBAAA,QAAA;AACA,UAAA,CAAA,SAAA,MAAA,KAAA,MAAA,IAAA;AACA;MACA,OAAA;AAEA;MACA;AAAA,IACA;AAEA,UAAA,eAAA,MAAA;AACA,YAAA,UAAA,cAAA,MAAA,KAAA;AACA,kBAAA,QAAA,WAAA;AACA,mBAAA,IAAA;AAAA,IACA;AAEA,UAAA,cAAA,MAAA;AACA,oBAAA,QAAA;AACA,kBAAA,QAAA;AACA,mBAAA,IAAA;AACA,iBAAA,MAAAJ,cAAA,MAAA,aAAA,GAAA,GAAA;AAAA,IACA;AAEA,UAAA,cAAA,MAAA;AAAA,IAAA;AACA,UAAA,eAAA,MAAA;AAAA,IAAA;AAEA,UAAA,aAAA,CAAA,OAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,sCAAA,EAAA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,aAAA,CAAA,eAAA;AACA,UAAA,CAAA;AAAA,eAAA;AACA,aAAA,WAAA,MAAA,GAAA,EAAA,CAAA;AAAA,IACA;AAEA,UAAA,WAAA,MAAA;AACA,UAAA,WAAA,UAAA,YAAA;AACA,oBAAA;AACA;MACA;AAAA,IACA;AAEA,UAAA,eAAA,YAAA;AACA,UAAA;AACA,cAAA,QAAA,IAAA;AAAA,UACA,SAAA;AAAA,UACA,YAAA;AAAA,UACA,aAAA,IAAA;AAAA,QACA,CAAA;AAAA,MACA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,YAAA,KAAA;AACAA,sBAAA,MAAA,UAAA,EAAA,OAAA,UAAA,MAAA,OAAA,CAAA;AAAA,MACA;AAAA,IACA;AAEAK,kBAAAA,OAAA,MAAA;AACAL,oBAAAA,MAAA,MAAA,OAAA,kCAAA,uBAAA;AACAA,oBAAAA,MAAA,cAAA;AAAA,QACA,OAAA,CAAA,mBAAA,eAAA;AAAA,MACA,CAAA;AAAA,IACA,CAAA;AAEAM,kBAAAA,UAAA,MAAA;AACA,aAAA,QAAAN,cAAA,MAAA,eAAA,cAAA,KAAA,CAAA;AACA;IACA,CAAA;AAEAO,kBAAAA,OAAA,MAAA;AACAP,oBAAA,MAAA,WAAA;AAAA,IACA,CAAA;AAEAQ,kBAAAA,kBAAA,MAAA;AACA,mBAAA,IAAA;AAAA,IACA,CAAA;AAEAC,kBAAAA,cAAA,MAAA;AACA;IACA,CAAA;AAEAC,kBAAAA,kBAAA,MAAA;AACA,aAAA;IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/fA,GAAG,WAAW,eAAe;"}