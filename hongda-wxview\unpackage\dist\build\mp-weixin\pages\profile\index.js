"use strict";const e=require("../../common/vendor.js"),o=require("../../api/data/user.js"),n=require("../../utils/config.js");if(!Array){(e.resolveComponent("up-avatar")+e.resolveComponent("up-cell")+e.resolveComponent("up-cell-group"))()}Math||((()=>"../../uni_modules/uview-plus/components/u-avatar/u-avatar.js")+(()=>"../../uni_modules/uview-plus/components/u-cell/u-cell.js")+(()=>"../../uni_modules/uview-plus/components/u-cell-group/u-cell-group.js")+a)();const a=()=>"../../components/layout/CustomTabBar.js",l=Object.assign({name:"ProfileIndex"},{__name:"index",setup(a){const l=e.ref(!1),t=e.ref(null),r=e.ref(""),s=e.ref(!1),u=e.ref(""),i=e.computed((()=>l.value&&t.value&&t.value.avatarUrl?t.value.avatarUrl:r.value)),c=e.ref(""),v=e.ref(""),d=e.ref(""),g=e.ref(""),p=e.ref(""),m=e.ref(""),h=e.ref(""),f=e.ref(""),x=o=>{const n=e.index.getStorageSync("staticAssets");return n&&n[o]?n[o]:""},y=()=>{r.value=x("default_avatar")},_=()=>{c.value=x("mybg"),v.value=x("my_edit"),d.value=x("group_right"),g.value=x("my_phone"),p.value=x("my_contract"),m.value=x("my_personal"),h.value=x("my_delete"),f.value=x("order-card-bg")},w=e.computed((()=>{if(!t.value)return"用户";if(t.value.nickname)return t.value.nickname;if(t.value.phoneNumber){const e=t.value.phoneNumber;return 11===e.length?e.substring(0,3)+"****"+e.substring(7):e}return"用户"})),b=e.computed((()=>{var e,o;if(!l.value)return"";const n=(null==(e=t.value)?void 0:e.phone)||(null==(o=t.value)?void 0:o.phoneNumber);return n?11===n.length?n.substring(0,3)+"****"+n.substring(7):n:"未绑定"})),S=o=>{if(!l.value)return console.log("用户未登录，跳转到登录页"),void I();console.log("用户已登录，跳转到:",o),e.index.navigateTo({url:o,fail:o=>{console.error("页面跳转失败:",o),e.index.showToast({title:"页面跳转失败",icon:"none",duration:2e3})}})},k=()=>{l.value?(console.log("点击报名订单卡片，跳转到订单页面"),e.index.navigateTo({url:"/pages_sub/pages_profile/orders",fail:o=>{console.error("跳转订单页面失败:",o),e.index.showToast({title:"页面跳转失败",icon:"none",duration:2e3})}})):(console.log("点击报名订单卡片，需要先登录"),I())},T=()=>{const o=e.index.getStorageSync("token"),n=e.index.getStorageSync("userInfo");console.log("从本地存储获取的token:",o),console.log("从本地存储获取的userInfo:",n);const a=!!o;console.log("计算出的登录状态:",a),console.log("当前页面登录状态:",l.value),l.value=a,t.value=n||null,l.value&&n?(console.log("用户已登录，用户信息:",n),n.phoneNumber&&console.log("用户手机号:",n.phoneNumber)):l.value&&!n?console.log("有token但无用户信息"):console.log("用户未登录")},I=()=>{e.index.navigateTo({url:"/pages_sub/pages_other/login"})},L=(o=!1)=>{o?N("已退出登录"):e.index.showModal({title:"提示",content:"确定要退出登录吗？",success:e=>{e.confirm&&N("已退出登录")}})},A=()=>{var e;if(!l.value)return console.log("编辑昵称需要先登录"),void I();s.value=!0,u.value=(null==(e=t.value)?void 0:e.nickname)||""},U=async()=>{const o=u.value.trim();o?(s.value=!1,await C(o)):e.index.showToast({title:"昵称不能为空",icon:"none",duration:2e3})},j=()=>{s.value=!1,u.value=""},C=async n=>{console.log("=== 开始更新昵称 ==="),e.index.showLoading({title:"正在更新..."});try{console.log("调用updateUserInfoApi更新昵称:",n);const a=await o.updateUserInfoApi({nickname:n});console.log("昵称更新接口调用成功:",a),e.index.hideLoading(),t.value&&(t.value.nickname=n,e.index.setStorageSync("userInfo",t.value)),e.index.showToast({title:"昵称修改成功",icon:"success",duration:2e3}),console.log("昵称更新完成")}catch(a){console.error("更新昵称过程中发生错误:",a),e.index.hideLoading(),e.index.showToast({title:a.message||"更新失败，请稍后再试",icon:"none",duration:3e3}),console.error("昵称更新失败详情:",{message:a.message,stack:a.stack})}},N=o=>{console.log("=== 开始清除用户数据 ==="),e.index.removeStorageSync("token"),e.index.removeStorageSync("userInfo"),l.value=!1,t.value=null,o&&e.index.showToast({title:o,icon:"success",duration:1500}),console.log("用户数据已清除，UI已更新")};e.onShow((()=>{e.index.hideTabBar(),setTimeout((()=>{T(),y(),_()}),100)})),e.onLoad((()=>{console.log("个人中心页 onLoad - 页面首次加载"),e.index.showShareMenu({menus:["shareAppMessage","shareTimeline"]}),T(),y(),_()}));const B=e=>{var o;const n=null==(o=null==e?void 0:e.detail)?void 0:o.avatarUrl;n&&M(n)},M=async a=>{if(!l.value)return void I();const r=e.index.getStorageSync("token");if(r){e.index.showLoading({title:"上传中..."});try{await new Promise(((l,s)=>{e.index.uploadFile({url:n.BASE_URL+"/common/upload",filePath:a,name:"file",header:{Authorization:"Bearer "+r},success:async n=>{try{const a=JSON.parse(n.data||"{}");if(200===n.statusCode&&200===a.code&&a.url){const n=a.url;await o.updateUserInfoApi({avatarUrl:n}),t.value&&(t.value.avatarUrl=n,e.index.setStorageSync("userInfo",t.value)),e.index.showToast({title:"头像已更新",icon:"success"}),l(!0)}else e.index.showToast({title:"上传失败",icon:"none"}),s(new Error("upload error"))}catch(a){e.index.showToast({title:"响应解析失败",icon:"none"}),s(a)}},fail:o=>{e.index.showToast({title:"上传失败",icon:"none"}),s(o)},complete:()=>{e.index.hideLoading()}})}))}catch(s){}}else I()};return e.onShareAppMessage((()=>({}))),(o,n)=>e.e({a:c.value,b:e.o((e=>l.value?null:I)),c:e.p({size:54,src:i.value}),d:l.value},l.value?{e:e.o(B)}:{},{f:l.value},l.value?{g:e.t(w.value),h:v.value,i:e.o(A)}:{j:e.o(I)},{k:f.value,l:d.value,m:e.o(k),n:g.value,o:e.p({title:"绑定手机号",value:b.value,isLink:!1,border:!1}),p:m.value,q:e.o((e=>S("/pages_sub/pages_other/policy?type=privacy_policy"))),r:e.p({title:"隐私政策",isLink:!0,"arrow-direction":"right",border:!1}),s:p.value,t:e.o((e=>S("/pages_sub/pages_other/policy?type=user_agreement"))),v:e.p({title:"用户协议",isLink:!0,"arrow-direction":"right",border:!1}),w:e.p({border:!1}),x:l.value},l.value?{y:e.o(L)}:{},{z:e.p({current:4}),A:s.value},s.value?{B:15,C:u.value,D:e.o((e=>u.value=e.detail.value)),E:e.t(u.value.length),F:e.t(15),G:e.o(j),H:e.o(U),I:e.o((()=>{})),J:e.o(j)}:{})}}),t=e._export_sfc(l,[["__scopeId","data-v-bf141ed8"]]);l.__runtimeHooks=2,wx.createPage(t);
