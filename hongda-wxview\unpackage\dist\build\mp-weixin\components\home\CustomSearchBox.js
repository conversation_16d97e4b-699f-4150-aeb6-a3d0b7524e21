"use strict";const e=require("../../common/vendor.js");if(!Array){e.resolveComponent("u-icon")()}Math;const a={__name:"CustomSearchBox",props:{placeholder:{type:String,default:"搜索活动"},modelValue:{type:String,default:""}},emits:["update:modelValue","search","input"],setup(a,{emit:t}){const l=a,o=t,r=e.ref(l.modelValue);e.watch((()=>l.modelValue),(e=>{r.value=e})),e.watch(r,(e=>{o("update:modelValue",e)}));const n=e=>{var a;const t=(null==(a=e.detail)?void 0:a.value)||r.value;o("search",t)},u=e=>{var a,t;const l=(null==(a=e.detail)?void 0:a.value)||(null==(t=e.target)?void 0:t.value);r.value=l,o("input",l)},i=()=>{o("search",r.value)};return(t,l)=>({a:a.placeholder,b:"width: 112rpx; height: 44rpx; font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30; font-weight: normal; font-size: 28rpx; color: #9B9A9A; line-height: 44rpx; text-align: left; font-style: normal; text-transform: none;",c:e.o([e=>r.value=e.detail.value,u]),d:e.o(n),e:r.value,f:e.p({name:"search",size:"24",color:"#999999"}),g:e.o(i)})}},t=e._export_sfc(a,[["__scopeId","data-v-0e6aa954"]]);wx.createComponent(t);
