"use strict";const e=require("../../common/vendor.js"),a=require("../../api/content/country.js");if(!Array){(e.resolveComponent("uni-load-more")+e.resolveComponent("uni-icons")+e.resolveComponent("u-image"))()}Math||((()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js")+(()=>"../../uni_modules/uview-plus/components/u-image/u-image.js")+n)();const n=()=>"../../components/home/<USER>",t={__name:"detail",setup(n){const t=e.index.upx2px(20),o=e.ref(0),i=e.ref(0),u=e.ref(0),l=()=>{getCurrentPages().length>1?e.index.navigateBack({delta:1}):e.index.switchTab({url:"/pages/country/index"})};e.onBackPress((()=>!(getCurrentPages().length>1)&&(e.index.switchTab({url:"/pages/country/index"}),!0)));const c=e.ref(null),s=e.ref(!0),r=e.ref(0),v=e.ref({}),d=e.computed((()=>({backgroundImage:v.value.bg_basic_info_card?`url('${v.value.bg_basic_info_card}')`:"none"}))),m=e.computed((()=>({backgroundImage:v.value.bg_tab_active?`url('${v.value.bg_tab_active}')`:"none"}))),_=e.ref([{id:"basic",name:"基本信息",iconKey:"icon_tab_basic_normal",activeIconKey:"icon_tab_basic_active"},{id:"investment",name:"招商政策",iconKey:"icon_tab_investment_normal",activeIconKey:"icon_tab_investment_active"},{id:"customs",name:"海关政策",iconKey:"icon_tab_customs_normal",activeIconKey:"icon_tab_customs_active"},{id:"tax",name:"税务政策",iconKey:"icon_tab_tax_normal",activeIconKey:"icon_tab_tax_active"},{id:"parks",name:"工业园区",iconKey:"icon_tab_parks_normal",activeIconKey:"icon_tab_parks_active"}]),p=e.computed((()=>_.value.map((e=>({id:e.id,name:e.name,icon:v.value[e.iconKey]||e.fallbackIcon,activeIcon:v.value[e.activeIconKey]||e.fallbackActiveIcon}))))),g=e.computed((()=>c.value&&Array.isArray(c.value.basicInfoJson)?c.value.basicInfoJson:[])),b=e.computed((()=>{const e=[],a=g.value;for(let n=0;n<a.length;n+=2){const t=[a[n]];a[n+1]&&t.push(a[n+1]),e.push(t)}return e})),f=e.computed((()=>{var e;return["investment","customs","tax"].includes(null==(e=p.value[r.value])?void 0:e.id)})),y=e.computed((()=>{var e;return null==(e=p.value[r.value])?void 0:e.id})),x=e.computed((()=>{var e;return null==(e=p.value[r.value])?void 0:e.name})),h=e.computed((()=>{if(!c.value)return"";switch(y.value){case"investment":return c.value.investmentPolicy;case"customs":return c.value.customsPolicy;case"tax":return c.value.taxPolicy;default:return""}}));return e.onLoad((async n=>{if(console.log("国别详情页 onLoad - 页面首次加载"),e.index.showShareMenu({menus:["shareAppMessage","shareTimeline"]}),(()=>{try{const a=e.index.getMenuButtonBoundingClientRect();o.value=a.top,i.value=a.height,u.value=a.bottom+t}catch(a){const n=e.index.getSystemInfoSync();o.value=n.statusBarHeight||20,i.value=44,u.value=o.value+i.value+t}})(),v.value=e.index.getStorageSync("staticAssets")||{},!n.id)return e.index.showToast({title:"参数错误",icon:"none"}),void e.index.navigateBack();if(n.tab){const e=p.value.findIndex((e=>e.id===n.tab));-1!==e&&(r.value=e)}try{const e=await a.getCountryDetail(n.id);c.value=e.data}catch(l){console.error("获取详情失败:",l),e.index.showToast({title:l.message||"加载失败",icon:"none"})}finally{s.value=!1}})),e.onShareAppMessage((()=>({}))),(a,n)=>e.e({a:s.value},s.value?{b:e.p({status:"loading"})}:c.value?e.e({d:o.value+"px",e:e.p({type:"left",color:"#000000",size:"22"}),f:e.o(l),g:e.t(c.value.nameCn||"国别详情"),h:i.value+"px",i:e.unref(t)+"px",j:u.value+"px",k:c.value.detailsCoverUrl,l:e.t(c.value.nameCn),m:e.t(c.value.nameEn),n:e.f(p.value,((a,n,t)=>({a:r.value===n?a.activeIcon:a.icon,b:e.t(a.name),c:a.id,d:r.value===n?1:"",e:e.o((e=>(e=>{r.value=e})(n)),a.id),f:e.s(r.value===n?m.value:{})}))),o:!f.value},f.value?{A:e.t(x.value),B:e.t(h.value),C:y.value,D:e.p({"country-id":c.value.id,"policy-type":y.value})}:e.e({p:e.t(c.value.introduction),q:g.value.length>0},g.value.length>0?{r:e.f(b.value,((a,n,t)=>{var o,i,u,l;return{a:e.t(null==(o=a[0])?void 0:o.key),b:e.t(null==(i=a[1])?void 0:i.key),c:e.t(null==(u=a[0])?void 0:u.value),d:e.t(null==(l=a[1])?void 0:l.value),e:n}})),s:e.s(d.value)}:{},{t:0===r.value,v:e.f(c.value.industrialParks,((a,n,t)=>({a:e.t(n+1),b:"3f056926-2-"+t,c:e.p({src:a.coverImageUrl,width:"100%",height:"240rpx",fade:!0,"lazy-load":!0}),d:e.t(a.name),e:e.t(a.location),f:e.t(a.industries),g:e.t(a.features),h:a.id,i:e.o((n=>{return t=a.id,void e.index.navigateTo({url:`/pages_sub/pages_other/park_detail?id=${t}`});var t}),a.id)}))),w:v.value.icon_park_location||"/static/icons/位置icon金@2x.png",x:v.value.icon_park_industries||"/static/icons/企业浅金@2x.png",y:v.value.icon_park_features||"/static/icons/亮点浅金@2x.png",z:4===r.value}),{E:u.value+"px"}):{},{c:c.value})}},o=e._export_sfc(t,[["__scopeId","data-v-3f056926"]]);t.__runtimeHooks=2,wx.createPage(o);
