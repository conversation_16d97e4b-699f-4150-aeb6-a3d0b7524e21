"use strict";const e=require("../../../common/vendor.js");if(!Array){(e.resolveComponent("up-picker")+e.resolveComponent("up-input")+e.resolveComponent("up-button")+e.resolveComponent("up-popup"))()}Math||((()=>"../../../uni_modules/uview-plus/components/u-picker/u-picker.js")+(()=>"../../../uni_modules/uview-plus/components/u-input/u-input.js")+(()=>"../../../uni_modules/uview-plus/components/u-button/u-button.js")+(()=>"../../../uni_modules/uview-plus/components/u-popup/u-popup.js"))();const o={__name:"FormPickerDialogs",props:{pickerShow:{type:<PERSON>olean,default:!1},pickerColumns:{type:Array,default:()=>[]},cascaderShow:{type:Boolean,default:!1},cascaderColumns:{type:Array,default:()=>[]},colorPickerShow:{type:Boolean,default:!1},colorList:{type:Array,default:()=>[]},currentColorField:{type:String,default:""},formData:{type:Object,default:()=>({})}},emits:["pickerConfirm","pickerCancel","cascaderConfirm","colorConfirm"],setup(o,{expose:r,emit:l}){const u=o,a=l,t=e.ref(""),p=e.ref(""),n=e=>{e&&e.value&&Array.isArray(e.value)&&0!==e.value.length&&e.value[0]&&void 0!==e.value[0].value&&a("pickerConfirm",e,p.value)},c=e=>{e&&e.value&&Array.isArray(e.value)&&a("cascaderConfirm",e,p.value)},s=()=>u.formData[u.currentColorField]||"",i=()=>{t.value&&!/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(t.value)&&(e.index.$u.toast("请输入正确的颜色格式，如 #FF0000"),t.value="")},m=()=>{t.value?(i(),t.value&&(a("colorConfirm",t.value),t.value="")):e.index.$u.toast("请输入颜色值")};return r({setCurrentField:e=>{p.value=e}}),(r,l)=>({a:e.o(n),b:e.o((e=>r.$emit("pickerCancel"))),c:e.p({show:o.pickerShow,columns:o.pickerColumns,keyName:"label"}),d:e.o(c),e:e.o((e=>r.$emit("pickerCancel"))),f:e.p({show:o.cascaderShow,columns:o.cascaderColumns,keyName:"label"}),g:e.f(o.colorList,((o,r,l)=>e.e({a:s()===o},(s(),{}),{b:o,c:o,d:e.o((e=>(e=>{a("colorConfirm",e)})(o)),o)}))),h:e.o(i),i:e.o((e=>t.value=e)),j:e.p({placeholder:"#FF0000",maxlength:"7",customStyle:"width: 200rpx; height: 60rpx; border: 1px solid #ddd; border-radius: 8rpx;",modelValue:t.value}),k:e.o(m),l:e.p({size:"small",type:"primary",customStyle:"margin-left: 20rpx;"}),m:e.o((e=>r.$emit("pickerCancel"))),n:e.p({show:o.colorPickerShow,mode:"bottom",round:"20",closeable:!0})})}},r=e._export_sfc(o,[["__scopeId","data-v-98280c54"]]);wx.createComponent(r);
