<template>
  <DetailSkeleton v-if="loading" />

  <view v-else-if="article" class="page-container">
    <view class="fixed-header" :style="{ height: headerHeight + 'px' }">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="custom-nav-bar" :style="{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }">
        <view class="nav-back-button" @click="goBack">
          <uni-icons type="left" color="#000000" size="22"></uni-icons>
        </view>
        <view class="nav-title">政策详情</view>
      </view>
    </view>

    <scroll-view scroll-y class="scrollable-content" :style="{ paddingTop: headerHeight + 'px' }">
      <view class="main-content">
        <view class="article-header">
          <text class="article-title">{{ article.title }}</text>
          <view class="article-meta">
            <view class="meta-item">
              <text class="meta-label">发布于 {{ article.createTime }}</text>
            </view>
          </view>
        </view>

        <view class="content-card">
          <view class="content-body">
            <mp-html
                :content="article.content"
                :domain="baseUrl"
                :tag-style="tagStyle"
                :container-style="containerStyle"
                :preview-img="true"
                lazy-load
            />
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <view v-else class="error-state">
    <u-empty mode="list" text="文章不存在或已被删除"></u-empty>
  </view>
</template>

<script setup>
import {onLoad, onShareAppMessage} from '@dcloudio/uni-app'
import {ref} from 'vue';
import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue';
import {getCountryPolicyArticle} from '@/api/content/countryPolicy.js';
import {IMAGE_BASE_URL} from '@/utils/config.js';
import {containerStyle, tagStyle} from '@/utils/mpHtmlStyles.js';
// [关键修改 1] 导入公共的富文本格式化函数
import { formatRichText } from '@/utils/tools.js';

// --- 自定义导航栏相关逻辑 (无变化) ---
const navBarPaddingBottomRpx = 10;
const navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);

const statusBarHeight = ref(0);
const navBarHeight = ref(0);
const headerHeight = ref(0);

const getNavBarInfo = () => {
  try {
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    statusBarHeight.value = menuButtonInfo.top;
    navBarHeight.value = menuButtonInfo.height;
    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;
  } catch (e) {
    const systemInfo = uni.getSystemInfoSync();
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    navBarHeight.value = 44;
    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;
  }
};

const goBack = () => {
  uni.navigateBack({ delta: 1 });
};
// --- 导航栏逻辑结束 ---


const baseUrl = IMAGE_BASE_URL;

const article = ref(null);
const loading = ref(true);

onLoad(async (options) => {
  console.log('政策详情页 onLoad - 页面首次加载');
  uni.showShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    });

  getNavBarInfo();

  if (!options.id) {
    uni.showToast({ title: '参数错误', icon: 'none' });
    loading.value = false;
    return;
  }

  try {
    const res = await getCountryPolicyArticle(options.id);
    if (res.code === 200 && res.data) {
      // [关键修改 2] 在将数据赋值给 article.value 之前，调用函数处理 content 字段
      res.data.content = formatRichText(res.data.content);
      article.value = res.data;
    } else {
      throw new Error(res.msg || '文章不存在或已被删除');
    }
  } catch (error) {
    console.error('获取国别政策文章失败:', error);
    article.value = null;
  } finally {
    loading.value = false;
  }
});

onShareAppMessage(() => {
  return {}; // 返回一个空对象，uni-app/微信会自动采用默认行为
});
</script>

<style lang="scss" scoped>
/* 样式无需修改 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.scrollable-content {
  flex: 1;
  height: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background-color: #f8f9fa;
}

.status-bar {
  width: 100%;
}

.custom-nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: content-box;
}

.nav-back-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 15rpx;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #000000;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60%;
}

.main-content {
  padding: 30rpx;
}

.article-header {
  background: #ffffff;
  padding: 40rpx;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  .article-title {
    font-size: 44rpx;
    font-weight: 700;
    color: #303133;
    line-height: 1.5;
  }
  .article-meta {
    margin-top: 30rpx;
    font-size: 26rpx;
    color: #909399;
  }
}

.content-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 10rpx 40rpx 40rpx 40rpx;
}

.error-state {
  padding-top: 200rpx;
}

:deep(.mp-html table) {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: 40rpx 0 !important;
  border: 2rpx solid #e9e9eb !important;
}

:deep(.mp-html th) {
  background: #f8f9fa !important;
  color: #303133 !important;
  padding: 24rpx !important;
  border: 2rpx solid #e9e9eb !important;
  font-weight: 600 !important;
}

:deep(.mp-html td) {
  border: 2rpx solid #e9e9eb !important;
  padding: 24rpx !important;
  background: #ffffff !important;
  color: #3D424D !important;
}
</style>