"use strict";
const common_vendor = require("../../common/vendor.js");
const api_data_registration = require("../../api/data/registration.js");
const api_data_event = require("../../api/data/event.js");
const utils_image = require("../../utils/image.js");
if (!Array) {
  const _easycom_up_navbar2 = common_vendor.resolveComponent("up-navbar");
  const _easycom_up_loading_page2 = common_vendor.resolveComponent("up-loading-page");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  const _easycom_up_empty2 = common_vendor.resolveComponent("up-empty");
  const _easycom_up_loadmore2 = common_vendor.resolveComponent("up-loadmore");
  (_easycom_up_navbar2 + _easycom_up_loading_page2 + _easycom_up_button2 + _easycom_up_empty2 + _easycom_up_loadmore2)();
}
const _easycom_up_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_up_loading_page = () => "../../uni_modules/uview-plus/components/u-loading-page/u-loading-page.js";
const _easycom_up_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_up_empty = () => "../../uni_modules/uview-plus/components/u-empty/u-empty.js";
const _easycom_up_loadmore = () => "../../uni_modules/uview-plus/components/u-loadmore/u-loadmore.js";
if (!Math) {
  (_easycom_up_navbar + _easycom_up_loading_page + _easycom_up_button + _easycom_up_empty + _easycom_up_loadmore + CustomTabBar)();
}
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const _sfc_main = {
  __name: "orders",
  setup(__props) {
    const loading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const registrationList = common_vendor.ref([]);
    const loginCheckTrigger = common_vendor.ref(0);
    const showCancelModal = common_vendor.ref(false);
    const currentCancelItem = common_vendor.ref(null);
    const ordersWarningIconUrl = common_vendor.ref("");
    const orderStatusBgUrl = common_vendor.ref("");
    const resolveAssetUrl = (assetKey) => {
      const assets = common_vendor.index.getStorageSync("staticAssets");
      return assets && assets[assetKey] ? assets[assetKey] : "";
    };
    const refreshOrdersAssets = () => {
      ordersWarningIconUrl.value = resolveAssetUrl("orders_warning");
      orderStatusBgUrl.value = resolveAssetUrl("order_bg");
    };
    const loadMoreStatus = common_vendor.computed(() => {
      if (loading.value)
        return "loading";
      return "nomore";
    });
    const getUserToken = () => {
      try {
        return common_vendor.index.getStorageSync("token") || null;
      } catch (e) {
        return null;
      }
    };
    const isLoggedIn = common_vendor.computed(() => {
      loginCheckTrigger.value;
      return !!getUserToken();
    });
    const checkLoginAndRefresh = async () => {
      common_vendor.index.__f__("log", "at pages_sub/pages_profile/orders.vue:219", "=== 检查登录状态并刷新数据 ===");
      loginCheckTrigger.value++;
      const currentToken = getUserToken();
      common_vendor.index.__f__("log", "at pages_sub/pages_profile/orders.vue:225", "当前登录状态:", !!currentToken);
      if (!currentToken) {
        common_vendor.index.__f__("log", "at pages_sub/pages_profile/orders.vue:228", "用户未登录，清空数据并跳转到登录页");
        registrationList.value = [];
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录后查看报名记录",
          confirmText: "去登录",
          cancelText: "取消",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.setStorageSync("loginBackPage", "/pages/profile/orders");
              common_vendor.index.navigateTo({ url: "/pages/login/index" });
            } else {
              common_vendor.index.navigateBack({
                fail: () => {
                  common_vendor.index.switchTab({ url: "/pages/index/index" });
                }
              });
            }
          }
        });
        return;
      }
      await loadMyRegistrations();
    };
    const loadMyRegistrations = async () => {
      if (loading.value)
        return;
      if (!isLoggedIn.value) {
        common_vendor.index.__f__("log", "at pages_sub/pages_profile/orders.vue:266", "用户未登录，跳过数据加载");
        registrationList.value = [];
        return;
      }
      loading.value = true;
      try {
        const response = await api_data_registration.getMyRegistrationsApi();
        if (response.code === 200) {
          const rawData = response.data || [];
          common_vendor.index.__f__("log", "at pages_sub/pages_profile/orders.vue:280", "获取报名记录成功:", rawData.length, "条");
          if (rawData.length > 0) {
            registrationList.value = await enrichRegistrationData(rawData);
          } else {
            registrationList.value = [];
          }
        } else {
          common_vendor.index.__f__("log", "at pages_sub/pages_profile/orders.vue:289", "暂无报名记录");
          registrationList.value = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_sub/pages_profile/orders.vue:294", "获取报名订单失败:", error);
        if (error.message && error.message.includes("未登录")) {
          common_vendor.index.__f__("log", "at pages_sub/pages_profile/orders.vue:298", "检测到登录状态异常，清除token并提示重新登录");
          common_vendor.index.removeStorageSync("token");
          loginCheckTrigger.value++;
          common_vendor.index.showModal({
            title: "登录过期",
            content: "登录状态已过期，请重新登录",
            confirmText: "重新登录",
            showCancel: false,
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.setStorageSync("loginBackPage", "/pages/profile/orders");
                common_vendor.index.navigateTo({ url: "/pages/login/index" });
              }
            }
          });
          return;
        }
        handleError(error, "获取报名记录");
      } finally {
        loading.value = false;
        isRefreshing.value = false;
      }
    };
    const enrichRegistrationData = async (registrations) => {
      const sortedRegistrations = registrations.sort((a, b) => {
        var _a, _b;
        const db = ((_a = parseSafeDate(b.registrationTime)) == null ? void 0 : _a.getTime()) ?? 0;
        const da = ((_b = parseSafeDate(a.registrationTime)) == null ? void 0 : _b.getTime()) ?? 0;
        return db - da;
      });
      const enrichedData = await Promise.allSettled(
        sortedRegistrations.map(async (registration) => {
          try {
            const eventDetailResponse = await api_data_event.getEventDetailApi(registration.eventId);
            if (eventDetailResponse.code === 200 && eventDetailResponse.data) {
              const eventInfo = eventDetailResponse.data;
              return {
                ...registration,
                title: eventInfo.title || "活动标题",
                coverImageUrl: utils_image.getFullImageUrl(eventInfo.coverImageUrl),
                location: eventInfo.location || "待定",
                startTime: eventInfo.startTime
              };
            } else {
              return {
                ...registration,
                title: "活动标题",
                coverImageUrl: "",
                location: "待定"
              };
            }
          } catch (error) {
            common_vendor.index.__f__("warn", "at pages_sub/pages_profile/orders.vue:362", "获取活动详情失败:", registration.eventId, error);
            return {
              ...registration,
              title: "活动标题",
              coverImageUrl: "",
              location: "待定"
            };
          }
        })
      );
      return enrichedData.filter((result) => result.status === "fulfilled").map((result) => result.value);
    };
    const goToEventDetail = (eventId) => {
      common_vendor.index.navigateTo({
        url: `/pages_sub/pages_event/detail?id=${eventId}`
      });
    };
    const showCancelConfirm = (item) => {
      currentCancelItem.value = item;
      showCancelModal.value = true;
    };
    const closeCancelModal = () => {
      showCancelModal.value = false;
      currentCancelItem.value = null;
    };
    const confirmCancelRegistration = () => {
      if (currentCancelItem.value) {
        cancelRegistration(currentCancelItem.value);
        closeCancelModal();
      }
    };
    const cancelRegistration = async (item) => {
      try {
        common_vendor.index.showLoading({
          title: "取消中...",
          mask: true
        });
        const response = await api_data_registration.cancelRegistrationApi({
          eventId: item.eventId
        });
        common_vendor.index.hideLoading();
        if (response.code === 200) {
          const index = registrationList.value.findIndex(
            (reg) => reg.eventId === item.eventId && reg.userId === item.userId
          );
          if (index !== -1) {
            registrationList.value[index].status = 1;
          }
          common_vendor.index.showToast({
            title: "取消报名成功",
            icon: "success",
            duration: 2e3
          });
        } else {
          throw new Error(response.msg || "取消报名失败");
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages_sub/pages_profile/orders.vue:456", "取消报名失败:", error);
        common_vendor.index.showToast({
          title: error.message || "取消报名失败，请稍后重试",
          icon: "none",
          duration: 3e3
        });
      }
    };
    const onRefresh = () => {
      isRefreshing.value = true;
      checkLoginAndRefresh();
    };
    const goToLogin = () => {
      common_vendor.index.navigateTo({ url: "/pages_sub/pages_other/login" });
    };
    const onImageLoad = (e) => {
      common_vendor.index.__f__("log", "at pages_sub/pages_profile/orders.vue:485", "图片加载成功");
    };
    const onImageError = (e) => {
      common_vendor.index.__f__("error", "at pages_sub/pages_profile/orders.vue:492", "图片加载失败:", e);
    };
    const onLoadMore = () => {
    };
    const parseSafeDate = (input) => {
      if (!input)
        return null;
      if (input instanceof Date) {
        return isNaN(input.getTime()) ? null : input;
      }
      if (typeof input === "number") {
        const d = new Date(input);
        return isNaN(d.getTime()) ? null : d;
      }
      if (typeof input === "string") {
        let s = input.trim();
        if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(s)) {
          s = s.replace(" ", "T");
        }
        let d = new Date(s);
        if (isNaN(d.getTime())) {
          const m = s.match(/^(\d{4})-(\d{1,2})-(\d{1,2})(?:[ T](\d{1,2}):(\d{2})(?::(\d{2}))?)?/);
          if (m) {
            const y = m[1];
            const mo = m[2];
            const day = m[3];
            const rest = m[4] ? ` ${m[4]}:${m[5]}:${m[6] || "00"}` : "";
            d = /* @__PURE__ */ new Date(`${y}/${mo}/${day}${rest}`);
          }
        }
        return isNaN(d.getTime()) ? null : d;
      }
      return null;
    };
    const formatRegistrationTime = (time) => {
      if (!time)
        return "";
      const date = parseSafeDate(time);
      if (!date)
        return "";
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    };
    const formatRegistrationStatus = (status) => {
      return status === 0 ? "已报名" : "已取消";
    };
    const getRegistrationStatusClass = (status) => {
      return status === 0 ? "registered" : "cancelled";
    };
    const handleError = (error, context = "") => {
      common_vendor.index.__f__("error", "at pages_sub/pages_profile/orders.vue:568", `${context} 错误:`, error);
      let message = "操作失败，请稍后重试";
      if (error.code) {
        switch (error.code) {
          case 401:
            message = "登录已过期，请重新登录";
            common_vendor.index.reLaunch({ url: "/pages/login/index" });
            return;
          case 403:
            message = "权限不足";
            break;
          case 404:
            message = "请求的资源不存在";
            break;
          case 500:
            message = "服务器错误，请稍后重试";
            break;
          default:
            message = error.message || message;
        }
      }
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration: 3e3
      });
    };
    common_vendor.onLoad(() => {
      common_vendor.index.__f__("log", "at pages_sub/pages_profile/orders.vue:601", "报名订单页 onLoad - 页面首次加载");
      common_vendor.index.showShareMenu({
        menus: ["shareAppMessage", "shareTimeline"]
      });
      refreshOrdersAssets();
      checkLoginAndRefresh();
    });
    common_vendor.onShow(async () => {
      try {
        const loginBackPage = common_vendor.index.getStorageSync("loginBackPage");
        if (loginBackPage === "/pages/profile/orders") {
          common_vendor.index.removeStorageSync("loginBackPage");
          loginCheckTrigger.value++;
          await loadMyRegistrations();
          return;
        }
      } catch (e) {
        common_vendor.index.__f__("warn", "at pages_sub/pages_profile/orders.vue:628", "检查登录返回标记失败:", e);
      }
      checkLoginAndRefresh();
      refreshOrdersAssets();
    });
    common_vendor.onPullDownRefresh(async () => {
      await onRefresh();
      setTimeout(() => {
        common_vendor.index.stopPullDownRefresh();
      }, 1e3);
    });
    common_vendor.onShareAppMessage(() => {
      return {};
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "报名订单",
          autoBack: true,
          fixed: true,
          safeAreaInsetTop: true,
          placeholder: true,
          bgColor: "#ffffff",
          leftIcon: "arrow-left",
          leftIconColor: "#333333",
          titleStyle: "font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30'; font-weight: normal; font-size: 32rpx; color: #000000; line-height: 44rpx; text-align: center; font-style: normal; text-transform: none;"
        }),
        b: loading.value && registrationList.value.length === 0
      }, loading.value && registrationList.value.length === 0 ? {
        c: common_vendor.p({
          loading: true,
          ["loading-text"]: "加载中..."
        })
      } : !loading.value && registrationList.value.length === 0 ? common_vendor.e({
        e: !isLoggedIn.value
      }, !isLoggedIn.value ? {
        f: common_vendor.o(goToLogin),
        g: common_vendor.p({
          type: "primary",
          text: "去登录"
        })
      } : {}, {
        h: common_vendor.p({
          mode: "list",
          text: isLoggedIn.value ? "暂无报名记录" : "请先登录查看报名记录",
          textColor: "#909399",
          iconSize: "120"
        })
      }) : {
        i: common_vendor.f(registrationList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: item.coverImageUrl,
            b: common_vendor.o(onImageError, item.id),
            c: common_vendor.o(onImageLoad, item.id),
            d: common_vendor.t(item.title || "活动标题"),
            e: common_vendor.t(formatRegistrationTime(item.registrationTime)),
            f: item.status === 0
          }, item.status === 0 ? {
            g: orderStatusBgUrl.value
          } : item.status === 1 ? {} : {
            i: common_vendor.t(formatRegistrationStatus(item.status)),
            j: common_vendor.n(getRegistrationStatusClass(item.status))
          }, {
            h: item.status === 1,
            k: item.status === 0
          }, item.status === 0 ? {
            l: common_vendor.o(($event) => showCancelConfirm(item), item.id)
          } : {}, {
            m: item.id,
            n: common_vendor.o(($event) => goToEventDetail(item.eventId), item.id)
          });
        })
      }, {
        d: !loading.value && registrationList.value.length === 0,
        j: registrationList.value.length > 0
      }, registrationList.value.length > 0 ? {
        k: common_vendor.p({
          status: loadMoreStatus.value,
          ["loading-text"]: "正在加载...",
          ["loadmore-text"]: "上拉加载更多",
          ["nomore-text"]: "没有更多了"
        })
      } : {}, {
        l: common_vendor.o(onLoadMore),
        m: isRefreshing.value,
        n: common_vendor.o(onRefresh),
        o: common_vendor.p({
          current: 4
        }),
        p: showCancelModal.value
      }, showCancelModal.value ? {
        q: ordersWarningIconUrl.value,
        r: common_vendor.o(closeCancelModal),
        s: common_vendor.o(confirmCancelRegistration),
        t: common_vendor.o(() => {
        }),
        v: common_vendor.o(closeCancelModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-79fff75c"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_sub/pages_profile/orders.js.map
