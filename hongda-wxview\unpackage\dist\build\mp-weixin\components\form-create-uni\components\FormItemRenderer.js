"use strict";const e=require("../../../common/vendor.js");if(!Array){(e.resolveComponent("up-input")+e.resolveComponent("up-textarea")+e.resolveComponent("up-checkbox")+e.resolveComponent("up-checkbox-group")+e.resolveComponent("up-radio")+e.resolveComponent("up-radio-group")+e.resolveComponent("up-switch")+e.resolveComponent("up-rate")+e.resolveComponent("up-slider")+e.resolveComponent("up-upload")+e.resolveComponent("up-form-item"))()}Math||((()=>"../../../uni_modules/uview-plus/components/u-input/u-input.js")+(()=>"../../../uni_modules/uview-plus/components/u-textarea/u-textarea.js")+(()=>"../../../uni_modules/uview-plus/components/u-checkbox/u-checkbox.js")+(()=>"../../../uni_modules/uview-plus/components/u-checkbox-group/u-checkbox-group.js")+(()=>"../../../uni_modules/uview-plus/components/u-radio/u-radio.js")+(()=>"../../../uni_modules/uview-plus/components/u-radio-group/u-radio-group.js")+(()=>"../../../uni_modules/uview-plus/components/u-switch/u-switch.js")+(()=>"../../../uni_modules/uview-plus/components/u-rate/u-rate.js")+(()=>"../../../uni_modules/uview-plus/components/u-slider/u-slider.js")+(()=>"../../../uni_modules/uview-plus/components/u-upload/u-upload.js")+(()=>"../../../uni_modules/uview-plus/components/u-form-item/u-form-item.js"))();const t={__name:"FormItemRenderer",props:{item:{type:Object,required:!0},formData:{type:Object,required:!0}},emits:["updateField","openPicker"],setup(t,{emit:i}){const a=t,o=i,r=e.computed((()=>(()=>{const e={input:()=>{var e,t;return"textarea"===(null==(e=a.item.props)?void 0:e.type)?"textarea":(null==(t=a.item.props)||t.type,"input")},inputNumber:"number",password:"input",textarea:"textarea",select:"select",cascader:"cascader",radio:"radio",checkbox:"checkbox",switch:"switch",slider:"slider",rate:"rate",datePicker:"date",timePicker:"time",dateTimePicker:"datetime",colorPicker:"color",upload:"upload",fcEditor:"textarea",elTreeSelect:"tree",tree:"tree",elTransfer:"transfer",group:"textarea",subForm:"textarea"}[a.item.type];return"function"==typeof e?e():e||a.item.type})())),l=e.computed((()=>["input","number","phone","email","inputNumber"].includes(r.value))),p=e.computed((()=>["select"].includes(r.value))),u=e.computed((()=>{var e;const t=r.value,i=a.item.type;if(["date","time","datetime","daterange","timerange"].includes(t))return!0;if("datePicker"===i){null==(e=a.item.props)||e.type;return!0}return"timePicker"===i})),d=e.computed((()=>{var e,t;a.item.type;const i=null==(e=a.item.props)?void 0:e.type,o=null==(t=a.item.props)?void 0:t.isRange;return"datetimerange"===i||"daterange"===i||!0===o||["daterange","timerange"].includes(r.value)})),n=e.computed((()=>["upload","imageUpload","fileUpload"].includes(r.value))),m=e.computed((()=>{var e,t,i;return a.item.options&&Array.isArray(a.item.options)?a.item.options:(null==(e=a.item.props)?void 0:e.options)&&Array.isArray(a.item.props.options)?a.item.props.options:(null==(t=a.item.props)?void 0:t.data)&&Array.isArray(a.item.props.data)?a.item.props.data:a.item.option&&Array.isArray(a.item.option)?a.item.option:a.item.children&&Array.isArray(a.item.children)?a.item.children:"elTransfer"===a.item.type&&(null==(i=a.item.props)?void 0:i.data)?a.item.props.data.map((e=>({label:e.label,value:e.key||e.value}))):[]})),s=()=>{var e;return"inputNumber"===a.item.type||"phone"===a.item.type?"number":"password"===(null==(e=a.item.props)?void 0:e.type)||"password"===a.item.type?"password":"text"},c=()=>{var e;return"phone"===a.item.type?11:(null==(e=a.item.props)?void 0:e.maxlength)||100},v=e.computed((()=>{const e=a.formData[a.item.field];if(!e)return"";const t=m.value;if(!t||!Array.isArray(t)||0===t.length)return"";if("cascader"===a.item.type||"cascader"===r.value)return y();const i=t.find((t=>t&&t.value===e));return i?i.label:""})),f=()=>v.value,y=()=>{const e=a.formData[a.item.field];if(!Array.isArray(e)||0===e.length)return"";let t="",i=m.value;for(let a=0;a<e.length;a++){const o=e[a],r=null==i?void 0:i.find((e=>e.value===o));if(!r)break;t+=(a>0?" / ":"")+r.label,i=r.children}return t},h=()=>{const e=a.formData[a.item.field];if(!e)return"";if(Array.isArray(e)){const t=x(m.value);return e.map((e=>{const i=t.find((t=>t.id===e||t.value===e));return i?i.label:e})).filter(Boolean).join(", ")}const t=x(m.value).find((t=>t.id===e||t.value===e));return t?t.label:e},x=(e,t=[])=>(e.forEach((e=>{t.push(e),e.children&&Array.isArray(e.children)&&x(e.children,t)})),t),b=()=>{var e;return(null==(e=a.item.props)?void 0:e.data)&&Array.isArray(a.item.props.data)?a.item.props.data.map((e=>({label:e.label,value:e.key||e.value}))):m.value},g=e=>{var t,i;let r="";"string"==typeof e?r=e:e&&"object"==typeof e?(r=(null==(t=e.detail)?void 0:t.value)||(null==(i=e.target)?void 0:i.value)||e.value||"",""===r&&e.mp&&e.mp.detail&&(r=e.mp.detail.value||"")):r="",r=String(r),o("updateField",a.item.field,r)},k=()=>{},w=e=>{o("updateField",a.item.field,e)},F=e=>{o("updateField",a.item.field,e)},A=e=>{o("updateField",a.item.field,e)},D=e=>{o("updateField",a.item.field,e)},C=e=>{o("updateField",a.item.field,e)},j=()=>{var t;const i=m.value;i&&Array.isArray(i)&&0!==i.length?o("openPicker","cascader",{field:a.item.field,options:i,placeholder:(null==(t=a.item.props)?void 0:t.placeholder)||"请选择"}):e.index.$u.toast("级联选择器配置错误")},P=()=>{var t,i;const r=m.value;if(!r||!Array.isArray(r)||0===r.length)return void e.index.$u.toast("树形选择器配置错误");const l=x(r);o("openPicker","tree",{field:a.item.field,options:l,isMultiple:(null==(t=a.item.props)?void 0:t.showCheckbox)||!1,placeholder:(null==(i=a.item.props)?void 0:i.placeholder)||"请选择"})},S=e=>{const t=Array.isArray(e)?e.filter((e=>null!=e&&""!==e)):[];o("updateField",a.item.field,t)},_=()=>{const t=m.value;if(!t||!Array.isArray(t))return void e.index.$u.toast("选择器配置错误");const i=t.filter((e=>e&&e.label&&void 0!==e.value));0!==i.length?"cascader"===a.item.type||"cascader"===r.value?o("openPicker","cascader",{field:a.item.field,options:i}):o("openPicker","select",{field:a.item.field,options:i}):e.index.$u.toast("选择器选项为空")},B=()=>{o("openPicker","color",{field:a.item.field})},$=()=>{const e=a.item.type;return"datePicker"===e?"date":"timePicker"===e?"time":"date"},z=()=>{const e=a.formData[a.item.field];if(!e){const e=new Date;return"timePicker"===a.item.type?e.toTimeString().split(" ")[0].substring(0,5):e.toISOString().split("T")[0]}return e},T=e=>{const t=a.formData[a.item.field];if(!t||"object"!=typeof t){const e=new Date;return"timePicker"===a.item.type?e.toTimeString().split(" ")[0].substring(0,5):e.toISOString().split("T")[0]}return t[e]||""},q=e=>{const t=a.formData[a.item.field];return t&&"object"==typeof t&&t[e]||""},V=t=>{const i=t.detail.value;o("updateField",a.item.field,i),e.index.$u.toast(`已选择${"datePicker"===a.item.type?"日期":"时间"}: ${i}`)},I=(t,i)=>{const r=t.detail.value,l={...a.formData[a.item.field]||{},[i]:r};o("updateField",a.item.field,l),e.index.$u.toast(`已选择${"start"===i?"开始":"结束"}${"datePicker"===a.item.type?"日期":"时间"}: ${r}`)},O=async t=>{const{file:i}=t,r=a.item.field,l=a.formData[r]||[],p={url:i.url,name:i.name||"文件",size:i.size,type:i.type};o("updateField",r,[...l,p]),e.index.$u.toast("上传成功")},R=e=>{const{index:t}=e,i=a.item.field,r=a.formData[i]||[];r.splice(t,1),o("updateField",i,[...r])};return(i,o)=>{var v,x,U,N,E,H,M,L,G,J,K,Q,W,X,Y,Z,ee,te,ie,ae,oe;return e.e({a:l.value},l.value?{b:e.o(g),c:e.o(g),d:e.o(k),e:e.o(g),f:e.o((e=>t.formData[t.item.field]=e)),g:e.p({placeholder:(null==(v=t.item.props)?void 0:v.placeholder)||{input:"请输入",number:"请输入数字",phone:"请输入手机号",email:"请输入邮箱地址"}[a.item.type]||"请输入",type:s(),maxlength:(null==(x=t.item.props)?void 0:x.maxlength)||c(),clearable:!0,confirmType:"done",adjustPosition:!0,customStyle:"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",modelValue:t.formData[t.item.field]})}:"textarea"===r.value||"textarea"===t.item.type?{i:e.o(g),j:e.o(g),k:e.o(g),l:e.o(g),m:e.o((e=>t.formData[t.item.field]=e)),n:e.p({placeholder:(null==(U=t.item.props)?void 0:U.placeholder)||"请输入",maxlength:(null==(N=t.item.props)?void 0:N.maxlength)||500,height:"120",count:!0,confirmType:"done",adjustPosition:!0,customStyle:"width: 686rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;",modelValue:t.formData[t.item.field]})}:p.value?{p:e.t(f()||(null==(E=t.item.props)?void 0:E.placeholder)||"请选择"),q:f()?"#23232A":"#909399",r:e.o(_)}:"cascader"===r.value?{t:e.t(y()||(null==(H=t.item.props)?void 0:H.placeholder)||"请选择"),v:y()?"#23232A":"#909399",w:e.o(j)}:"tree"===r.value?{y:e.t(h()||(null==(M=t.item.props)?void 0:M.placeholder)||"请选择"),z:h()?"#23232A":"#909399",A:e.o(P)}:"transfer"===r.value?{C:e.t(b().length),D:e.f(b(),((t,i,a)=>({a:t.value,b:"8194d7d4-4-"+a+",8194d7d4-3",c:e.p({name:t.value,label:t.label,customStyle:{marginBottom:"16rpx"}})}))),E:e.o(S),F:e.p({value:t.formData[t.item.field]||[],placement:"column"}),G:e.t((t.formData[t.item.field]||[]).length)}:"radio"===r.value?{I:e.f(m.value,((t,i,a)=>({a:t.value||i,b:"8194d7d4-6-"+a+",8194d7d4-5",c:e.p({name:t.value,label:t.label||`选项${i+1}`,customStyle:{marginRight:"40rpx",marginBottom:"20rpx"}})}))),J:e.o(w),K:e.p({value:t.formData[t.item.field],placement:"row"})}:"checkbox"===r.value?{M:e.f(m.value,((t,i,a)=>({a:t.value,b:"8194d7d4-8-"+a+",8194d7d4-7",c:e.p({name:t.value,label:t.label,customStyle:{marginRight:"40rpx",marginBottom:"20rpx"}})}))),N:e.o(F),O:e.p({value:t.formData[t.item.field],placement:"row"})}:"switch"===r.value?e.e({Q:e.o(A),R:e.o((e=>t.formData[t.item.field]=e)),S:e.p({activeColor:(null==(L=t.item.props)?void 0:L.activeColor)||"#2979ff",inactiveColor:(null==(G=t.item.props)?void 0:G.inactiveColor)||"#ffffff",size:"25",modelValue:t.formData[t.item.field]}),T:null==(J=t.item.props)?void 0:J.desc},(null==(K=t.item.props)?void 0:K.desc)?{U:e.t(t.item.props.desc)}:{}):"rate"===r.value?{W:e.o(D),X:e.o((e=>t.formData[t.item.field]=e)),Y:e.p({count:(null==(Q=t.item.props)?void 0:Q.max)||5,size:(null==(W=t.item.props)?void 0:W.size)||35,allowHalf:(null==(X=t.item.props)?void 0:X.allowHalf)||!1,colors:(null==(Y=t.item.props)?void 0:Y.colors)||["#909399","#909399","#FF9900"],modelValue:t.formData[t.item.field]})}:"slider"===r.value?{aa:e.o(C),ab:e.o((e=>t.formData[t.item.field]=e)),ac:e.p({min:(null==(Z=t.item.props)?void 0:Z.min)||0,max:(null==(ee=t.item.props)?void 0:ee.max)||100,step:(null==(te=t.item.props)?void 0:te.step)||1,showValue:!0,customStyle:"width: 686rpx;",modelValue:t.formData[t.item.field]})}:u.value?e.e({ae:d.value},d.value?{af:e.t(q("start")||("timerange"===a.item.type?"开始时间":"开始日期")),ag:$(),ah:T("start"),ai:e.o((e=>I(e,"start"))),aj:e.t(q("end")||("timerange"===a.item.type?"结束时间":"结束日期")),ak:$(),al:T("end"),am:e.o((e=>I(e,"end")))}:{an:e.t(a.formData[a.item.field]||{date:"请选择日期",time:"请选择时间",datetime:"请选择日期时间"}[a.item.type]||"请选择"),ao:e.t(a.item.type.includes("time")?"clock":"calendar"),ap:$(),aq:z(),ar:e.o(V)}):"color"===r.value?{at:t.formData[t.item.field]||"#ffffff",av:e.o(B),aw:e.o(B),ax:e.p({value:t.formData[t.item.field],placeholder:(null==(ie=t.item.props)?void 0:ie.placeholder)||"请选择颜色",readonly:!0,customStyle:"width: 580rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box; margin-left: 20rpx;"})}:n.value?{az:e.o(O),aA:e.o(R),aB:e.p({fileList:t.formData[t.item.field]||[],maxCount:(null==(ae=t.item.props)?void 0:ae.maxCount)||9,accept:{imageUpload:"image",fileUpload:"file"}[a.item.type]||"all",maxSize:(null==(oe=t.item.props)?void 0:oe.maxSize)||10485760,width:"160",height:"160"})}:{},{h:"textarea"===r.value||"textarea"===t.item.type,o:p.value,s:"cascader"===r.value,x:"tree"===r.value,B:"transfer"===r.value,H:"radio"===r.value,L:"checkbox"===r.value,P:"switch"===r.value,V:"rate"===r.value,Z:"slider"===r.value,ad:u.value,as:"color"===r.value,ay:n.value,aC:e.p({label:t.item.label||t.item.title,prop:t.item.field,required:t.item.required})})}}},i=e._export_sfc(t,[["__scopeId","data-v-8194d7d4"]]);wx.createComponent(i);
