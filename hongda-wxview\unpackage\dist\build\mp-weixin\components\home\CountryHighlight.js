"use strict";const e=require("../../common/vendor.js"),a=require("../../api/content/country.js");if(!Array){(e.resolveComponent("uni-load-more")+e.resolveComponent("uni-icons"))()}Math||((()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js"))();const n={__name:"CountryHighlight",setup(n){const t=e.getCurrentInstance(),o=e.ref(!0),u=e.ref(!1),i=e.ref([]),l=e.ref(null),c=e.ref(null),r=e.ref("basic"),v=e.ref({}),s=e.ref(0);e.ref(null);const d=e.ref(null),_=e.ref([{id:"basic",name:"基本信息",iconKey:"icon_tab_basic_normal",activeIconKey:"icon_tab_basic_active"},{id:"investment",name:"招商政策",iconKey:"icon_tab_investment_normal",activeIconKey:"icon_tab_investment_active"},{id:"customs",name:"海关政策",iconKey:"icon_tab_customs_normal",activeIconKey:"icon_tab_customs_active"},{id:"tax",name:"税务政策",iconKey:"icon_tab_tax_normal",activeIconKey:"icon_tab_tax_active"},{id:"parks",name:"工业园区",iconKey:"icon_tab_parks_normal",activeIconKey:"icon_tab_parks_active"}]),m=e.computed((()=>_.value.map((e=>({id:e.id,name:e.name,icon:v.value[e.iconKey]||"",activeIcon:v.value[e.activeIconKey]||""}))))),b=e.computed((()=>({backgroundImage:v.value.bg_badge_gold?`url('${v.value.bg_badge_gold}')`:"none"}))),g=e.computed((()=>({backgroundImage:v.value.bg_badge_blue?`url('${v.value.bg_badge_blue}')`:"none"}))),p=e.computed((()=>({backgroundImage:v.value.bg_tab_active_home?`url('${v.value.bg_tab_active_home}')`:"none"}))),y=e.computed((()=>{var e,a;return`${(null==(e=c.value)?void 0:e.nameCn)||""} - ${(null==(a=m.value.find((e=>e.id===r.value)))?void 0:a.name)||""}`})),f=e.computed((()=>{if(!c.value)return"<p>暂无相关信息。</p>";return{basic:c.value.introduction,investment:c.value.investmentPolicy,customs:c.value.customsPolicy,tax:c.value.taxPolicy,parks:"<p>请点击“更多”查看详细的工业园区列表。</p>"}[r.value]||"<p>暂无相关信息。</p>"})),h=async e=>{u.value=!0,c.value=null;try{const n=await a.getCountryDetail(e);c.value=n.data}catch(n){console.error(`获取ID为 ${e} 的国别详情失败:`,n)}finally{u.value=!1}},I=()=>{l.value&&e.index.navigateTo({url:`/pages_sub/pages_country/detail?id=${l.value}&tab=${r.value}`})};return e.onMounted((()=>{v.value=e.index.getStorageSync("staticAssets")||{},(async()=>{o.value=!0;try{const e={pageNum:1,pageSize:5},n=await a.getCountryList(e);if(n.data&&n.data.length>0){i.value=n.data;const e=n.data[0].id;l.value=e,await h(e)}}catch(e){console.error("获取推荐国别失败:",e)}finally{o.value=!1}})()})),(a,n)=>e.e({a:!o.value&&i.value.length>0},!o.value&&i.value.length>0?e.e({b:e.f(i.value,((a,n,o)=>{return{a:a.listCoverUrl,b:e.t((u=a.nameCn,u?u.length>3?u.substring(0,3)+"...":u:"")),c:l.value===a.id?1:"",d:e.s(l.value===a.id?b.value:g.value),e:a.id,f:"country-card-"+a.id,g:l.value===a.id?1:"",h:e.o((n=>{return o=a.id,d.value&&(clearTimeout(d.value),d.value=null),void(l.value!==o?(l.value=o,r.value="basic",h(o),e.nextTick$1((()=>{const a=e.index.createSelectorQuery().in(t);a.select(".country-selector-inner").boundingClientRect(),a.select(`#country-card-${o}`).boundingClientRect(),a.exec((e=>{if(e&&e[0]&&e[1]){const a=e[0],n=e[1].left-a.left;s.value=n}}))}))):e.index.navigateTo({url:`/pages_sub/pages_country/detail?id=${o}&tab=${r.value}`}));var o}),a.id)};var u})),c:s.value,d:e.f(m.value,((a,n,t)=>({a:r.value===a.id?a.activeIcon:a.icon,b:e.t(a.name),c:a.id,d:r.value===a.id?1:"",e:e.o((e=>{return n=a.id,void(r.value=n);var n}),a.id),f:e.s(r.value===a.id?p.value:{})}))),e:u.value},u.value?{f:e.p({status:"loading"})}:c.value?{h:e.t(y.value),i:e.p({type:"right",size:"14",color:"#888"}),j:e.o(I),k:f.value}:{},{g:c.value}):{})}},t=e._export_sfc(n,[["__scopeId","data-v-0956782b"]]);wx.createComponent(t);
