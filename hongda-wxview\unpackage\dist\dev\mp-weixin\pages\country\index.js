"use strict";
const common_vendor = require("../../common/vendor.js");
const api_content_country = require("../../api/content/country.js");
const utils_config = require("../../utils/config.js");
const utils_tools = require("../../utils/tools.js");
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  _easycom_uni_load_more2();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
if (!Math) {
  (_easycom_uni_load_more + CustomTabBar)();
}
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const headerBgUrl = common_vendor.computed(() => assets.value.bg_country_list_header || "");
    const activeTabBgUrl = common_vendor.computed(() => assets.value.bg_country_list_active_tab || "");
    const dynamicIconStyle = common_vendor.computed(() => {
      const searchIconUrl = assets.value.icon_common_search;
      if (searchIconUrl) {
        return {
          "--search-icon-url": `url(${searchIconUrl})`
        };
      }
      return {};
    });
    const baseUrl = utils_config.IMAGE_BASE_URL;
    const searchKeyword = common_vendor.ref("");
    const activeContinent = common_vendor.ref("ALL");
    const countryList = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const instance = common_vendor.getCurrentInstance();
    const assets = common_vendor.ref(common_vendor.index.getStorageSync("staticAssets") || {});
    const continents = [
      { label: "全部", value: "ALL" },
      { label: "亚洲", value: "ASIA" },
      { label: "欧洲", value: "EUROPE" },
      { label: "北美洲", value: "NORTH_AMERICA" },
      { label: "南美洲", value: "SOUTH_AMERICA" },
      { label: "非洲", value: "AFRICA" },
      { label: "大洋洲", value: "OCEANIA" }
    ];
    const activeIndex = common_vendor.ref(0);
    const tabsInfo = common_vendor.ref([]);
    const currentScrollLeft = common_vendor.ref(0);
    const showIndicator = common_vendor.ref(false);
    const tabScrollLeft = common_vendor.ref(0);
    const tabsContainerWidth = common_vendor.ref(0);
    const indicatorLeft = common_vendor.computed(() => {
      if (!tabsInfo.value.length || activeIndex.value >= tabsInfo.value.length) {
        return -999;
      }
      const activeTab = tabsInfo.value[activeIndex.value];
      if (!activeTab)
        return -999;
      const indicatorHalfWidth = common_vendor.index.upx2px(20);
      const tabCenterX = activeTab.left + activeTab.width / 2;
      const left = tabCenterX - currentScrollLeft.value - indicatorHalfWidth;
      return left;
    });
    const onTabsScroll = (event) => {
      currentScrollLeft.value = event.detail.scrollLeft;
    };
    const getTabsRect = () => {
      return new Promise((resolve) => {
        const query = common_vendor.index.createSelectorQuery().in(instance);
        query.select(".continent-tabs").boundingClientRect();
        query.selectAll(".tab-item").boundingClientRect();
        query.exec((res) => {
          if (res && res[1] && res[1].length) {
            const containerRect = res[0];
            const tabRects = res[1];
            tabsContainerWidth.value = containerRect ? containerRect.width : 0;
            tabsInfo.value = tabRects;
            showIndicator.value = true;
            resolve(tabRects);
          } else {
            resolve([]);
          }
        });
      });
    };
    const scrollToActiveTab = () => {
      var _a;
      if (!tabsInfo.value.length || activeIndex.value >= tabsInfo.value.length) {
        return;
      }
      const activeTab = tabsInfo.value[activeIndex.value];
      const containerWidth = tabsContainerWidth.value;
      if (!activeTab || !containerWidth)
        return;
      const tabCenter = activeTab.left + activeTab.width / 2;
      const containerCenter = containerWidth / 2;
      let scrollTo = tabCenter - containerCenter;
      const maxScroll = Math.max(0, ((_a = activeTab.dataset) == null ? void 0 : _a.scrollWidth) - containerWidth || 0);
      scrollTo = Math.max(0, Math.min(scrollTo, maxScroll));
      tabScrollLeft.value = scrollTo;
    };
    const onFilterTap = async (continentValue, index) => {
      if (activeContinent.value === continentValue)
        return;
      activeIndex.value;
      activeContinent.value = continentValue;
      activeIndex.value = index;
      if (!tabsInfo.value.length) {
        await getTabsRect();
      }
      common_vendor.nextTick$1(() => {
        scrollToActiveTab();
      });
      fetchData(true);
    };
    const initTabsLayout = async () => {
      await common_vendor.nextTick$1();
      let retryCount = 0;
      const maxRetries = 5;
      const tryGetLayout = async () => {
        const rects = await getTabsRect();
        if (rects.length === 0 && retryCount < maxRetries) {
          retryCount++;
          setTimeout(tryGetLayout, 100);
        } else if (rects.length > 0) {
          scrollToActiveTab();
        }
      };
      tryGetLayout();
    };
    const fetchData = async (isRefresh = false) => {
      if (loading.value)
        return;
      loading.value = true;
      try {
        const res = await api_content_country.getCountryList({
          continent: activeContinent.value,
          keyword: searchKeyword.value
        });
        countryList.value = isRefresh ? res.data : [...countryList.value, ...res.data];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/country/index.vue:278", "获取国别列表失败:", error);
        common_vendor.index.showToast({ title: "数据加载失败", icon: "none" });
      } finally {
        loading.value = false;
        if (isRefresh) {
          common_vendor.index.stopPullDownRefresh();
        }
      }
    };
    const debouncedSearch = utils_tools.debounce(() => {
      fetchData(true);
    }, 500);
    const onSearchInput = () => {
      if (!searchKeyword.value.trim()) {
        fetchData(true);
      } else {
        debouncedSearch();
      }
    };
    const onSearch = () => {
      fetchData(true);
    };
    const goToDetail = (countryId) => {
      common_vendor.index.navigateTo({ url: `/pages_sub/pages_country/detail?id=${countryId}` });
    };
    const loadMore = () => {
    };
    common_vendor.onLoad(() => {
      common_vendor.index.__f__("log", "at pages/country/index.vue:330", "国别列表页 onLoad - 页面首次加载");
      common_vendor.index.showShareMenu({
        menus: ["shareAppMessage", "shareTimeline"]
      });
      fetchData(true);
      setTimeout(initTabsLayout, 200);
    });
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
      setTimeout(initTabsLayout, 100);
    });
    common_vendor.onPullDownRefresh(() => {
      fetchData(true);
    });
    common_vendor.onShareAppMessage(() => {
      return {};
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(onSearch),
        b: common_vendor.o([($event) => searchKeyword.value = $event.detail.value, onSearchInput]),
        c: searchKeyword.value,
        d: common_vendor.o(onSearch),
        e: common_vendor.s(dynamicIconStyle.value),
        f: common_vendor.f(continents, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.label),
            b: tab.value,
            c: common_vendor.n({
              active: activeContinent.value === tab.value
            }),
            d: common_vendor.o(($event) => onFilterTap(tab.value, index), tab.value),
            e: "tab-" + index,
            f: activeContinent.value === tab.value ? `url(${activeTabBgUrl.value})` : "none"
          };
        }),
        g: common_vendor.o(onTabsScroll),
        h: tabScrollLeft.value,
        i: indicatorLeft.value + "px",
        j: showIndicator.value ? 1 : 0,
        k: `url(${headerBgUrl.value})`,
        l: common_vendor.f(countryList.value, (item, k0, i0) => {
          return {
            a: common_vendor.unref(baseUrl) + item.listCoverUrl,
            b: common_vendor.t(item.nameCn),
            c: common_vendor.t(item.nameEn),
            d: common_vendor.t(item.summary),
            e: common_vendor.unref(baseUrl) + item.flagUrl,
            f: item.id,
            g: common_vendor.o(($event) => goToDetail(item.id), item.id)
          };
        }),
        m: loading.value
      }, loading.value ? {
        n: common_vendor.p({
          status: "loading"
        })
      } : countryList.value.length === 0 ? {} : {}, {
        o: countryList.value.length === 0,
        p: common_vendor.o(loadMore),
        q: common_vendor.p({
          current: 3
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1e6eaa19"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/country/index.js.map
